{"id": 314, "name": "SliverPrototypeExtentList", "localName": "Sliver 프로토타입 확장 리스트", "info": "prototypeItem 속성은 Widget이며, 주축 방향의 item 크기를 제한하지만 표시되지는 않습니다. delegate는 SliverChildDelegate를 받아 item 생성을 완료합니다.", "lever": 2, "family": 4, "linkIds": [185, 186], "nodes": [{"file": "node1_base.dart", "name": "SliverPrototypeExtentList 기본 사용", "desc": ["【prototypeItem】 : 주축 방향 크기 컴포넌트   【Widget】", "【delegate】 : 자식 대리자   【SliverChildDelegate】"]}]}
{"id": 306, "name": "SliverFillRemaining", "localName": "Sliver riempie il rimanente", "info": "Un sliver che contiene un singolo elemento figlio box, che riempie lo spazio rimanente nella finestra. È possibile specificare due valori bool per controllare l'effetto di scorrimento, come nell'esempio seguente, da provare personalmente.", "lever": 4, "family": 4, "linkIds": [183, 251], "nodes": [{"file": "node1_base.dart", "name": "Uso di base di SliverFillRemaining", "desc": ["【hasScrollBody】 : Se ha un corpo di scorrimento   【bool】", "【fillOverscroll】 : Se può riempire l'area di scorrimento   【bool】", "【child】 : Componente figlio   【Widget】"]}]}
{"id": 183, "name": "CustomScrollView", "localName": "Vista de desplazamiento universal", "info": "Una estructura de desplazamiento universal que puede especificar la dirección de desplazamiento, si es inverso, el controlador de desplazamiento y otras propiedades. Los componentes secundarios que contiene deben ser de la familia Sliver.", "lever": 5, "family": 4, "linkIds": [184, 185, 188], "nodes": [{"file": "node1_base.dart", "name": "Uso básico de CustomScrollView", "desc": ["【slivers】 : Lista de componentes secundarios   【List<Widget>】", "【reverse】 : Si es inverso   【bool】", "【scrollDirection】 : Dirección de desplazamiento   【Axis】", "【controller】 : Controlador   【ScrollController】"]}]}
{"id": 184, "name": "SliverAppBar", "localName": "<PERSON><PERSON> d'en-tê<PERSON>ver", "info": "Structure générique de la barre supérieure de la famille Sliver, qui peut spécifier des composants gauche, centre et droit, un espace de rétrécissement, une profondeur d'ombre, un mode fixe, une couleur d'arrière-plan, etc.", "lever": 4, "family": 4, "linkIds": [183, 196], "nodes": [{"file": "node1_base.dart", "name": "Utilisation de base de SliverAppBar", "desc": ["【leading】 : Composant gauche   【Widget】", "【title】 : Composant central   【Widget】", "【actions】 : Liste des composants de fin   【List<Widget>】", "【floating】 : Flottant ou non   【bool】", "【pinned】 : Reste en haut ou non   【bool】", "【snap】 : Semi-déployé ou non   【bool】", "【bottom】 : Composant inférieur   【PreferredSizeWidget】", "【expandedHeight】 : <PERSON><PERSON> étendue   【double】", "【elevation】 : Profondeur d'ombre   【double】", "【flexibleSpace】 : Espace étendu   【FlexibleSpaceBar】", "【backgroundColor】 : <PERSON>uleur d'arrière-plan   【Color】", "【controller】 : Contrôleur   【ScrollController】", "   snap doit être true lorsque floating est true"]}]}
{"id": 303, "name": "CupertinoSliverRefreshControl", "localName": "Sliver Aktualisierungssteuerung", "info": "iOS-Stil Pull-to-Refresh-Steuerung, die asynchrone Aktualisierungsmethoden, benutzerdefinierte Steuerungskomponenten, die Höhe des Indikators und die Höhe des Auslösens des Ladens durchziehen kann.", "lever": 4, "family": 4, "linkIds": [183, 251], "nodes": [{"file": "node1_base.dart", "name": "Grundlegende Verwendung der Aktualisierungssteuerung", "desc": ["【refreshIndicatorExtent】 : Höhe des Ladeindikators   【double】", "【refreshTriggerPullDistance】 : Höhe des Auslösens des Ladens durchziehen   【double】", "【onRefresh】 : Pull-to-Refresh-Ereignis   【RefreshCallback】", "【builder】 : Indikator-Builder   【RefreshControlIndicatorBuilder】"]}]}
{"id": 309, "name": "PinnedHeaderSliver", "localName": "Cabeçalho <PERSON>", "info": "Um painel deslizante que pode permanecer na parte superior da viewport, facilitando o efeito de fixação no topo.", "lever": 4, "family": 4, "linkIds": [190], "nodes": [{"file": "node_01.dart", "name": "Exemplo Oficial do PinnedHeaderSliver", "display": "new_page", "desc": ["【child】 : Component<PERSON> filho   【Widget?】", "O PinnedHeaderSliver só pode ser usado em viewports de rolagem, onde o componente filho não sai da viewport ao rolar para o topo, permanecendo assim na parte superior da viewport."]}, {"file": "node_02.dart", "name": "<PERSON><PERSON><PERSON><PERSON> no <PERSON>o", "display": "new_page", "desc": ["Efeito de título fixo no topo baseado no PinnedHeaderSliver, observe que a linha divisória abaixo também tem o efeito de fixação no topo."]}, {"file": "node_03.dart", "name": "Título + Barra de Pesquisa Fixa no Topo", "display": "new_page", "desc": ["Efeito de título + barra de pesquisa fixa no topo baseado no PinnedHeaderSliver."]}]}
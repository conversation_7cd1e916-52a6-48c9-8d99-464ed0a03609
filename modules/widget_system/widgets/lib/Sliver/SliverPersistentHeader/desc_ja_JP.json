{"id": 190, "name": "SliverPersistentHeader", "localName": "吸頂スライド", "info": "通常、CustomScrollViewで使用され、コンポーネントがスクロール中に上部に留まり、スクロールして消えないようにします。", "lever": 5, "family": 4, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "SliverPersistentHeaderの基本的な使用法", "desc": ["【delegate】 : デリゲート   【SliverPersistentHeaderDelegate】", "【floating】 : フローティングするかどうか   【bool】", "【pinned】 : 上部に留まるかどうか   【bool】"]}]}
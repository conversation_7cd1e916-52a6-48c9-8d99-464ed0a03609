{"id": 333, "name": "TooltipTheme", "localName": "Thème d'infobulle", "info": "Principalement utilisé pour définir les propriétés par défaut des composants Tooltip pour les descendants. Il est également possible d'obtenir les propriétés du TooltipTheme par défaut via ce composant.", "lever": 2, "family": 5, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "Utilisation de base de TooltipTheme", "desc": ["Vous pouvez spécifier les propriétés de TooltipThemeData pour définir le style par défaut des composants Tooltip pour les descendants, comme la décoration, le style du texte, la durée d'affichage, les marges, etc. Vous pouvez également utiliser TooltipTheme.of pour obtenir les propriétés du thème Tooltip."]}]}
{"id": 333, "name": "TooltipTheme", "localName": "Tema del Tooltip", "info": "Utilizzato principalmente per impostare le proprietà predefinite per i componenti Tooltip discendenti. È anche possibile ottenere le proprietà del TooltipTheme predefinito tramite questo componente.", "lever": 2, "family": 5, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "Uso di base di TooltipTheme", "desc": ["È possibile specificare le proprietà dei dati di TooltipThemeData per impostare lo stile predefinito per i componenti Tooltip discendenti, come decorazioni, stili di testo, durata di visualizzazione, margini, ecc. È anche possibile utilizzare TooltipTheme.of per ottenere le proprietà del tema del Tooltip."]}]}
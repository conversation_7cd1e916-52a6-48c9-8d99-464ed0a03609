{"id": 316, "name": "KeepAlive", "localName": "Mantener activo", "info": "En listas de carga perezosa, si el estado de los hijos necesita mantenerse activo. Es la implementación subyacente de AutomaticKeepAlive, generalmente no se usa por separado.", "lever": 1, "family": 5, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "Introducción a KeepAlive", "desc": ["【child】 : *Componente hijo   【Widget】", "【keepAlive】 : *Si se mantiene activo   【bool】", "En la capa del framework de Flutter, solo se usa en AutomaticKeepAlive, y en el código fuente también se menciona que rara vez se usa por separado. Este ejemplo muestra cómo mantener activo el estado de los elementos de ListView."]}]}
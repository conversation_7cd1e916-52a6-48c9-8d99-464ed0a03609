{"id": 17, "name": "CheckboxListTile", "localName": "<PERSON><PERSON> de case à cocher", "info": "Une structure de liste générique fournie par Flutter, de structure gauche-centre, avec une case à cocher à la fin. Des composants peuvent être insérés aux positions correspondantes, ce qui permet de répondre facilement à des entrées spécifiques.", "lever": 3, "family": 0, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "La performance de base de CheckBoxListTile est la suivante", "desc": ["【secondary】: Composant gauche   【Widget】", "【checkColor】: <PERSON><PERSON><PERSON> de la coche   【Color】", "【activeColor】: <PERSON><PERSON><PERSON> de la bordure lors de la sélection   【Color】", "【title】: Composant supérieur central   【Widget】", "【subtitle】: Composant inférieur central   【Widget】", "【onChanged】: Événement de sélection   【Function(bool)】"]}, {"file": "node2_select.dart", "name": "Effet de sélection de CheckBoxListTile", "desc": ["【selected】: Est sélectionné   【bool】"]}, {"file": "node3_dense.dart", "name": "Propriété de densité de CheckBoxListTile", "desc": ["【dense】: Est dense   【bool】"]}]}
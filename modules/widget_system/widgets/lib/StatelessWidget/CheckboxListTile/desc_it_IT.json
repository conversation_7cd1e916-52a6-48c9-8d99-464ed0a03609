{"id": 17, "name": "CheckboxListTile", "localName": "Piastrella di selezione", "info": "Una struttura di elenco generica fornita da Flutter, con una struttura sinistra-centro, e una casella di controllo alla fine. I componenti possono essere inseriti nelle posizioni corrispondenti, rendendo facile affrontare voci specifiche.", "lever": 3, "family": 0, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "La rappresentazione di base di CheckBoxListTile è la seguente", "desc": ["【secondary】: Componente sinistro   【Widget】", "【checkColor】: Colore del segno di spunta   【Color】", "【activeColor】: Colore del bordo quando selezionato   【Color】", "【title】: Componente superiore centrale   【Widget】", "【subtitle】: Componente inferiore centrale   【Widget】", "【onChanged】: Evento di selezione   【Function(bool)】"]}, {"file": "node2_select.dart", "name": "Effetto di selezione di CheckBoxListTile", "desc": ["【selected】: Se selezionato   【bool】"]}, {"file": "node3_dense.dart", "name": "Proprietà di disposizione compatta di CheckBoxListTile", "desc": ["【dense】: Se disposto in modo compatto   【bool】"]}]}
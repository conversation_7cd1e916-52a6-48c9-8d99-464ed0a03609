{"id": 17, "name": "CheckboxListTile", "localName": "Флажок плитки", "info": "Универсальная структура элемента списка, предоставляемая Flutter, имеет лево-центральную структуру, а в конце находится CheckBox. Компоненты могут быть вставлены в соответствующие позиции, что позволяет легко адаптироваться к конкретным элементам.", "lever": 3, "family": 0, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "Основное представление CheckBoxListTile выглядит следующим образом", "desc": ["【secondary】: Левый компонент   【Widget】", "【checkColor】: Цвет ✔️   【Color】", "【activeColor】: Цвет рамки при выборе   【Color】", "【title】: Верхний центральный компонент   【Widget】", "【subtitle】: Нижний центральный компонент   【Widget】", "【onChanged】: Событие выбора   【Function(bool)】"]}, {"file": "node2_select.dart", "name": "Эффект выбора CheckBoxListTile", "desc": ["【selected】: Выбрано ли   【bool】"]}, {"file": "node3_dense.dart", "name": "Свойство плотного расположения CheckBoxListTile", "desc": ["【dense】: Плотное ли расположение   【bool】"]}]}
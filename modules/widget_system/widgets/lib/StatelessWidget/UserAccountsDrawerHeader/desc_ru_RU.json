{"id": 22, "name": "UserAccountsDrawerHeader", "localName": "Заголовок отображения", "info": "Универсальная структура отображения, предоставляемая Flutter, в которую можно вставлять компоненты в соответствующие места, что позволяет легко адаптироваться к конкретным элементам, часто используется в Drawer.", "lever": 3, "family": 0, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "Основное представление компонента", "desc": ["【currentAccountPicture】: Верхний компонент   【Widget】", "【accountName】: Средний компонент   【Widget】", "【accountEmail】: Нижний компонент   【Widget】", "【decoration】: Декорация   【Decoration】"]}, {"file": "node2_pro.dart", "name": "Верхний правый угол и нижняя часть", "desc": ["【otherAccountsPictures】: Верхний правый компонент   【List<Widget>】", "【onDetailsPressed】: Событие нажатия в нижнем правом углу   【Function()】", "【arrowColor】: Цвет кнопки в нижнем правом углу   【Color】", "【margin】: Вне<PERSON>ний отступ   【EdgeInsetsGeometry】"]}]}
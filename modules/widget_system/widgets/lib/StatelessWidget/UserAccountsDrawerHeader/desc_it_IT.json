{"id": 22, "name": "UserAccountsDrawerHeader", "localName": "Intestazione del cassetto degli account utente", "info": "Una struttura di visualizzazione generica fornita da Flutter, in cui è possibile inserire componenti nelle posizioni corrispondenti, utile per gestire voci specifiche, comunemente utilizzata nei cassetti.", "lever": 3, "family": 0, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "La rappresentazione di base di questo componente è la seguente", "desc": ["【currentAccountPicture】: componente superiore   【Widget】", "【accountName】: componente centrale   【Widget】", "【accountEmail】: componente inferiore   【Widget】", "【decoration】: decorazione   【Decoration】"]}, {"file": "node2_pro.dart", "name": "Angolo superiore destro e parte inferiore", "desc": ["【otherAccountsPictures】: componente superiore destro   【List<Widget>】", "【onDetailsPressed】: evento di clic nell'angolo inferiore destro   【Function()】", "【arrowColor】: colore del pulsante nell'angolo inferiore destro   【Color】", "【margin】: margine esterno   【EdgeInsetsGeometry】"]}]}
{"id": 19, "name": "RadioListTile", "localName": "Azulejo de Rádio", "info": "Uma estrutura de item de lista genérica fornecida pelo Flutter, com uma estrutura de centro-direita, e um Rádio na extremidade. Componentes podem ser inseridos nas posições correspondentes, o que permite lidar facilmente com itens específicos.", "lever": 3, "family": 0, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "RadioListTile requer um tipo genérico T", "desc": ["【value】 : objeto do item  【T】", "【groupValue】 : objeto selecionado  【T】", "【selected】: está selecionado   【bool】", "【secondary】: componente à direita   【Widget】", "【title】: componente superior no centro   【Widget】", "【subtitle】: componente inferior no centro   【Widget】", "【onChanged】: evento de alternância   【Function(T)】"]}, {"file": "node2_dense.dart", "name": "Cor de seleção e disposição densa do RadioListTile", "desc": ["【activeColor】 : cor quando selecionado  【Color】", "【dense】: disposi<PERSON> densa   【bool】"]}]}
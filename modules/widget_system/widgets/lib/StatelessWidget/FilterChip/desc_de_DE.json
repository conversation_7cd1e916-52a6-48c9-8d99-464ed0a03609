{"id": 15, "name": "FilterChip", "localName": "Filterstreifen", "info": "<PERSON><PERSON><PERSON> wie die Chip-Komponente, hat Eigenschaften für ausgewählt oder nicht und ein Auswahlereignis. Wenn ausgewählt, wird die obere Ebene der linken Komponente mit einem ✔️ überlagert.", "lever": 4, "family": 0, "linkIds": [11, 12, 13, 14, 153], "nodes": [{"file": "node1_base.dart", "name": "FilterChip kann Auswahlereignisse akzeptieren", "desc": ["【selected】: Ob ausgewählt   【bool】", "【onSelected】: Auswahlereignis   【Function(bool)】", "【selectedColor】: Farbe nach der Auswahl   【Color】", "【selectedShadowColor】: Schattenfarbe nach der Auswahl   【Color】,"]}]}
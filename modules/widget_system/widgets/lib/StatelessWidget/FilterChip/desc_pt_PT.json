{"id": 15, "name": "FilterChip", "localName": "Filtro de Chip", "info": "Estilo semelhante ao componente Chip, com propriedades de seleção e eventos de seleção. Quando selecionado, a camada superior do componente à esquerda será coberta por um ✔️.", "lever": 4, "family": 0, "linkIds": [11, 12, 13, 14, 153], "nodes": [{"file": "node1_base.dart", "name": "FilterChip pode aceitar eventos de seleção", "desc": ["【selected】: Se selecionado   【bool】", "【onSelected】: Evento de seleção   【Function(bool)】", "【selectedColor】: Cor após seleção   【Color】", "【selectedShadowColor】: <PERSON><PERSON> da sombra após seleção   【Color】,"]}]}
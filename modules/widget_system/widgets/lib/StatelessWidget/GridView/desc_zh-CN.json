{"id": 163, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "localName": "网格组件", "info": "容纳多个组件，并以网格的方式。可以通过count、extent、custom、builder等构造。有内边距、是否反向、滑动控制器等属性。", "lever": 5, "family": 0, "linkIds": [21, 162], "nodes": [{"file": "node1_base.dart", "name": "GridView.count构造", "desc": ["【children】 : 子组件列表   【List<Widget>】", "【crossAxisCount】 : 主轴一行box数量  【int】", "【mainAxisSpacing】 : 主轴每行间距  【double】", "【crossAxisSpacing】 : 交叉轴每行间距  【double】", "【childAspectRatio】 : box主长/交叉轴长  【double】", "【crossAxisCount】 : 主轴一行数量  【int】"]}, {"file": "node2_direction.dart", "name": "GridView滑动方向", "desc": ["【scrollDirection】 : 滑动方向   【Axis】", "【reverse】 : 是否反向滑动   【bool】", "【shrinkWrap】 : 无边界时是否包裹  【bool】"]}, {"file": "node3_extend.dart", "name": "GridView滑动方向", "desc": ["【scrollDirection】 : 滑动方向   【Axis】", "【reverse】 : 是否反向滑动   【bool】", "【shrinkWrap】 : 无边界时是否包裹  【bool】"]}, {"file": "node4_builder.dart", "name": "GridView.builder构造", "desc": ["【itemCount】 : 条目数量   【int】", "【gridDelegate】 : 网格代理   【SliverGridDelegate】", "【itemBuilder】 : 条目构造器  【IndexedWidgetBuilder】"]}]}
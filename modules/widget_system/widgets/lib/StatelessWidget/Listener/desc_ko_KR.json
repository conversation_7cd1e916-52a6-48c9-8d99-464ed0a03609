{"id": 147, "name": "Listener", "localName": "이벤트 리스너", "info": "컴포넌트 이벤트의 리스너로, 누름, 놓음, 이동, 취소 등의 이벤트를 수신할 수 있습니다. GestureDetector보다 원시적이며, 더 많은 정보를 얻을 수 있습니다.", "lever": 3, "family": 0, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "Listener 기본 이벤트", "desc": ["【child】 : 자식 컴포넌트   【Widget】", "【onPointerDown】 : 누름 이벤트   【Function(PointerDownEvent)】", "【onPointerMove】 : 이동 이벤트   【Function(PointerMoveEvent)】", "【onPointerMove】 : 놓음 이벤트   【Function(PointerUpEvent)】", "【onPointerCancel】 : 취소 이벤트   【Function(PointerUpEvent)】"]}]}
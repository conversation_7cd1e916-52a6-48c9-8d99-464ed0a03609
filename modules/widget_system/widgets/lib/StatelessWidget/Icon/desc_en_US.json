{"id": 6, "name": "Icon", "localName": "Icon Component", "info": "Component for displaying icons. Can specify icon resources, size, and color. Very simple, but very useful", "lever": 2, "family": 0, "linkIds": [7, 30, 125], "nodes": [{"file": "node1_base.dart", "name": "Used to display an icon", "desc": ["【Input】 : Icon data 【IconData】", "【size】 : Size  【double】", "【color】: Color   【Color】"]}, {"file": "node2_diy.dart", "name": "Use custom icons", "desc": ["You can download icon fonts from the iconfont website for use"]}]}
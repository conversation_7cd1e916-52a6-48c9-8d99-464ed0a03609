{"id": 203, "name": "OrientationBuilder", "localName": "Costruttore di Orientamento", "info": "Può richiamare se il componente genitore è orizzontale o verticale, e può costruire diversi componenti figli in base a ciò.", "lever": 2, "family": 0, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "Uso di base di OrientationBuilder", "desc": ["【builder】 : Costruttore del componente di orientamento   【OrientationWidgetBuilder】"]}]}
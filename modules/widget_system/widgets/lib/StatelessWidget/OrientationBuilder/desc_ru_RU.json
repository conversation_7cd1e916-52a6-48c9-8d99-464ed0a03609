{"id": 203, "name": "OrientationBuilder", "localName": "Ориентация строителя", "info": "Может обратно вызывать, является ли родительский компонент горизонтальным или вертикальным, и может создавать различные дочерние компоненты на основе этого.", "lever": 2, "family": 0, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "Основное использование OrientationBuilder", "desc": ["【builder】 : Строитель компонента ориентации   【OrientationWidgetBuilder】"]}]}
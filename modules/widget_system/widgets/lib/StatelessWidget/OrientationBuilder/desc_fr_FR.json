{"id": 203, "name": "OrientationBuilder", "localName": "Constructeur d'orientation", "info": "Capable de rappeler si le composant parent est horizontal ou vertical, et peut construire différents composants enfants en conséquence.", "lever": 2, "family": 0, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "Utilisation de base d'OrientationBuilder", "desc": ["【builder】 : Constructeur de composant d'orientation   【OrientationWidgetBuilder】"]}]}
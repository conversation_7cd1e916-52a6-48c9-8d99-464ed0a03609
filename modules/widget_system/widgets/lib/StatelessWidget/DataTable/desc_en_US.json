{"id": 102, "name": "DataTable", "localName": "Data Table", "info": "A table component that can be customized with logic for clicking, editing, sorting, etc.", "lever": 3, "family": 0, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "Basic Usage of DataTable", "desc": ["【columns】 : columns   【List<DataColumn>】", "【rows】 : rows  【List<DataRow>】"]}, {"file": "node2_operation.dart", "name": "Sorting in DataTable", "desc": ["【sortColumnIndex】 : column index   【int】", "【columnSpacing】 : column spacing   【double】", "【sortAscending】 : ascending order  【bool】"]}]}
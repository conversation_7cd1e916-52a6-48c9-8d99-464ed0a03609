{"id": 102, "name": "DataTable", "localName": "Date<PERSON>bell<PERSON>", "info": "<PERSON><PERSON> Tabellenkomponente, die Logik für Klicken, <PERSON><PERSON><PERSON>, Sortieren usw. festlegen kann.", "lever": 3, "family": 0, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "Grundlegende Verwendung von DataTable", "desc": ["【columns】 : Spalten   【List<DataColumn>】", "【rows】 : Zeilen  【List<DataRow>】"]}, {"file": "node2_operation.dart", "name": "Sortierung von DataTable", "desc": ["【sortColumnIndex】 : Spaltennummer   【int】", "【columnSpacing】 : Spaltenabstand   【double】", "【sortAscending】 : Aufsteigend sortieren  【bool】"]}]}
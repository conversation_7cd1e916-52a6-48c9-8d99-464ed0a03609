{"id": 102, "name": "DataTable", "localName": "Tabela de Dados", "info": "Um componente de tabela que pode definir lógicas para cliques, modificações, ordenação e outras operações.", "lever": 3, "family": 0, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "Uso Básico do DataTable", "desc": ["【columns】 : Colunas   【List<DataColumn>】", "【rows】 : Linhas  【List<DataRow>】"]}, {"file": "node2_operation.dart", "name": "Ordenação do DataTable", "desc": ["【sortColumnIndex】 : Índice da Coluna   【int】", "【columnSpacing】 : Espaçamento entre Colunas   【double】", "【sortAscending】 : Ordem Ascendente  【bool】"]}]}
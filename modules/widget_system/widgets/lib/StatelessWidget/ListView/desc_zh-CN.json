{"id": 162, "name": "ListView", "localName": "列表组件", "info": "列表显示的领军人物，容纳多个子组件，可以通过builder、separated、custom等构造。有内边距、是否反向、滑动控制器等属性。", "lever": 5, "family": 0, "linkIds": [16, 163], "nodes": [{"file": "node1_base.dart", "name": "ListView基本使用", "desc": ["【children】 : 子组件列表   【List<Widget>】", "【padding】 : 内边距  【EdgeInsetsGeometry】"]}, {"file": "node2_direction.dart", "name": "ListView横向滑动", "desc": ["【scrollDirection】 : 滑动方向   【Axis】", "【reverse】 : 是否反向滑动   【bool】", "【shrinkWrap】 : 无边界时是否包裹  【bool】"]}, {"file": "node3_builder.dart", "name": "ListView.builder构造", "desc": ["【itemCount】 : 条目个数   【int】", "【itemBuilder】 : 条目构造器   【IndexedWidgetBuilder】"]}, {"file": "node4_separated.dart", "name": "ListView.separated构造", "desc": ["【separatorBuilder】 : 条目构造器   【IndexedWidgetBuilder】"]}]}
{"id": 162, "name": "ListView", "localName": "Composant de liste", "info": "Le leader de l'affichage de listes, contenant plusieurs sous-composants, peut être construit via builder, separated, custom, etc. Possède des attributs tels que le padding, la direction inverse, le contrôleur de défilement, etc.", "lever": 5, "family": 0, "linkIds": [16, 163], "nodes": [{"file": "node1_base.dart", "name": "Utilisation de base de ListView", "desc": ["【children】 : Liste des sous-composants   【List<Widget>】", "【padding】 : Padding   【EdgeInsetsGeometry】"]}, {"file": "node2_direction.dart", "name": "Défilement horizontal de ListView", "desc": ["【scrollDirection】 : Direction de défilement   【Axis】", "【reverse】 : Défilement inverse   【bool】", "【shrinkWrap】 : Enveloppe sans limite   【bool】"]}, {"file": "node3_builder.dart", "name": "Construction de ListView.builder", "desc": ["【itemCount】 : Nombre d'éléments   【int】", "【itemBuilder】 : Constructeur d'éléments   【IndexedWidgetBuilder】"]}, {"file": "node4_separated.dart", "name": "Construction de ListView.separated", "desc": ["【separatorBuilder】 : Constructeur d'éléments   【IndexedWidgetBuilder】"]}]}
{"id": 162, "name": "ListView", "localName": "리스트 컴포넌트", "info": "리스트 표시의 선두주자로, 여러 하위 컴포넌트를 수용할 수 있으며 builder, separated, custom 등을 통해 구성할 수 있습니다. 내부 여백, 반대 방향 여부, 스크롤 컨트롤러 등의 속성이 있습니다.", "lever": 5, "family": 0, "linkIds": [16, 163], "nodes": [{"file": "node1_base.dart", "name": "ListView 기본 사용", "desc": ["【children】 : 하위 컴포넌트 리스트   【List<Widget>】", "【padding】 : 내부 여백  【EdgeInsetsGeometry】"]}, {"file": "node2_direction.dart", "name": "ListView 가로 스크롤", "desc": ["【scrollDirection】 : 스크롤 방향   【Axis】", "【reverse】 : 반대 방향 스크롤 여부   【bool】", "【shrinkWrap】 : 경계가 없을 때 감싸기 여부  【bool】"]}, {"file": "node3_builder.dart", "name": "ListView.builder 구성", "desc": ["【itemCount】 : 항목 개수   【int】", "【itemBuilder】 : 항목 빌더   【IndexedWidgetBuilder】"]}, {"file": "node4_separated.dart", "name": "ListView.separated 구성", "desc": ["【separatorBuilder】 : 항목 빌더   【IndexedWidgetBuilder】"]}]}
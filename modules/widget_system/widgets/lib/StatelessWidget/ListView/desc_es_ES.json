{"id": 162, "name": "ListView", "localName": "Componente de lista", "info": "El líder en la visualización de listas, capaz de contener múltiples componentes hijos, puede construirse mediante builder, separated, custom, etc. Tiene propiedades como el relleno interno, si es inverso, el controlador de desplazamiento, etc.", "lever": 5, "family": 0, "linkIds": [16, 163], "nodes": [{"file": "node1_base.dart", "name": "Uso básico de ListView", "desc": ["【children】 : Lista de componentes hijos   【List<Widget>】", "【padding】 : <PERSON><PERSON><PERSON> interno  【EdgeInsetsGeometry】"]}, {"file": "node2_direction.dart", "name": "Desplazamiento horizontal de ListView", "desc": ["【scrollDirection】 : Dirección de desplazamiento   【Axis】", "【reverse】 : Si el desplazamiento es inverso   【bool】", "【shrinkWrap】 : Si se envuelve cuando no hay límites  【bool】"]}, {"file": "node3_builder.dart", "name": "Construcción de ListView.builder", "desc": ["【itemCount】 : Número de elementos   【int】", "【itemBuilder】 : Constructor de elementos   【IndexedWidgetBuilder】"]}, {"file": "node4_separated.dart", "name": "Construcción de ListView.separated", "desc": ["【separatorBuilder】 : Constructor de elementos   【IndexedWidgetBuilder】"]}]}
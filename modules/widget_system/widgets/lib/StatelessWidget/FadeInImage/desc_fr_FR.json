{"id": 8, "name": "FadeInImage", "localName": "Image en fondu", "info": "Charge une image avec un dégradé transparent. <PERSON><PERSON> pouvez spécifier une image de remplacement, les courbes d'animation d'entrée et de sortie, la durée, la largeur, la hauteur, le type de fit, l'alignement, le mode de répétition, etc.", "lever": 2, "family": 0, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "FadeInImage.assetNetwork charger une image réseau", "desc": ["【placeholder】 : adresse de l'image de remplacement  【String】", "【image】 : adresse de l'image à afficher  【String】", "【width】: largeur   【double】", "【height】: hauteur   【double】", "【fadeInDuration】: durée du fondu entrant   【Duration】", "【fadeOutDuration】: durée du fondu sortant   【Duration】", "【fadeInCurve】: courbe du fondu entrant   【Cubic】", "【fadeOutCurve】: courbe du fondu sortant   【Cubic】", "【fit】: mode d'ajustement   【BoxFit】", "【alignment】: mode d'alignement   【Alignment】", "【repeat】: mode de répétition   【ImageRepeat】,"]}]}
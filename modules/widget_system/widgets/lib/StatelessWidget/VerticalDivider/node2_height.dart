

import 'package:flutter/material.dart';

/// create by 张风捷特烈 on 2020/4/27
/// contact me <NAME_EMAIL>

class WidthVerticalDivider extends StatelessWidget {
  const WidthVerticalDivider({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    List<Color> dataColor = [
      Colors.red, Colors.yellow,
      Colors.blue, Colors.green];
    List<double> dataThickness = [10.0, 20.0, 30.0, 40.0];
    Map<Color,double>  data = Map.fromIterables(dataColor, dataThickness);
    return SizedBox(
      height: 150,
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: dataColor
            .map((e) => VerticalDivider(
          color: e,
          indent:data[e],
          endIndent: data[e]!*2,
          width: data[e],
          thickness: data[e]!/10,
        ))
            .toList(),
      ),
    );
  }
}

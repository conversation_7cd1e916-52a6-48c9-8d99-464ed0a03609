{"id": 23, "name": "MaterialButton", "localName": "Pulsante Material", "info": "Un pulsante Material generico implementato sulla base di RawMaterialButton. Può contenere un componente figlio, può definire colori, forme e altre rappresentazioni, e può ricevere eventi di clic e pressione prolungata.", "lever": 4, "family": 0, "linkIds": [25, 26, 27, 326, 175], "nodes": [{"file": "node1_base.dart", "name": "Evento di clic di MaterialButton", "desc": ["【color】: Colore   【Color】", "【splashColor】: Colore dell'effetto a increspatura   【Color】", "【height】: Altezza   【double】", "【elevation】: Profondità dell'ombra   【double】", "【child】: Componente figlio   【Widget】", "【textColor】: Colore del testo del componente figlio   【Color】", "【highlightColor】: Colore di evidenziazione durante la pressione prolungata   【Color】", "【padding】: Spaziatura interna   【EdgeInsetsGeometry】", "【onPressed】: Evento di clic   【Function】"]}, {"file": "node2_onLongPress.dart", "name": "Evento di pressione prolungata di MaterialButton", "desc": ["【highlightColor】: Colore di evidenziazione durante la pressione prolungata   【Color】", "【onLongPress】: Evento di pressione prolungata   【Function】"]}, {"file": "node3_shape.dart", "name": "Forma personalizzata di MaterialButton", "desc": ["【shape】: Forma   【ShapeBorder】"]}]}
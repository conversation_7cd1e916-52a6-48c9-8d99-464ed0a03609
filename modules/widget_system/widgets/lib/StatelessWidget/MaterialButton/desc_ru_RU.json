{"id": 23, "name": "MaterialButton", "localName": "Материальная кнопка", "info": "Универсальная Material кнопка, реализованная на основе RawMaterialButton. Может содержать дочерний компонент, позволяет определять цвет, форму и другие характеристики, может принимать события нажатия и длительного нажатия.", "lever": 4, "family": 0, "linkIds": [25, 26, 27, 326, 175], "nodes": [{"file": "node1_base.dart", "name": "Событие нажатия MaterialButton", "desc": ["【color】: Цвет   【Color】", "【splashColor】: Цвет водяного эффекта   【Color】", "【height】: Высота   【double】", "【elevation】: Г<PERSON><PERSON><PERSON><PERSON>на тени   【double】", "【child】: До<PERSON><PERSON><PERSON>ний компонент   【Widget】", "【textColor】: Цвет текста дочернего компонента   【Color】", "【highlightColor】: Цвет подсветки при длительном нажатии   【Color】", "【padding】: Внутренний отступ   【EdgeInsetsGeometry】", "【onPressed】: Событие нажатия   【Function】"]}, {"file": "node2_onLongPress.dart", "name": "Событие длительного нажатия MaterialButton", "desc": ["【highlightColor】: Цвет подсветки при длительном нажатии   【Color】", "【onLongPress】: Событие длительного нажатия   【Function】"]}, {"file": "node3_shape.dart", "name": "Пользовательская форма MaterialButton", "desc": ["【shape】: Форма   【ShapeBorder】"]}]}
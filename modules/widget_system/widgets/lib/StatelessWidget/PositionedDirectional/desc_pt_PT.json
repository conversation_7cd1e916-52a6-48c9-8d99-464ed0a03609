{"id": 159, "name": "PositionedDirectional", "localName": "Posicionamento Direcional", "info": "Funciona da mesma forma que o componente Positioned, mas com nomes de propriedades diferentes. Só pode ser usado em Stack, e permite especificar distâncias de cima, esquerda, direita e baixo para posicionar um componente com precisão.", "lever": 3, "family": 0, "linkIds": [108, 122], "nodes": [{"file": "node1_base.dart", "name": "Uso Básico de PositionedDirectional", "desc": ["【child】 : Componente   【Widget】", "【top】 : <PERSON><PERSON><PERSON><PERSON> até o topo do pai   【double】", "【end】 : <PERSON><PERSON><PERSON><PERSON> até a direita do pai   【double】", "【start】 : <PERSON><PERSON><PERSON><PERSON> até a esquerda do pai   【double】", "【bottom】 : Distância até a base do pai   【double】"]}]}
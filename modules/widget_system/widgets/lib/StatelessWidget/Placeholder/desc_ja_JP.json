{"id": 36, "name": "Placeholder", "localName": "プレースホルダーコンポーネント", "info": "矩形とバツ印のプレースホルダーコンポーネントで、色、線の太さ、幅と高さなどの属性を指定できます。", "lever": 1, "family": 0, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "Placeholderの基本属性", "desc": ["【color】: 色   【Color】", "【strokeWidth】: 線の太さ   【double】"]}, {"file": "node2_fallback.dart", "name": "Placeholderのfallback属性", "desc": ["エリアに幅と高さの制約がない場合のプレースホルダーコンポーネントの幅と高さ。", "【fallbackHeight】: 高さ   【double】", "【fallbackWidth】: 幅   【double】"]}]}
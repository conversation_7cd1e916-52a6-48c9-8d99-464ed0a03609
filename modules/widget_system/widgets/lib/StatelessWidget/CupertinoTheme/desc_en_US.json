{"id": 169, "name": "CupertinoTheme", "localName": "iOS Theme", "info": "You can obtain the CupertinoThemeData object through CupertinoTheme.of. You can also specify the theme to be applied to the descendant components of CupertinoTheme.", "lever": 3, "family": 0, "linkIds": [156, 168], "nodes": [{"file": "node1_base.dart", "name": "Text Style-TextTheme", "desc": ["Descendant components can obtain and use the theme data through CupertinoTheme.of."]}, {"file": "node2_use.dart", "name": "Usage of CupertinoThemeData", "desc": ["Like Theme, you can share specified properties among descendants, but there are fewer properties. Note that if you need to use the theme, you cannot obtain it in the current context."]}]}
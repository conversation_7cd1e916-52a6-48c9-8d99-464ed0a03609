{"id": 210, "name": "PageStorage", "localName": "Page Storage", "info": "Can store the state of a page, maintaining the state when switching pages. It is used in ScrollView, PageView, ExpansionTile, etc. in the source code.", "lever": 3, "family": 0, "linkIds": [52, 165], "nodes": [{"file": "node1_base.dart", "name": "Basic Usage of PageStorage", "desc": ["【bucket】: Storage Area 【PageStorageBucket】", "【child】: Child Widget 【Widget】", "When switching interfaces and initializing components, the state will not be reset. As in the CountWidget example, the child component needs to read the state from the storage during initialization and write the state to the storage when changing the state. Additionally, MaterialApp already has PageStorage built-in, but you can also create your own PageStorage."]}]}
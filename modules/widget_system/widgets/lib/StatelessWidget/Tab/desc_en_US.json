{"id": 148, "name": "Tab", "localName": "Tab", "info": "Generally used as an item in the TabBar, with a top-bottom structure, allowing the specification of an icon and a content component.", "lever": 1, "family": 0, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "Basic Usage of Tab", "desc": ["【child】: Child component 【Widget】", "【text】: Text 【String】", "【icon】: Bottom component 【Widget】", "    text and child cannot exist simultaneously"]}]}
{"id": 9, "name": "CircleAvatar", "localName": "Componente Circular", "info": "Pode transformar uma imagem em um círculo e colocar um componente no meio. Pode especificar raio, cor de primeiro plano, cor de fundo, etc.", "lever": 4, "family": 0, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "Comportamento do CircleAvatar", "desc": ["【radius】 : raio  【double】", "【backgroundImage】 : recurso de imagem  【ImageProvider】", "【foregroundColor】: cor de primeiro plano   【Color】", "【backgroundColor】: cor de fundo   【Color】", "【minRadius】: raio mínimo   【double】", "【maxRadius】: raio máximo   【double】", "【child】: component<PERSON> filho   【Child】"]}]}
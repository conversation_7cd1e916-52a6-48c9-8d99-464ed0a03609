{"id": 14, "name": "InputChip", "localName": "<PERSON>", "info": "Stile simile al componente Chip, integrato con eventi di clic, eliminazione e selezione. Nota: gli eventi di clic e selezione non possono coesistere.", "lever": 4, "family": 0, "linkIds": [11, 12, 13, 15, 153], "nodes": [{"file": "node1_base.dart", "name": "Può accettare eventi di clic ed eliminazione", "desc": ["【onPressed】: Evento di clic   【Function()】", "【onDeleted】: Evento di eliminazione   【Function()】"]}, {"file": "node2_select.dart", "name": "Può accettare eventi di selezione", "desc": ["【selected】: Se selezionato   【bool】", "【onSelected】: Evento di selezione   【Function(bool)】"]}]}
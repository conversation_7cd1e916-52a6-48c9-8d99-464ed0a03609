{"id": 18, "name": "SwitchListTile", "localName": "Переключатель плитки", "info": "Общая структура элемента списка, предоставляемая Flutter, имеет лево-центральную структуру, а в конце находится Switch. Компоненты могут быть вставлены в соответствующие позиции, что позволяет легко адаптироваться к конкретным элементам.", "lever": 3, "family": 0, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "Основное поведение SwitchListTile", "desc": ["【secondary】: Левый компонент   【Widget】", "【title】: Верхний центральный компонент   【Widget】", "【subtitle】: Нижний центральный компонент   【Widget】", "【inactiveThumbColor】: Цвет кружка в невыбранном состоянии   【Color】", "【inactiveTrackColor】: Цвет дорожки в невыбранном состоянии   【Color】", "【activeColor】: Цвет кружка в выбранном состоянии   【Color】", "【activeTrackColor】: Цвет дорожки в выбранном состоянии   【Color】", "【onChanged】: Событие выбора   【Function(bool)】"]}, {"file": "node2_select.dart", "name": "Эффект выбора SwitchListTile", "desc": ["【selected】: Выбрано ли   【bool】", "【inactiveThumbImage】: Изображение кружка в невыбранном состоянии   【ImageProvider】", "【activeThumbImage】: Изображение кружка в выбранном состоянии   【ImageProvider】"]}, {"file": "node3_dense.dart", "name": "Свойство плотного расположения SwitchListTile", "desc": ["【dense】: Плотное ли расположение   【bool】"]}]}
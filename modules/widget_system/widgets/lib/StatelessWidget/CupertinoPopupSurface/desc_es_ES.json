{"id": 217, "name": "CupertinoPopupSurface", "localName": "Capa emergente difuminada", "info": "Fondo difuminado rectangular con bordes redondeados para cuadros emergentes de iOS, utilizado en diálogos de estilo Cupertino en el código fuente.", "lever": 2, "family": 0, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "Uso de CupertinoPopupSurface", "desc": ["【isSurfacePainted】 : Si se pinta en blanco   【bool】", "【child】 : <PERSON>mpo<PERSON><PERSON> hijo   【Widget】", "Efecto de prueba: izquierda isSurfacePainted = false, derecha isSurfacePainted = true"]}]}
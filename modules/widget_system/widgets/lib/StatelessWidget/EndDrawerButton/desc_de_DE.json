{"id": 361, "name": "EndDrawerButton", "localName": "Rechte Schubladen-Schaltfläche", "info": "Eine rechte Schubladen-Icon-Schaltfläche, die das EndDrawerButtonIcon verwendet, um das Icon anzuzeigen. Standardmäßig öffnet ein Klick die rechte Schublade.", "lever": 1, "family": 0, "linkIds": [275, 276], "nodes": [{"file": "node1_base.dart", "name": "Grundlegende Verwendung von EndDraw<PERSON>", "desc": ["【onPressed】 : <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>  【VoidCallback?】", "【style】: Schaltflächenstil   【ButtonStyle?】", "<PERSON><PERSON> on<PERSON><PERSON> leer ist, wird beim Klicken die rechte Schublade geöffnet."]}]}
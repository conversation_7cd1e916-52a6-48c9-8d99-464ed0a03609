{"id": 140, "name": "SnackBar", "localName": "情報提示バー", "info": "コンポーネントとして簡単な構造コンポーネントで、形状、影の深さ、背景色などを指定できます。一般的にScaffoldStateのshowSnackBarメソッドを使用して下部から表示されます。", "lever": 4, "family": 1, "linkIds": [141, 142], "nodes": [{"file": "node1_base.dart", "name": "SnackBarの基本使用", "desc": ["【content】 : 中間内容コンポーネント   【Widget】", "【action】 : 右側ボタン   【SnackBarAction】", "【duration】 : 持続時間   【Widget】", "【backgroundColor】 : 背景色   【Color】", "【elevation】 : 影の深さ   【double】", "【shape】 : 形状   【ShapeBorder】", "【onVisible】 : 表示時のコールバック  【Function()】"]}]}
{"id": 21, "name": "GridTile", "localName": "Сетка плитки", "info": "Универсальная структура элемента списка, предоставляемая Flutter, которая позволяет указать компоненты заголовка, нижнего колонтитула и дочерние компоненты, часто используется в списках сеток.", "lever": 3, "family": 0, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "Основное представление GridTile выглядит следующим образом", "desc": ["【header】: Компонент заголовка   【Widget】", "【child】: До<PERSON><PERSON><PERSON>ний компонент   【Widget】", "【footer】: Компонент нижнего колонтитула   【Widget】"]}]}
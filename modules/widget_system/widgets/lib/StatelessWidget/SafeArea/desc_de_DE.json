{"id": 207, "name": "SafeArea", "localName": "Sicherheitsbereich", "info": "Durch das Hinzufügen von Innenabständen werden Layoutprobleme angepasst, die durch die Besonderheiten einiger Handys (abgerundete Ecken, Notch-Display usw.) verursacht werden.", "lever": 3, "family": 0, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "SafeArea Verwendungstest", "desc": ["【left】 : Ob die linke Seite aktiviert ist   【bool】", "【top】 : Ob die obere Seite aktiviert ist   【bool】", "【bottom】 : Ob die untere Seite aktiviert ist   【bool】", "【right】 : Ob die rechte Seite aktiviert ist   【bool】", "【child】 : Untergeordnete Komponente   【Widget】"]}]}
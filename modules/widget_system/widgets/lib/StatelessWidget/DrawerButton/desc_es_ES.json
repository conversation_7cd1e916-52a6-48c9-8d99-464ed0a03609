{"id": 276, "name": "<PERSON>er<PERSON><PERSON><PERSON>", "localName": "Botón de cajón", "info": "Un botón de icono de cajón izquierdo, utiliza DrawerButtonIcon para mostrar el icono, el evento de clic predeterminado puede abrir el cajón izquierdo.", "lever": 1, "family": 0, "linkIds": [273, 361], "nodes": [{"file": "node1_base.dart", "name": "Uso básico de DrawerButton", "desc": ["【onPressed】: Evento de clic  【VoidCallback?】", "【style】: <PERSON><PERSON><PERSON> del botón   【ButtonStyle?】", "<PERSON>uando onPressed está vacío, al hacer clic se abrirá el cajón izquierdo."]}]}
{"id": 33, "name": "ToggleButtons", "localName": "Gruppo di pulsanti di commutazione", "info": "Riceve un elenco di componenti e può specificare proprietà come bordi, angoli arro<PERSON>ti, colori, ecc. In base alla logica specifica, è possibile implementare la selezione singola o multipla di più pulsanti.", "lever": 4, "family": 0, "linkIds": [332, 262], "nodes": [{"file": "node1_single.dart", "name": "ToggleButtons selezione singola", "desc": ["【children】: Insieme di componenti figli   【List<Widget>】", "【borderWidth】: Larg<PERSON><PERSON> del bordo   【double】", "【borderRadius】: <PERSON><PERSON> a<PERSON>   【BorderRadius】", "【isSelected】: Insieme di selezione   【List<bool>】", "【onPressed】: Evento di clic   【Function(int)】"]}, {"file": "node2_color.dart", "name": "Proprietà del colore di ToggleButtons", "desc": ["【borderColor】: Colore del bordo   【Color】", "【selectedBorderColor】: Colore del bordo selezionato   【Color】", "【selectedColor】: Colore del componente selezionato   【Color】", "【fillColor】: Colore di riempimento selezionato   【Color】", "【splashColor】: Colore dell'effetto ondulazione   【Color】"]}, {"file": "node3_multi.dart", "name": "ToggleButtons selezione multipla", "desc": ["È possibile controllare la logica di trasformazione dello stato per creare effetti diversi."]}]}
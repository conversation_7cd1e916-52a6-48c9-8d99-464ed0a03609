{"id": 33, "name": "ToggleButtons", "localName": "Toggle Buttons Group", "info": "Receives a list of components, and can specify properties such as border, rounded corners, color, etc. According to specific logic, it can achieve the requirements of single or multiple selection of buttons.", "lever": 4, "family": 0, "linkIds": [332, 262], "nodes": [{"file": "node1_single.dart", "name": "ToggleButtons Single Selection", "desc": ["【children】: Children components   【List<Widget>】", "【borderWidth】: Border width   【double】", "【borderRadius】: Rounded corners   【BorderRadius】", "【isSelected】: Selection status set   【List<bool>】", "【onPressed】: Click event   【Function(int)】"]}, {"file": "node2_color.dart", "name": "ToggleButtons Color Properties", "desc": ["【borderColor】: Border color   【Color】", "【selectedBorderColor】: Selected border color   【Color】", "【selectedColor】: Component color when selected   【Color】", "【fillColor】: Fill color when selected   【Color】", "【splashColor】: Ripple color   【Color】"]}, {"file": "node3_multi.dart", "name": "ToggleButtons Multiple Selection", "desc": ["The logic of state transformation can be controlled to create different effects."]}]}
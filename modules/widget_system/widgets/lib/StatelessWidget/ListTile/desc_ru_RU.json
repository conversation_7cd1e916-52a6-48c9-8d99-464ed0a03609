{"id": 16, "name": "ListTile", "localName": "Список плиток", "info": "Универсальная структура элемента списка, предоставляемая Flutter, имеет структуру слева, по центру и справа. В соответствующие позиции можно вставлять компоненты, что позволяет легко адаптироваться к конкретным элементам.", "lever": 3, "family": 0, "linkIds": [162, 334], "nodes": [{"file": "node1_base.dart", "name": "Основное представление ListTile выглядит следующим образом", "desc": ["【leading】: Левый компонент   【Widget】", "【title】: Верхний центральный компонент   【Widget】", "【subtitle】: Нижний центральный компонент   【Widget】", "【trailing】: Правый компонент   【Widget】", "【contentPadding】: Внутренний отступ   【EdgeInsetsGeometry】", "【onLongPress】: Событие нажатия   【Function()】"]}, {"file": "node2_select.dart", "name": "Эффект выбора и событие долгого нажатия ListTile", "desc": ["【selected】: Выбрано ли   【bool】", "【onTap】: Событие нажатия   【Function()】"]}, {"file": "node3_dense.dart", "name": "Свойство плотного расположения ListTile", "desc": ["【dense】: Плотное ли расположение   【bool】"]}]}
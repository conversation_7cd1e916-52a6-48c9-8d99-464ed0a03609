{"id": 16, "name": "ListTile", "localName": "List Tile", "info": "A general list item structure provided by Flutter, which has a left-center-right structure. Components can be inserted at the corresponding positions, making it easy to handle specific items.", "lever": 3, "family": 0, "linkIds": [162, 334], "nodes": [{"file": "node1_base.dart", "name": "The basic representation of ListTile is as follows", "desc": ["【leading】: Left component   【Widget】", "【title】: Top middle component   【Widget】", "【subtitle】: Bottom middle component   【Widget】", "【trailing】: Trailing component   【Widget】", "【contentPadding】: Inner padding   【EdgeInsetsGeometry】", "【onLongPress】: Click event   【Function()】"]}, {"file": "node2_select.dart", "name": "ListTile selection effect and long press event", "desc": ["【selected】: Whether selected   【bool】", "【onTap】: Click event   【Function()】"]}, {"file": "node3_dense.dart", "name": "Dense property of ListTile", "desc": ["【dense】: Whether dense   【bool】"]}]}
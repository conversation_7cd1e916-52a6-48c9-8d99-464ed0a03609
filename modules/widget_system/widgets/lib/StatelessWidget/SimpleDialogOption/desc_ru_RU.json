{"id": 133, "name": "SimpleDialogOption", "localName": "Простая опция диалога", "info": "Кнопка, которая редко используется, обычно используется в SimpleDialog, принимает событие нажатия.", "lever": 1, "family": 0, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "Основное использование SimpleDialogOption", "desc": ["【child】 : До<PERSON><PERSON><PERSON>ний компонент   【Widget】", "【onPressed】 : Событие нажатия  【Function()】"]}]}
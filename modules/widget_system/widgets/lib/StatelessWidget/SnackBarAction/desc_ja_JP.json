{"id": 141, "name": "SnackBarAction", "localName": "スナックバーボタン", "info": "一般的にSnackBarでのみ使用され、クリックイベントを受け取ります。一度クリックするとボタンは無効化され、色や無効時の色を指定できます。", "lever": 1, "family": 1, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "SnackBarActionの基本使用", "desc": ["【label】 :  ラベル  【String】", "【textColor】 : 文字色   【Color】", "【disabledTextColor】 : 無効時の文字色   【Color】", "【onPressed】 : クリックコールバック  【Function()】"]}]}
{"id": 10, "name": "Visibility", "localName": "显隐组件", "info": "控制一个组件显示或隐藏,可设置隐藏后的占位组件。与其功能相似的由OffStage组件。", "lever": 3, "family": 0, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "根据visible控制内部子组件的显隐情况", "desc": ["【visible】 : 是否显示  【bool】", "【child】: 孩子   【Widget】", "默认孩子隐藏时会失去原来所在区域。"]}, {"file": "node2_replacement.dart", "name": "replacement可在隐藏时进行占位", "desc": ["【replacement】 : 隐藏时的占位组件  【Widget】"]}]}
{"id": 155, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "localName": "<PERSON>-tête de tiroir", "info": "Généralement utilisé dans Drawer, comme en-tête du tiroir. Peut spécifier des propriétés telles que les marges intérieures et extérieures, la décoration, les composants enfants, etc.", "lever": 2, "family": 0, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "Utilisation de base de DrawerHeader", "desc": ["【child】 : <PERSON><PERSON><PERSON><PERSON> enfant   【Widget】", "【decoration】 : Décoration   【Decoration】", "【margin】 : Marge extérieure   【EdgeInsetsGeometry】", "【padding】 : Marge intérieure   【EdgeInsetsGeometry】"]}]}
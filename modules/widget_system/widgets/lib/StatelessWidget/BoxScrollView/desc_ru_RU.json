{"id": 350, "name": "BoxScrollView", "localName": "Прокручиваемое представление коробки", "info": "BoxScrollView — это абстрактный класс, который наследуется от ScrollView, поэтому его нельзя использовать напрямую. Его подклассы включают ListView и GridView. Обычно не рекомендуется самостоятельно реализовывать подклассы для его использования.", "lever": 1, "family": 0, "linkIds": [183, 162, 163], "nodes": [{"file": "node1_base.dart", "name": "Введение в BoxScrollView", "desc": ["【reverse】 : Обратный порядок   【bool】", "【scrollDirection】 : Направление прокрутки   【Axis】", "【cacheExtent】 : Размер кэша   【double】", "【dragStartBehavior】 : Поведение при перетаскивании   【DragStartBehavior】", "【clipBehavior】 : Поведение обрезки   【ClipBehavior】", "【controller】 : Контро<PERSON><PERSON>ер   【ScrollController】"]}]}
{"id": 11, "name": "Chip", "localName": "Kleine Leiste Komponente", "info": "Eine horizontale, abgerundete kleine Leiste, die links, mittig und rechts Komponenten enthalten kann. <PERSON><PERSON>, Schattenfarbe und Klick-Ereignisse angeben.", "lever": 4, "family": 0, "linkIds": [12, 13, 14, 15, 153], "nodes": [{"file": "node1_base.dart", "name": "Normale Darstellung des Chips wie folgt", "desc": ["【avatar】: <PERSON>e Komponente   【Widget】", "【label】: <PERSON><PERSON><PERSON>mpo<PERSON>   【Widget】", "【padding】 : Innenabstand  【EdgeInsetsGeometry】", "【labelPadding】: Label-Abstand   【EdgeInsetsGeometry】"]}, {"file": "node2_color.dart", "name": "Farbe und Schatten können eingestellt werden", "desc": ["【backgroundColor】: Hintergrundfarbe   【Color】", "【shadowColor】: Schattenfarbe   【Color】", "【elevation】: Sc<PERSON>tentiefe   【double】"]}, {"file": "node3_delete.dart", "name": "<PERSON><PERSON><PERSON>-Schaltfläche kann eingestellt werden", "desc": ["【deleteIcon】: <PERSON><PERSON><PERSON> (normalerweise ein Icon)   【Widget】", "【deleteIconColor】: Farbe der rechten Komponente   【Color】", "【onDeleted】: Klick<PERSON>Ereignis der rechten Komponente   【Function】"]}]}
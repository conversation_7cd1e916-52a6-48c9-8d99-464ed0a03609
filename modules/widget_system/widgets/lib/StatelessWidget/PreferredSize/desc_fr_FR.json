{"id": 204, "name": "PreferredSize", "localName": "<PERSON><PERSON>", "info": "Implémente l'interface PreferredSizeWidget, peut contenir un composant enfant, définit une taille préférée et n'impose aucune contrainte à son composant enfant.", "lever": 2, "family": 0, "linkIds": [57, 64], "nodes": [{"file": "node1_base.dart", "name": "Ajuster la hauteur de l'AppBar avec PreferredSize", "desc": ["【preferredSize】 : taille   【Size】"]}, {"file": "node2_adapter.dart", "name": "Conversion et utilisation de PreferredSize", "desc": ["【PreferredSize convertit un composant ordinaire en PreferredSizeWidget"]}]}
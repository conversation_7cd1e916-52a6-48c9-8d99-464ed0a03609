{"id": 204, "name": "PreferredSize", "localName": "Tamaño preferido", "info": "Implementa la interfaz PreferredSizeWidget, puede contener un componente hijo, establece un tamaño preferido y no impone ninguna restricción a su componente hijo.", "lever": 2, "family": 0, "linkIds": [57, 64], "nodes": [{"file": "node1_base.dart", "name": "Ajustar la altura de AppBar con PreferredSize", "desc": ["【preferredSize】 : tamaño   【Size】"]}, {"file": "node2_adapter.dart", "name": "Uso de conversión de PreferredSize", "desc": ["【PreferredSize convierte un componente normal en un PreferredSizeWidget"]}]}
{"id": 204, "name": "PreferredSize", "localName": "优先尺寸", "info": "实现了PreferredSizeWidget接口，可容纳一个子组件，设置优先尺寸，不会对其子组件施加任何约束。", "lever": 2, "family": 0, "linkIds": [57, 64], "nodes": [{"file": "node1_base.dart", "name": "PreferredSize调整AppBar高度", "desc": ["【preferredSize】 : 尺寸   【Size】"]}, {"file": "node2_adapter.dart", "name": "PreferredSize的转化使用", "desc": ["【PreferredSize将普通组件转化为PreferredSizeWidget"]}]}
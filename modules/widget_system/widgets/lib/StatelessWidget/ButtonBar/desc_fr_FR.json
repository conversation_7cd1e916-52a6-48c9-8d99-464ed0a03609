{"id": 29, "name": "<PERSON><PERSON><PERSON><PERSON>", "localName": "Barre de boutons", "info": "Reçoit une liste de composants, souvent utilisée pour contenir plusieurs boutons. Peut spécifier l'alignement, les marges, etc.", "lever": 3, "family": 0, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "Alignement de la barre de boutons", "desc": ["【alignment】: Alignement   【MainAxisAlignment】", "【children】: Ensemble de composants enfants   【List<Widget>】"]}, {"file": "node2_padding.dart", "name": "Marge et hauteur de la barre de boutons", "desc": ["【buttonPadding】: Marge intérieure   【EdgeInsetsGeometry】", "【buttonHeight】: <PERSON><PERSON>   【double】"]}]}
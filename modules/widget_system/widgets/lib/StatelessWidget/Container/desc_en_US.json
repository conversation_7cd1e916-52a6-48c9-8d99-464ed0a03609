{"id": 1, "name": "Container", "localName": "Container Component", "info": "A container component used to hold a single child component. It integrates several functionalities of single child components, such as padding, margin, transformation, decoration, constraints, etc...", "lever": 5, "family": 0, "linkIds": [74, 85, 80, 78, 70, 123], "nodes": [{"file": "node1_base.dart", "name": "Can be used to display an area with specified width and height", "desc": ["【width】 : width   【int】", "【height】: height   【int】", "【color】: color    【Color】"]}, {"file": "node2_child.dart", "name": "Can place a child component within the area", "desc": ["【padding】 : padding   【EdgeInsetsGeometry】", "【margin】: margin   【EdgeInsetsGeometry】", "【child】: child component    【Widget】"]}, {"file": "node3_alignment.dart", "name": "Can align and position the child component", "desc": ["【alignment】 : alignment   【AlignmentGeometry】"]}, {"file": "node4_decoration.dart", "name": "Can decorate the child component", "desc": ["【decoration】 : decoration   【Decoration】", "Can decorate: borders, rounded corners, colors, gradients, shadows, images, etc."]}, {"file": "node5_transform.dart", "name": "Container also has transformation capabilities", "desc": ["【transform】 : transformation matrix   【Matrix4】", "Matrix transformation based on Matrix4, see linear algebra for details"]}, {"file": "node6_constraints.dart", "name": "Constraints of the Container", "desc": ["【constraints】 : constraints   【BoxConstraints】", "Will constrain the size of the area, it will not be smaller than the specified minimum width and height, nor larger than the specified maximum width and height."]}]}
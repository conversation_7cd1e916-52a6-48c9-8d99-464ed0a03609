import 'package:flutter/material.dart';

/// create by 张风捷特烈 on 2020/4/25
/// contact me <NAME_EMAIL>

class ContainerAlignment extends StatelessWidget {
  const ContainerAlignment({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      alignment: Alignment.bottomRight,
      width: 200,
      height: 200 * 0.618,
      color: Colors.grey.with<PERSON><PERSON><PERSON>(88),
      child: const Icon(
        Icons.android,
        color: Colors.green,
      ),
    );
  }
}
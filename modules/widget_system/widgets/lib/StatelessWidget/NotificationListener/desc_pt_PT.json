{"id": 220, "name": "NotificationListener", "localName": "Ouvinte de Notificação", "info": "Pode especificar o subgenérico T de Notification para monitorar alterações desse tipo. O Flutter possui muitas Notificações de deslizamento integradas, mas você também pode criar e monitorar suas próprias Notificações personalizadas.", "lever": 2, "family": 0, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "Monitorar OverscrollIndicatorNotification", "desc": ["Esta notificação é chamada de volta apenas quando o deslizamento atinge o topo ou o fundo, e o atributo leading determina se é o topo ou o fundo. <PERSON><PERSON><PERSON> disso, através de notification#disallowGlow(), é possível remover a sombra azul do deslizamento no topo e no fundo."]}, {"file": "node2_update.dart", "name": "Monitorar ScrollUpdateNotification", "desc": ["Durante o processo de deslizamento, os dados de deslizamento são chamados de volta, permitindo que você obtenha uma grande quantidade de dados para operações."]}]}
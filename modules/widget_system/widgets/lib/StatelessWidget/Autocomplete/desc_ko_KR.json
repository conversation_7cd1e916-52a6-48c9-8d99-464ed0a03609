{"id": 356, "name": "Autocomplete", "localName": "자동 완성", "info": "입력 중에, 사용자가 선택할 수 있도록 연관어를 보여주는 레이어를 제공하며, 높은 수준의 커스터마이징이 가능합니다.", "lever": 4, "family": 0, "linkIds": [54, 199], "nodes": [{"file": "node1_base.dart", "name": "Autocomplete 기본 사용", "desc": ["【optionsBuilder】 : 옵션 빌더   【AutocompleteOptionsBuilder<T>】", "【onSelected】 : 선택 시 콜백   【AutocompleteOnSelected<T>】"]}, {"file": "node2_type.dart", "name": "Autocomplete의 제네릭", "desc": ["【optionsViewBuilder】 : 패널 빌더   【AutocompleteOptionsViewBuilder<T>】", "【fieldViewBuilder】 : 입력 빌더   【AutocompleteFieldViewBuilder】", "【displayStringForOption】 : 텍스트 표시   【AutocompleteOptionToString】,"]}]}
{"id": 356, "name": "Autocomplete", "localName": "自动填充", "info": "在输入期间，提供联想词浮层展示，以便用户选择，拥有高度的可定制性。", "lever": 4, "family": 0, "linkIds": [54, 199], "nodes": [{"file": "node1_base.dart", "name": "Autocomplete基本使用", "desc": ["【optionsBuilder】 : 选项构造器   【AutocompleteOptionsBuilder<T>】", "【onSelected】 : 选择时回调   【AutocompleteOnSelected<T>】"]}, {"file": "node2_type.dart", "name": "Autocomplete的泛型", "desc": ["【optionsViewBuilder】 : 面板构造器   【AutocompleteOptionsViewBuilder<T>】", "【fieldViewBuilder】 : 输入构造器   【AutocompleteFieldViewBuilder】", "【displayStringForOption】 : 文字展示   【AutocompleteOptionToString】,"]}]}
{"id": 211, "name": "MaterialBanner", "localName": "Composant de bannière", "info": "Composant de bannière de style Material, prenant en charge les structures gauche-centre-droite ou gauche-centre-bas, peut spécifier les marges, la couleur de fond, etc.", "lever": 2, "family": 0, "linkIds": [], "nodes": [{"file": "node1_one_btn.dart", "name": "Utilisation d'une ligne de MaterialBanner", "desc": ["【content】 : Composant central   【Widget】", "【leading】: Composant gauche   【Widget】", "【actions】: Liste des composants droits   【List<Widget>】", "【padding】: Marge intérieure   【EdgeInsetsGeometry】", "【forceActionsBelow】: Si les boutons sont en bas   【bool】", "【backgroundColor】: <PERSON>uleur de fond    【Color】"]}, {"file": "node2_two_btn.dart", "name": "Utilisation de deux lignes de MaterialBanner", "desc": ["【contentTextStyle】: Style de la position centrale   【TextStyle】", "【leadingPadding】: <PERSON><PERSON> du composant gauche    【EdgeInsetsGeometry】", "Lorsque le nombre de composants de fin est supérieur à 1, la structure du composant est gauche-centre-bas."]}]}
{"id": 211, "name": "MaterialBanner", "localName": "Компонент баннера", "info": "Компонент баннера в стиле Material, поддерживает структуру слева-в центре-справа или слева-в центре-снизу, можно указать отступы, цвет фона и т.д.", "lever": 2, "family": 0, "linkIds": [], "nodes": [{"file": "node1_one_btn.dart", "name": "Использование MaterialBanner в одну строку", "desc": ["【content】 : Центральный компонент   【Widget】", "【leading】: Левый компонент   【Widget】", "【actions】: Список правых компонентов   【List<Widget>】", "【padding】: Внутренние отступы   【EdgeInsetsGeometry】", "【forceActionsBelow】: Кнопки внизу   【bool】", "【backgroundColor】: Цвет фона    【Color】"]}, {"file": "node2_two_btn.dart", "name": "Использование MaterialBanner в две строки", "desc": ["【contentTextStyle】: Стиль центральной позиции   【TextStyle】", "【leadingPadding】: Отступ левого компонента    【EdgeInsetsGeometry】", "Когда количество конечных компонентов больше 1, структура компонента становится слева-в центре-снизу."]}]}
{"id": 211, "name": "MaterialBanner", "localName": "배너 컴포넌트", "info": "Material 스타일의 배너 컴포넌트, 좌중우 또는 좌중하 구조를 지원하며, 여백 및 배경색 등을 지정할 수 있습니다.", "lever": 2, "family": 0, "linkIds": [], "nodes": [{"file": "node1_one_btn.dart", "name": "MaterialBanner 한 줄 사용", "desc": ["【content】 : 중간 컴포넌트   【Widget】", "【leading】: 왼쪽 컴포넌트   【Widget】", "【actions】: 오른쪽 컴포넌트 리스트   【List<Widget>】", "【padding】: 안쪽 여백   【EdgeInsetsGeometry】", "【forceActionsBelow】: 버튼이 아래에 있는지 여부   【bool】", "【backgroundColor】: 배경색    【Color】"]}, {"file": "node2_two_btn.dart", "name": "MaterialBanner 두 줄 사용", "desc": ["【contentTextStyle】: 중간 위치 스타일   【TextStyle】", "【leadingPadding】: 왼쪽 컴포넌트 여백    【EdgeInsetsGeometry】", "꼬리 컴포넌트 수가 1보다 크면, 이 컴포넌트 구조는 좌중하입니다."]}]}
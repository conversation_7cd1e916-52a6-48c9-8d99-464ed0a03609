{"id": 193, "name": "AboutListTile", "localName": "关于应用条目", "info": "一个点击条目，点击时可以弹出应用相关信息，可指定应用图标、应用名、应用版本号等信息和内部的子组件列表。", "lever": 3, "family": 0, "linkIds": [130, 145], "nodes": [{"file": "node1_base.dart", "name": "AboutListTile基本使用", "desc": ["【icon】 : 左图标   【Widget】", "【applicationIcon】 : 左上图标   【Widget】", "【applicationVersion】 : 版本号  【String】", "【applicationName】 : 应用名  【String】", "【applicationLegalese】 : 应用律术   【String】", "【aboutBoxChildren】 : 弹框内容组件   【List<Widget>】"]}]}
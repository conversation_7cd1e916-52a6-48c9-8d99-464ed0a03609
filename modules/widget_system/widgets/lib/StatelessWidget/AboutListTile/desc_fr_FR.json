{"id": 193, "name": "AboutListTile", "localName": "Élément À Propos de l'Application", "info": "Un élément cliquable qui peut afficher des informations sur l'application lorsqu'il est cliqué. Peut spécifier l'icône de l'application, le nom de l'application, le numéro de version de l'application et d'autres informations ainsi qu'une liste de composants internes.", "lever": 3, "family": 0, "linkIds": [130, 145], "nodes": [{"file": "node1_base.dart", "name": "Utilisation de base de AboutListTile", "desc": ["【icon】 : Icône gauche   【Widget】", "【applicationIcon】 : Icône en haut à gauche   【Widget】", "【applicationVersion】 : Numéro de version  【String】", "【applicationName】 : Nom de l'application  【String】", "【applicationLegalese】 : Législation de l'application   【String】", "【aboutBoxChildren】 : Composants du contenu de la boîte de dialogue   【List<Widget>】"]}]}
{"id": 2, "name": "Text", "localName": "텍스트 컴포넌트", "info": "텍스트를 표시하는 데 사용되는 컴포넌트. 매우 많은 속성을 가지고 있어 사용자의 요구를 충족시킬 수 있으며, 핵심 스타일은 style 속성에 의해 제어됩니다.", "lever": 5, "family": 0, "linkIds": [101, 324], "nodes": [{"file": "node1.dart", "name": "텍스트의 기본 스타일", "desc": ["【입력 매개변수】 : 텍스트  【String】", "【style】: 텍스트 스타일   【TextStyle】", "【color】: 텍스트 색상   【Color】", "【fontSize】: 텍스트 크기   【double】", "【fontWeight】: 글자 두께   【FontWeight】", "【fontStyle】: 글자 스타일   【fontStyle】", "【letterSpacing】: 글자 간격   【double】"]}, {"file": "node2.dart", "name": "텍스트 그림자", "desc": ["【shadows】 : 텍스트  【List<Shadow>】", "【backgroundColor】: 배경 색상   【Color】"]}, {"file": "node3_decoration.dart", "name": "텍스트 장식선", "desc": ["【fontFamily】 : 텍스트 글꼴  【String】", "【decoration】: 장식선   【TextDecoration】", "【decorationColor】: 장식선 색상   【Color】", "【decorationThickness】: 장식선 두께   【double】", "【decorationStyle】: 장식선 스타일   【TextDecorationStyle】"]}, {"file": "node4_textAlign.dart", "name": "텍스트 정렬 방식", "desc": ["【textAlign】: 정렬 방식   【TextAlign】", "아래는 순서대로:left、right、center、justify、start、end,"]}, {"file": "node5_textDirection.dart", "name": "텍스트 방향과 최대 줄 수", "desc": ["【maxLines】 : 최대 줄 수  【int】", "【textDirection】 : 텍스트 방향  【TextDirection】", "아래는 순서대로:rtl、ltr,"]}, {"file": "node6_softWrap.dart", "name": "줄바꿈 여부와 넘침 효과", "desc": ["【softWrap】 : 줄바꿈 여부  【bool】", "【overflow】 : 넘침 효과  【TextOverflow】", "아래 softWrap=false;  overflow는 순서대로:clip、fade、ellipsis、visible,"]}]}
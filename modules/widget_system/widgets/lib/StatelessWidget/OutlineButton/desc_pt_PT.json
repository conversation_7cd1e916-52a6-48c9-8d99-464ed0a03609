{"id": 27, "name": "OutlineButton", "localName": "Botão de Contorno", "info": "Bot<PERSON> de estilo de borda, implementado com base no MaterialButton, todas as propriedades são semelhantes ao MaterialButton.", "lever": 3, "family": 0, "deprecated": -1, "linkIds": [23, 24, 25, 175], "nodes": [{"file": "node1_base.dart", "name": "Evento de Clique do OutlineButton", "desc": ["【textColor】: Cor do texto do componente filho   【Color】", "【splashColor】: Cor do efeito de ondulação   【Color】", "【highlightColor】: Cor de destaque ao pressionar   【Color】", "【highlightedBorderColor】: Co<PERSON> da borda ao destacar   【Color】", "【child】: Compo<PERSON><PERSON> filho   【Widget】", "【padding】: Preenchimento interno   【EdgeInsetsGeometry】", "【borderSide】: <PERSON><PERSON> da borda   【BorderSide】", "【onPressed】: Evento de clique   【Function】", "    ", "", "class CustomOutlineButton extends StatelessWidget {", "  const CustomOutlineButton({Key? key) : super(key: key);", "", "  final String info =", "      'O botão OutlineButton saiu de cena no Flutter 3.0. O substituto é o botão OutlinedButton.';", "", "  @override", "  Widget build(BuildContext context) {", "    return Container(", "      color: Colors.blue.withOpacity(0.1),", "      padding: const EdgeInsets.all(10),", "      margin: const EdgeInsets.all(10),", "      child: Text(info),", "    );", "  ", "", "   @override", "   Widget build(BuildContext context) {", "     return OutlineButton(Botão de Contorno", "       onPressed: () {,", "       child: const Text(\"OutlineButton\"),", "       padding: const EdgeInsets.all(8),", "       splashColor: Colors.green,", "       highlightColor: Colors.orangeAccent,", "       highlightedBorderColor: Colors.grey,", "       textColor: const Color(0xff000000),", "       borderSide: const BorderSide(color: Color(0xff0A66F8), width: 2),"]}]}
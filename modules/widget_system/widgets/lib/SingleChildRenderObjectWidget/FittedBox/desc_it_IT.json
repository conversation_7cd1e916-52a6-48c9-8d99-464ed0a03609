{"id": 87, "name": "FittedBox", "localName": "Scato<PERSON> adattata", "info": "Può contenere un componente figlio, utilizza la proprietà fit per determinare la modalità di adattamento dell'area del componente figlio rispetto al componente padre, ha la proprietà di allineamento alignment.", "lever": 4, "family": 2, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "Uso base di FittedBox", "desc": ["【child】 : componente figlio   【Widget】", "【fit】 : modalità di adattamento   【BoxFit】", "【alignment】 : modalità di allineamento   【AlignmentGeometry】"]}]}
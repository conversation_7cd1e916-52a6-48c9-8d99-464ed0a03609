{"id": 87, "name": "FittedBox", "localName": "Адаптивный бокс", "info": "Может содержать один дочерний компонент, использует свойство fit для определения режима адаптации области дочернего компонента относительно родительского компонента, имеет свойство выравнивания alignment.", "lever": 4, "family": 2, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "Основное использование FittedBox", "desc": ["【child】 : До<PERSON><PERSON><PERSON>ний компонент   【Widget】", "【fit】 : Режим адаптации   【BoxFit】", "【alignment】 : Способ выравнивания   【AlignmentGeometry】"]}]}
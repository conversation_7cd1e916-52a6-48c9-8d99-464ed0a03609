{"id": 292, "name": "IgnorePointer", "localName": "<PERSON><PERSON><PERSON><PERSON>", "info": "Enthält ein Kind-Widget und kann durch Festlegen der Eigenschaft ignoring entscheiden, ob das Kind Gestenereignisse ignoriert. Es selbst empfängt keine Ereignisse.", "lever": 4, "family": 2, "linkIds": [295, 146, 149, 150], "nodes": [{"file": "node1_base.dart", "name": "Grundlegende Verwendung von <PERSON>", "desc": ["【child】 : Kind-Widget   【Widget】", "【ignoring】 : O<PERSON> <PERSON><PERSON>ignisse ignoriert werden sollen   【bool】", "Wie unten gezeigt, wenn der S<PERSON> ausgewählt ist, ist ignoring true, und die Schaltflächenereignisse werden gesperrt und können nicht geklickt werden."]}]}
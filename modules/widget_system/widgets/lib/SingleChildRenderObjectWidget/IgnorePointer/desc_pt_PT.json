{"id": 292, "name": "IgnorePointer", "localName": "Ignorar Cliques", "info": "Contém um componente filho e pode decidir se o filho ignora eventos de gestos especificando o atributo ignoring, ele próprio não aceita eventos.", "lever": 4, "family": 2, "linkIds": [295, 146, 149, 150], "nodes": [{"file": "node1_base.dart", "name": "Uso Básico do IgnorePointer", "desc": ["【child】 : Compo<PERSON><PERSON> filho   【Widget】", "【ignoring】 : Se deve ignorar eventos   【bool】", "<PERSON><PERSON><PERSON><PERSON>, quando o Switch está selecionado, ignoring é true, e o evento do botão será bloqueado, não podendo ser clicado."]}]}
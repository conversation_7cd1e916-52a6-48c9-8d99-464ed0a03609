{"id": 296, "name": "PhysicalModel", "localName": "Physical Module", "info": "Allows child components to be clipped according to circles and squares, and can specify background color, rounded corners, shadow depth, shadow color, and clipping behavior.", "lever": 3, "family": 2, "linkIds": [279, 69], "nodes": [{"file": "node1_base.dart", "name": "Basic Usage of PhysicalModel", "desc": ["【clipBehavior】: Clipping Behavior   【Clip】", "【borderRadius】: Rounded Corners   【BorderRadius】", "【child】: Child Component   【Widget】", "【elevation】: Shadow Depth   【double】", "【shadowColor】: Shadow Color   【Color】", "【shape】: Shape   【BoxShape】", "【color】: Color    【Color】"]}]}
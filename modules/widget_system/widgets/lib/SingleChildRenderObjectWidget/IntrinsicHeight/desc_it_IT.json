{"id": 298, "name": "IntrinsicHeight", "localName": "Altezza intrinseca", "info": "Un componente che regola le dimensioni dei suoi elementi figli in base all'altezza intrinseca degli elementi figli, risolve molti problemi di layout, ma è relativamente costoso.", "lever": 4, "family": 2, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "Uso di base di IntrinsicHeight", "desc": ["【child】 : Componente figlio   【Widget】", "Come nell'esempio: l'altezza a sinistra è variabile, l'altezza al centro è fissa, l'altezza a destra prende il valore più alto dei due precedenti."]}]}
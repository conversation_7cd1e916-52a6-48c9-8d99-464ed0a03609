{"id": 298, "name": "IntrinsicHeight", "localName": "Встроенная высота", "info": "Компонент, который регулирует размер своих дочерних элементов в зависимости от их собственной высоты, может решить множество проблем с макетом, но относительно дорог.", "lever": 4, "family": 2, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "Основное использование IntrinsicHeight", "desc": ["【child】 : До<PERSON><PERSON><PERSON>ний компонент   【Widget】", "Как в примере: высота слева может изменяться, высота в середине фиксирована, высота справа берет максимальное значение из первых двух."]}]}
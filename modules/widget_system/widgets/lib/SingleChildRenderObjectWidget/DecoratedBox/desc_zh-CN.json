{"id": 70, "name": "DecoratedBox", "localName": "装饰盒", "info": "可容纳一个子组件，可将其进行装饰。核心属性为decoration,可设置边线、渐变、阴影、背景图等。", "lever": 4, "family": 2, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "DecoratedBox基本使用", "desc": ["【decoration】 : 装饰对象   【Decoration】", "【position】 : 前景色(左)/后景色(右)   【DecorationPosition】"]}, {"file": "node2_image.dart", "name": "DecoratedBox形状和图片装饰", "desc": ["【shape】 : 形状   【BoxShape】", "【image】 : 背景图片   【DecorationImage】,"]}, {"file": "node3_border.dart", "name": "DecoratedBox边线装饰", "desc": ["【border】 : 边线   【BoxBorder】,"]}, {"file": "node4_shape.dart", "name": "DecoratedBox形状装饰", "desc": ["通过ShapeDecoration对象可指定边线形状,"]}, {"file": "node5_line.dart", "name": "DecoratedBox底线装饰", "desc": ["通过UnderlineTabIndicator对象可指定底线,"]}, {"file": "node6_flutterLogo.dart", "name": "FlutterLogoDecoration装饰", "desc": ["通过FlutterLogoDecoration对象可指定Flutter图标装饰(并没有什么太大的作用),"]}]}
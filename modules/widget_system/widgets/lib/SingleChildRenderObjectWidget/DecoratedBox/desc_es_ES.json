{"id": 70, "name": "DecoratedBox", "localName": "Caja Decorada", "info": "<PERSON>uede contener un componente hijo y decorarlo. La propiedad principal es decoration, que permite configurar bordes, degradados, sombras, imágenes de fondo, etc.", "lever": 4, "family": 2, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "Uso básico de DecoratedBox", "desc": ["【decoration】 : Objeto de decoración   【Decoration】", "【position】 : Color de primer plano (izquierda)/Color de fondo (derecha)   【DecorationPosition】"]}, {"file": "node2_image.dart", "name": "Decoración de forma e imagen en DecoratedBox", "desc": ["【shape】 : Forma   【BoxShape】", "【image】 : Imagen de fondo   【DecorationImage】,"]}, {"file": "node3_border.dart", "name": "Decoración de borde en DecoratedBox", "desc": ["【border】 : Borde   【BoxBorder】,"]}, {"file": "node4_shape.dart", "name": "Decoración de forma en DecoratedBox", "desc": ["Se puede especificar la forma del borde mediante el objeto ShapeDecoration,"]}, {"file": "node5_line.dart", "name": "Decoración de línea inferior en DecoratedBox", "desc": ["Se puede especificar la línea inferior mediante el objeto UnderlineTabIndicator,"]}, {"file": "node6_flutterLogo.dart", "name": "Decoración con FlutterLogoDecoration", "desc": ["Se puede especificar la decoración del ícono de Flutter mediante el objeto FlutterLogoDecoration (no tiene mucho uso),"]}]}
{"id": 70, "name": "DecoratedBox", "localName": "장식 상자", "info": "하위 컴포넌트를 포함할 수 있으며, 이를 장식할 수 있습니다. 핵심 속성은 decoration으로, 테두리, 그라데이션, 그림자, 배경 이미지 등을 설정할 수 있습니다.", "lever": 4, "family": 2, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "DecoratedBox 기본 사용", "desc": ["【decoration】 : 장식 객체   【Decoration】", "【position】 : 전경색(왼쪽)/배경색(오른쪽)   【DecorationPosition】"]}, {"file": "node2_image.dart", "name": "DecoratedBox 형태와 이미지 장식", "desc": ["【shape】 : 형태   【BoxShape】", "【image】 : 배경 이미지   【DecorationImage】,"]}, {"file": "node3_border.dart", "name": "DecoratedBox 테두리 장식", "desc": ["【border】 : 테두리   【BoxBorder】,"]}, {"file": "node4_shape.dart", "name": "DecoratedBox 형태 장식", "desc": ["ShapeDecoration 객체를 통해 테두리 형태를 지정할 수 있습니다,"]}, {"file": "node5_line.dart", "name": "DecoratedBox 밑줄 장식", "desc": ["UnderlineTabIndicator 객체를 통해 밑줄을 지정할 수 있습니다,"]}, {"file": "node6_flutterLogo.dart", "name": "FlutterLogoDecoration 장식", "desc": ["FlutterLogoDecoration 객체를 통해 Flutter 아이콘 장식을 지정할 수 있습니다(별다른 기능은 없습니다),"]}]}
{"id": 88, "name": "ColorFiltered", "localName": "<PERSON><PERSON><PERSON><PERSON>", "info": "Kann ein Kindelement aufnehmen und das Element mit 29 Überblendungsmodi und beliebigen Komponenten mischen, so leistungsstark, dass ich nicht weiß, was ich sagen soll. Erfahren Sie mehr über die App, die mit einem Klick alles grau macht.", "lever": 5, "family": 2, "linkIds": [277, 38], "nodes": [{"file": "node1_base.dart", "name": "Grundlegende Verwendung von ColorFiltered", "desc": ["【child】 : Kindelement   【Widget】", "【colorFilter】 : Farbfilter   【ColorFilter】"]}]}
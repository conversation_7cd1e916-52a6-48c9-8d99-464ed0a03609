{"id": 88, "name": "ColorFiltered", "localName": "Filtro de Cor", "info": "Pode conter um componente filho e pode misturar o componente com qualquer outro componente em 29 modos de sobreposição de cores, tão poderoso que não sei o que dizer. Saiba mais sobre o aplicativo que transforma tudo em cinza com um toque.", "lever": 5, "family": 2, "linkIds": [277, 38], "nodes": [{"file": "node1_base.dart", "name": "Uso Básico do ColorFiltered", "desc": ["【child】 : Compo<PERSON><PERSON> filho   【Widget】", "【colorFilter】 : Filtro de cor   【ColorFilter】"]}]}
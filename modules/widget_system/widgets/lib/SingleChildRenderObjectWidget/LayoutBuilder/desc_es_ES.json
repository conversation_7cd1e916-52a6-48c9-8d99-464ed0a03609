{"id": 287, "name": "LayoutBuilder", "localName": "<PERSON>st<PERSON><PERSON> de diseño", "info": "Puede detectar el tamaño del área del contenedor padre y completar el diseño personalizado según la información de tamaño del contenedor padre. Es un componente de diseño muy práctico.", "lever": 4, "family": 2, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "Conocimiento básico de LayoutBuilder", "desc": ["【builder】: Const<PERSON>ctor de diseño   【LayoutWidgetBuilder】"]}, {"file": "node2_fit.dart", "name": "Adaptación de diseño de LayoutBuilder", "desc": ["Puede diseñar la presentación de componentes según el tamaño del área.", "<PERSON>r e<PERSON><PERSON><PERSON>, mostrar diferentes estructuras de diseño en áreas de diferentes anchos.", "Después de todo, en muchos lugares no es fácil obtener el área del componente padre, y usar LayoutBuilder será muy útil."]}, {"file": "node3_expend.dart", "name": "Uso extendido de LayoutBuilder", "desc": ["Usar TextPainter para detectar el número de líneas de texto y lograr la función de expandir o contraer."]}]}
{"id": 74, "name": "Padding", "localName": "Componente margine", "info": "P<PERSON>ò contenere un componente figlio, aggiungendo il proprio padding interno per limitare il posizionamento del componente figlio, la proprietà principale è padding.", "lever": 4, "family": 2, "linkIds": [1, 191], "nodes": [{"file": "node1_all.dart", "name": "Padding margine uguale su tutti i lati", "desc": ["【child】 : componente figlio   【Widget】", "【padding】 : margine interno su tutti i lati   【EdgeInsetsGeometry】\"", "EdgeInsets.all viene utilizzato per definire lo stesso margine su tutti i lati"]}, {"file": "node2_only.dart", "name": "Padding margine singolo", "desc": ["EdgeInsets.only viene utilizzato per definire lo stesso margine su tutti i lati"]}, {"file": "node3_symmetric.dart", "name": "Padding margine direzionale", "desc": ["EdgeInsets.symmetric viene utilizzato per definire i margini orizzontali e verticali"]}]}
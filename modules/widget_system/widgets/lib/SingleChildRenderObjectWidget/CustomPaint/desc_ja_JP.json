{"id": 166, "name": "CustomPaint", "localName": "描画コンポーネント", "info": "CustomPainterを使用して描画することで、複雑なカスタム描画コンポーネントを実現できます。これはFlutterのカスタムコンポーネントの中心的な存在です。", "lever": 5, "family": 2, "linkIds": [], "nodes": [{"file": "node1_clock.dart", "name": "CustomPaintで線と図形を描画", "desc": ["【painter】 : ペインター   【CustomPainter】"]}, {"file": "node2_bezier.dart", "name": "CustomPaintでベジェ曲線を描画", "desc": ["Flutterはベジェ曲線などの複雑な描画もサポートしています。"]}]}
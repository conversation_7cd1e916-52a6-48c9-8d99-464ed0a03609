{"id": 264, "name": "RepaintBoundary", "localName": "重绘边界", "info": "为子组件创建一个单独的显示列表,提升性能。源码中在TextField、DrawerController、Scrollbar、Sliver等组件中均有应用", "lever": 4, "family": 2, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "RepaintBoundary基本使用", "desc": ["【child】 : 子组件   【Widget】", "比如上面的绘制视图，即使shouldRepaint为false,在滑动中会也会不断执行paint方法,使用RepaintBoundary可以避免不必要的绘制。"]}, {"file": "node2_save.dart", "name": "保存Widget成为图片", "desc": ["通过RenderRepaintBoundary可以获取子组件的Image信息，从而获取字节保存为图片文件。"]}]}
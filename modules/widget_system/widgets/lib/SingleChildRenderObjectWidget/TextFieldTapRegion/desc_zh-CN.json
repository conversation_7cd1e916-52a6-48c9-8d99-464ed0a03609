{"id": 281, "name": "TextFieldTapRegion", "localName": "输入框点击范围", "info": "groupId 为 EditableText 的 TapRegion，可以让其他组件的点击范围与输入框视为一体。", "lever": 3, "family": 2, "linkIds": [280, 54, 245], "nodes": [{"file": "node1.dart", "name": "监听点击组件内部和外部", "desc": ["案例中点击加号和减号，不会取消输入框的焦点，键盘仍然可以输入。", "【enabled】 : 是否可用   【bool】", "【onTapOutside】 : 点击外界监听   【TapRegionCallback?】", "【onTapInside】 : 点击内部监听   【TapRegionCallback?】", "【groupId】 : 点击区域组标识   【Object?】"]}]}
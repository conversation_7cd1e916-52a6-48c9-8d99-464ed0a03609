{"id": 281, "name": "TextFieldTapRegion", "localName": "입력란 클릭 범위", "info": "groupId가 EditableText인 TapRegion으로, 다른 컴포넌트의 클릭 범위를 입력란과 일체로 간주할 수 있습니다.", "lever": 3, "family": 2, "linkIds": [280, 54, 245], "nodes": [{"file": "node1.dart", "name": "컴포넌트 내부 및 외부 클릭 감지", "desc": ["예시에서 더하기 및 빼기 버튼을 클릭해도 입력란의 포커스가 해제되지 않으며, 키보드로 입력이 가능합니다.", "【enabled】 : 사용 가능 여부   【bool】", "【onTapOutside】 : 외부 클릭 감지   【TapRegionCallback?】", "【onTapInside】 : 내부 클릭 감지   【TapRegionCallback?】", "【groupId】 : 클릭 영역 그룹 식별자   【Object?】"]}]}
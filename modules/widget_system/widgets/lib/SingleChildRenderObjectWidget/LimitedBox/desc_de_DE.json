{"id": 79, "name": "LimitedBox", "localName": "Begrenzungsbox", "info": "Kann ein Unterelement aufnehmen und den Bereich des Unterelements durch Angabe der maximalen Breite und Höhe begrenzen.", "lever": 3, "family": 2, "linkIds": [80], "nodes": [{"file": "node1_base.dart", "name": "Grundlegende Verwendung von LimitedBox", "desc": ["【child】 : Unterelement   【Widget】", "【maxHeight】 : Maximale Höhe   【double】", "【maxWidth】 : Maximale Breite   【double】"]}]}
{"id": 278, "name": "<PERSON>drop<PERSON><PERSON><PERSON>", "localName": "背景フィルター", "info": "子を1つ収容し、背景にぼかしフィルターを適用します。Stackを使用して背景をぼかすことで、コンポーネントのぼかし効果を実現できます。", "lever": 4, "family": 2, "linkIds": [88, 97, 67], "nodes": [{"file": "node1_base.dart", "name": "BackdropFilterの基本的な使用法", "desc": ["【child】 : 子コンポーネント   【Widget】", "【filter】 : フィルター   【ImageFilter】", "ImageFilter.blurはガウスぼかしを実現し、x、yのぼかし係数を指定できます。"]}]}
{"id": 80, "name": "ConstrainedBox", "localName": "Ограниченный контейнер", "info": "Может содержать один дочерний компонент, ограничивая область размещения дочернего компонента путем указания минимальной и максимальной ширины и высоты.", "lever": 3, "family": 2, "linkIds": [1, 79, 81], "nodes": [{"file": "node1_base.dart", "name": "Основное использование BoxConstraints", "desc": ["【child】 : До<PERSON><PERSON><PERSON>ний компонент   【Widget】", "【minWidth】 : Минимальная ширина   【double】", "【minHeight】 : Минимальная высота   【double】", "【maxHeight】 : Максимальная высота   【double】", "【maxWidth】 : Максимальная ширина   【double】"]}]}
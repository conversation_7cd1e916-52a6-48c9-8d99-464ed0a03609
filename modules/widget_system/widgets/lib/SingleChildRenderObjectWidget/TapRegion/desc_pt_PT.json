{"id": 280, "name": "TapRegion", "localName": "<PERSON><PERSON>", "info": "Componente capaz de detectar retornos de chamada de toque interno ou externo, através do groupId várias áreas podem ser consideradas como uma só.", "lever": 4, "family": 2, "linkIds": [146, 54], "nodes": [{"file": "node1.dart", "name": "Monitorizar toques internos e externos do componente", "desc": ["【enabled】 : Se está disponível   【bool】", "【onTapOutside】 : Monitorização de toque externo   【TapRegionCallback?】", "【onTapInside】 : Monitorização de toque interno   【TapRegionCallback?】", "【groupId】 : Identificador do grupo de área de toque   【Object?】"]}]}
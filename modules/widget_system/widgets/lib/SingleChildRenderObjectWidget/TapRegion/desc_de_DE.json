{"id": 280, "name": "TapRegion", "localName": "Klickberei<PERSON>", "info": "Ko<PERSON>nent<PERSON>, die interne oder externe Klickrückrufe erkennen kann. Durch die groupId können mehrere Bereiche als eine Einheit betrachtet werden.", "lever": 4, "family": 2, "linkIds": [146, 54], "nodes": [{"file": "node1.dart", "name": "Interne und externe Klicks überwachen", "desc": ["【enabled】 : Verfügbar   【bool】", "【onTapOutside】 : Externer Klick-Listener   【TapRegionCallback?】", "【onTapInside】 : Interner Klick-Listener   【TapRegionCallback?】", "【groupId】 : Klickbereichsgruppenkennung   【Object?】"]}]}
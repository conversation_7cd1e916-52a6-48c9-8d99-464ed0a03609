{"id": 78, "name": "Transform", "localName": "Transformação", "info": "Pode acomodar um subcomponente, pode transformar o subcomponente através de uma matriz de transformação 4*4.", "lever": 4, "family": 2, "linkIds": [], "nodes": [{"file": "node1_skew.dart", "name": "Transformação de inclinação skew", "desc": ["A inclinação x é controlada pelo número R0C1, o parâmetro de entrada é o valor em radianos, indicando o ângulo de inclinação", "A inclinação y é controlada pelo número R1C0, o parâmetro de entrada é o valor em radianos, indicando o ângulo de inclinação"]}, {"file": "node2_translation.dart", "name": "Transformação de translação translationValues", "desc": ["A translação x é controlada pelo número R0C3, o parâmetro de entrada é um valor numérico, indicando o comprimento da translação", "A translação y é controlada pelo número R1C3, o parâmetro de entrada é um valor numérico, indicando o comprimento da translação", "A translação z é controlada pelo número R2C3, o parâmetro de entrada é um valor numérico, indicando o comprimento da translação"]}, {"file": "node3_scale.dart", "name": "Transformação de escala diagonal3Values", "desc": ["A escala x é controlada pelo número R0C0, o parâmetro de entrada é um valor numérico, indicando a taxa de escala", "A escala y é controlada pelo número R1C2, o parâmetro de entrada é um valor numérico, indicando a taxa de escala", "A escala z é controlada pelo número R2C2, o parâmetro de entrada é um valor numérico, indicando a taxa de escala"]}, {"file": "node4_rotate.dart", "name": "Transformação de rotação rotation", "desc": ["A rotação x é controlada por R1C1, R1C2, R2C1, R2C2, o parâmetro de entrada representa radianos", "A rotação y é controlada por R0C0, R0C2, R2C0, R2C2, o parâmetro de entrada representa radianos", "A rotação z é controlada por R0C0, R0C1, R1C0, R1C1"]}, {"file": "node5_perspective.dart", "name": "Transformação de perspetiva rotation", "desc": ["A perspetiva é controlada por R3C1, R3C2, R3C3"]}]}
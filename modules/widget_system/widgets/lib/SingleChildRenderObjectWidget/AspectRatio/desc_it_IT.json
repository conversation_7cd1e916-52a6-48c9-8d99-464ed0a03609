{"id": 77, "name": "AspectRatio", "localName": "Scatola delle proporzioni", "info": "Può contenere un componente figlio, limitando l'area del componente figlio specificando il rapporto larghezza-altezza aspectRatio.", "lever": 3, "family": 2, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "Uso di base di AspectRatio", "desc": ["【child】 : Componente figlio   【Widget】", "【aspectRatio】 : <PERSON><PERSON><PERSON> la<PERSON>-altezza   【double】"]}]}
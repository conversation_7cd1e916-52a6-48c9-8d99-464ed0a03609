{"id": 265, "name": "CompositedTransformFollower", "localName": "合成変換フォロワー", "info": "通常、CompositedTransformTarget コンポーネントと組み合わせて使用され、Overlay をターゲットの変換に伴わせることができます。", "lever": 3, "family": 2, "linkIds": [266, 182], "nodes": [{"file": "node1_base.dart", "name": "CompositedTransformFollower 使用", "desc": ["【child】 : 子コンポーネント   【Widget】", "【link】 : リンク   【LayerLink】", "【offset】 : オフセット   【Offset】", "【targetAnchor】 : ターゲットアンカー   【Alignment】", "【followerAnchor】 : フォロワーアンカー   【Alignment】", "【showWhenUnlinked】 : リンクされていない場合に表示するか   【bool】"]}]}
{"id": 283, "name": "CallbackShortcuts", "localName": "Accesos directos de devolución de llamada", "info": "Se pueden configurar combinaciones como accesos directos, que responderán a eventos de teclas rápidas después de obtener el foco.", "lever": 3, "family": 2, "linkIds": [282, 284], "nodes": [{"file": "node1.dart", "name": "Uso de accesos directos", "desc": ["En el caso, después de activar el foco, las combinaciones de teclas Ctrl+↑ y Ctrl+↓ pueden aumentar o disminuir el número", "【enabled】: Si está disponible 【bool】", "【onTapOutside】: Escucha de clic fuera 【TapRegionCallback?】", "【onTapInside】: Escucha de clic dentro 【TapRegionCallback?】", "【groupId】: Identificador del grupo de área de clic 【Object?】"]}]}
{"id": 283, "name": "CallbackShortcuts", "localName": "快捷键回调", "info": "可以设置组合作为快捷键，在获取焦点后, 响应快捷键事件。", "lever": 3, "family": 2, "linkIds": [282, 284], "nodes": [{"file": "node1.dart", "name": "快捷键使用", "desc": ["案例中激活焦点后，Ctrl+↑  和 Ctrl+↓ 组合键可以增加或减少数字", "【enabled】 : 是否可用   【bool】", "【onTapOutside】 : 点击外界监听   【TapRegionCallback?】", "【onTapInside】 : 点击内部监听   【TapRegionCallback?】", "【groupId】 : 点击区域组标识   【Object?】"]}]}
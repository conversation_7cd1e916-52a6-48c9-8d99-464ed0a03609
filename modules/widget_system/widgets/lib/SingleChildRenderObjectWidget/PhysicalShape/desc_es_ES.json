{"id": 279, "name": "PhysicalShape", "localName": "Forma Física", "info": "Permite que los componentes hijos se recorten según una ruta, y se puede especificar el color de fondo, la profundidad de la sombra, el color de la sombra y el comportamiento de recorte.", "lever": 4, "family": 2, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "Uso básico de PhysicalShape", "desc": ["【clipper】: Recortador   【CustomClipper<Path>】", "【clipBehavior】: Comportamiento de recorte   【Clip】", "【child】: <PERSON>mpo<PERSON><PERSON> hijo   【Widget】", "【elevation】: Profundidad de la sombra   【double】", "【shadowColor】: Color de la sombra   【Color】", "【color】: Color    【Color】"]}]}
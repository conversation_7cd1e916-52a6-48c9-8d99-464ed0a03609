{"id": 67, "name": "ClipRect", "localName": "<PERSON><PERSON><PERSON><PERSON>", "info": "Kann ein Unterelement aufnehmen und es rechteckig zuschneiden. Kann mit SizedBox, <PERSON><PERSON>, AspectRadio usw. eingeschränkt werden, um den Bereich zu begrenzen.", "lever": 3, "family": 2, "linkIds": [66, 68, 69], "nodes": [{"file": "node1_base.dart", "name": "Grundlegende Verwendung von ClipRect", "desc": ["【child】 : Unterelement   【Widget】", "【clipBehavior】 : Zuschneideverhalten   【Clip】", "【clipper】 : Zuschneider   【CustomClipper<Rect>】"]}]}
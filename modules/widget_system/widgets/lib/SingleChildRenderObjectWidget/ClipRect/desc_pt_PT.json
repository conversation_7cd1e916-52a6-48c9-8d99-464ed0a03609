{"id": 67, "name": "ClipRect", "localName": "Recorte Retangular", "info": "Pode conter um componente filho e cortá-lo em forma retangular. Pode ser delimitado com componentes como SizedBox, Align, AspectRadio, etc.", "lever": 3, "family": 2, "linkIds": [66, 68, 69], "nodes": [{"file": "node1_base.dart", "name": "Uso Básico do ClipRect", "desc": ["【child】 : <PERSON><PERSON><PERSON><PERSON>   【Widget】", "【clipBehavior】 : Comportamento de Corte   【Clip】", "【clipper】 : Cortador   【CustomClipper<Rect>】"]}]}
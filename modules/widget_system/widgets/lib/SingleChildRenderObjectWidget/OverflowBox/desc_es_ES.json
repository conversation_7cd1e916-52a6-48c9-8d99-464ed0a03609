{"id": 83, "name": "OverflowBox", "localName": "Caja de desbordamiento", "info": "<PERSON>uede contener un componente hijo, y el componente hijo puede desbordar el área del componente padre. Se pueden especificar áreas máximas y mínimas de ancho y alto para limitar, y tiene la propiedad de alineación alignment.", "lever": 4, "family": 2, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "Uso básico de OverflowBox", "desc": ["【child】 : <PERSON>mpo<PERSON><PERSON> hijo   【Widget】", "【minWidth】 : <PERSON><PERSON> m<PERSON>   【double】", "【minHeight】 : <PERSON><PERSON> mínima   【double】", "【maxHeight】 : <PERSON><PERSON> máxima   【double】", "【maxWidth】 : <PERSON><PERSON> m<PERSON>xi<PERSON>   【double】", "【alignment】 : Modo de alineación   【AlignmentGeometry】"]}]}
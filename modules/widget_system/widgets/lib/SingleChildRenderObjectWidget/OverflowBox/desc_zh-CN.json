{"id": 83, "name": "OverflowBox", "localName": "溢出盒", "info": "可容纳一个子组件，且子组件允许溢出父组件区域，可以指定宽高的最大最小区域进行限定，拥有对齐属性alignment。", "lever": 4, "family": 2, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "OverflowBox基本使用", "desc": ["【child】 : 孩子组件   【Widget】", "【minWidth】 : 最小宽   【double】", "【minHeight】 : 最小高   【double】", "【maxHeight】 : 最大高   【double】", "【maxWidth】 : 最大宽   【double】", "【alignment】 : 对齐方式   【AlignmentGeometry】"]}]}
{"id": 297, "name": "IntrinsicWidth", "localName": "<PERSON><PERSON><PERSON>", "info": "Componente que ajusta o tamanho dos seus elementos filhos com base na largura intrínseca dos mesmos, resolvendo muitos problemas de layout, mas relativamente caro.", "lever": 4, "family": 2, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "Uso Básico do IntrinsicWidth", "desc": ["【child】 : Compo<PERSON><PERSON> filho   【Widget】", "Como no exemplo: a largura acima pode variar, a largura do meio é fixa, e a largura abaixo assume o valor mais alto dos dois anteriores."]}]}
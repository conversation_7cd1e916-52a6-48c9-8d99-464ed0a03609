{"id": 297, "name": "IntrinsicWidth", "localName": "Intrinsische Breite", "info": "<PERSON><PERSON> Komponente, die die Größe ihrer Kinder basierend auf deren intrinsischer Breite anpasst, kann viele Layoutprobleme lösen, ist jedoch relativ teuer.", "lever": 4, "family": 2, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "Grundlegende Verwendung von IntrinsicW<PERSON>th", "desc": ["【child】: Kindkomponente 【Widget】", "Wie im Beispiel: Die Breite oben kann variieren, die Breite in der Mitte ist fest, und die Breite unten nimmt den höchsten Wert der beiden vorherigen."]}]}
{"id": 266, "name": "CompositedTransformTarget", "localName": "Objetivo de transformación compuesta", "info": "Generalmente se usa junto con el componente CompositedTransformFollower, lo que permite que el Overlay siga la transformación del objetivo.", "lever": 3, "family": 2, "linkIds": [265, 182], "nodes": [{"file": "node1_base.dart", "name": "Uso de CompositedTransformTarget", "desc": ["【child】 : <PERSON>mpo<PERSON><PERSON> hijo   【Widget】", "【link】 : Enlace   【LayerLink】"]}]}
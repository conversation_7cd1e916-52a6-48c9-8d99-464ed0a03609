import 'package:flutter/material.dart';
/// create by 张风捷特烈 on 2020-04-19
/// contact me <NAME_EMAIL>

class CustomBaseline extends StatefulWidget {
  const CustomBaseline({Key? key}) : super(key: key);

  @override
  _CustomBaselineState createState() => _CustomBaselineState();
}

class _CustomBaselineState extends State<CustomBaseline> {
  double _baseline=20;

  @override
  Widget build(BuildContext context) {
    Widget childBox = const Text(
      '你好,Flutter',
      style: TextStyle(fontSize: 20, fontFamily: "<PERSON><PERSON>"),
    );


    Widget baseline = Baseline(
        child: childBox,
        baseline: _baseline,
        baselineType: TextBaseline.alphabetic);

    return Column(
      children: <Widget>[
        _buildSlider(),
        Container(
          width: 100/0.618,
          height: 100,
          color: Colors.grey.withAlpha(22),
          child: baseline,
        ),
      ],
    );
  }

  Widget _buildSlider() => Slider(
        divisions: 20,
        min: 0,
        max: 60,
        label: _baseline.toString(),
        value: _baseline,
        onChanged: (v) => setState(() => _baseline = v),
      );
}

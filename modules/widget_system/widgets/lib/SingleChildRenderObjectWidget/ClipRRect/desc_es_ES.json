{"id": 68, "name": "ClipRRect", "localName": "Recorte rectangular con esquinas redondeadas", "info": "Puede contener un componente hijo y realizar un recorte rectangular con esquinas redondeadas. Especifica borderRadius como el radio de las esquinas.", "lever": 3, "family": 2, "linkIds": [66, 67, 69], "nodes": [{"file": "node1_base.dart", "name": "Uso básico de ClipRRect", "desc": ["【child】 : <PERSON>mpo<PERSON><PERSON> hijo   【Widget】", "【borderRadius】 : Radio del borde   【BorderRadius】", "【clipBehavior】 : Comportamiento de recorte   【Clip】", "【clipper】 : Recortador   【CustomClipper<Rect>】"]}]}
{"id": 24, "name": "CupertinoButton", "localName": "iOS 버튼", "info": "iOS 스타일의 버튼. 색상, 클릭 시 투명도, 내부 여백, 모서리 반경 등을 지정할 수 있습니다. 클릭 이벤트를 수신할 수 있습니다.", "lever": 3, "family": 1, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "CupertinoButton 클릭 이벤트", "desc": ["【color】: 색상   【Color】", "【pressedOpacity】: 눌렀을 때 투명도   【double】", "【child】: 자식 위젯   【Widget】", "【padding】: 내부 여백   【EdgeInsetsGeometry】", "【borderRadius】: 모서리 반경   【BorderRadius】", "【onPressed】: 클릭 이벤트   【Function】"]}]}
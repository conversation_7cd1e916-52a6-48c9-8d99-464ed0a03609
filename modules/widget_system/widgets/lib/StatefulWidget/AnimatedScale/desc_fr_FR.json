{"id": 249, "name": "AnimatedScale", "localName": "Animation de mise à l'échelle", "info": "Lorsque la quantité de mise à l'échelle donnée change, le composant enfant peut s'ajuster automatiquement par rapport à la valeur de mise à l'échelle, et les valeurs avant et après ont une animation.", "lever": 3, "family": 1, "linkIds": [120, 247, 201], "nodes": [{"file": "node1.dart", "name": "Effet d'animation de mise à l'échelle", "desc": ["Dans ce cas, faites glisser pour ajuster le paramètre scale et observez l'effet de l'animation.", "【child】 : <PERSON><PERSON><PERSON><PERSON> enfant   【Widget】", "【duration】 : Du<PERSON>e de l'animation   【Duration】", "【onEnd】 : Rappel de fin d'animation   【Function()】", "【curve】 : Courbe d'animation   【Duration】", "【alignment】 : Centre de transformation de l'animation   【Alignment】", "【filterQuality】 : Qualité du filtre   【FilterQuality】", "【scale】 : Quantité de mise à l'échelle   【double】"]}]}
{"id": 46, "name": "CircularProgressIndicator", "localName": "Indicador de Progresso Circular", "info": "Exibição de progresso circular, pode especificar atributos como cor, largura da linha, progresso, etc. Quando o valor é nulo, ele gira continuamente.", "lever": 3, "family": 1, "linkIds": [47, 48], "nodes": [{"file": "node1_base.dart", "name": "Uso Básico do CircularProgressIndicator", "desc": ["【value】 : Progresso   【double】", "【backgroundColor】 : Cor de Fundo   【Color】", "【valueColor】 : Cor do Progresso   【Animation<Color>】", "【strokeWidth】 : <PERSON><PERSON><PERSON>   【double】"]}]}
{"id": 223, "name": "DropdownButtonFormField", "localName": "Form Dropdown", "info": "It is implemented based on DropdownButton, so it has similar basic properties. However, it has the characteristics of FormField and can call back the onSaved and validator methods.", "lever": 2, "family": 1, "linkIds": [55, 222], "nodes": [{"file": "node1_base.dart", "name": "Simple Usage of Form Dropdown", "desc": ["【items】: List of child components 【List<DropdownMenuItem<T>>】", "【validator】: Form validation callback 【FormFieldValidator<T>】", "【onSaved】: Form save callback 【FormFieldSetter<T>】", "For other properties, refer to DropdownButton. For form validation features, refer to FormField."]}]}
{"id": 194, "name": "Sc<PERSON><PERSON>", "localName": "Sc<PERSON><PERSON>", "info": "It needs to wrap a scrollable area. When it is scrollable, a scroll bar will be displayed for indication.", "lever": 3, "family": 1, "linkIds": [195, 164, 162], "nodes": [{"file": "node1_base.dart", "name": "Basic Usage of Scrollbar", "desc": ["【child】: Child widget 【Widget】", "【controller】: Controller 【ScrollController】"]}]}
{"id": 226, "name": "TweenAnimationBuilder", "localName": "Construtor de Animação de Interpolação", "info": "Realiza animações de interpolação nas propriedades relacionadas através do interpolador Tween, utilizando o builder para construir localmente e reduzir o escopo de atualização. Não é necessário personalizar o animador, podendo especificar a duração da animação, a curva e o callback de término.", "lever": 3, "family": 1, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "Exemplo de uso do TweenAnimationBuilder", "desc": ["【tween】 : *Interpolador   【Tween<T>】", "【duration】 : *Duração   【Duration】", "【builder】 : *Construtor   【ValueWidgetBuilder<T>】", "【curve】 : Curva de animação   【Curve】", "【onEnd】 : Callback de término   【VoidCallback】", "【child】 : Compo<PERSON><PERSON> filho   【Widget】"]}]}
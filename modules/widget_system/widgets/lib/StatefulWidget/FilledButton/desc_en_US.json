{"id": 359, "name": "FilledButton", "localName": "Filled <PERSON><PERSON>", "info": "A filled button that conforms to Material Design, with styles set via FilledButtonTheme.", "lever": 4, "family": 1, "linkIds": [354, 355, 353], "nodes": [{"file": "node1.dart", "name": "Basic Usage", "desc": ["【child】: Button content 【Widget】", "【onPressed】: Click event 【VoidCallback】", "【onHover】: Long press event 【ValueChanged<bool>? 】", "【onLongPress】: Long press event 【VoidCallback?】", "The filled button is visually second only to [FloatingActionButton] and is used for important, finalizing actions such as save, join now, or confirm."]}, {"file": "node2.dart", "name": "<PERSON><PERSON>", "desc": ["FilledButton.tonal is a tonal filled button, visually between [FilledButton] and [OutlinedButton], suitable for scenarios requiring slightly stronger emphasis than an outlined button but with lower priority. For example, a [Next] button."]}]}
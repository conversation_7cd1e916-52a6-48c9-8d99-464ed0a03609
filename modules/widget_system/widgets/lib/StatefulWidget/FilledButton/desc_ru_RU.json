{"id": 359, "name": "FilledButton", "localName": "Заполненная кнопка", "info": "Кнопка, соответствующая Material Design, стиль которой задается через FilledButtonTheme.", "lever": 4, "family": 1, "linkIds": [354, 355, 353], "nodes": [{"file": "node1.dart", "name": "Основное использование", "desc": ["【child】 : Содержимое кнопки   【Widget】", "【onPressed】 : Событие нажатия   【VoidCallback】", "【onHover】 : Событие наведения   【ValueChanged<bool>? 】", "【onLongPress】 : Событие долгого нажатия   【VoidCallback?】", "Заполненная кнопка визуально уступает только [FloatingActionButton] и используется для важных, завершающих действий, таких как: сохранение, немедленное присоединение или подтверждение."]}, {"file": "node2.dart", "name": "Тональный вариант", "desc": ["FilledButton.tonal - это тональная заполненная кнопка, визуально находящаяся между [FilledButton] и [OutlinedButton], подходит для сценариев, где требуется немного больше акцента, чем у контурной кнопки, но с более низким приоритетом. Например, кнопка [Далее]."]}]}
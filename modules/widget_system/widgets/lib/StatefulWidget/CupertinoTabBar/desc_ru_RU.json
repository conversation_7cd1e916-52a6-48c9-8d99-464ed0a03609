{"id": 63, "name": "CupertinoTabBar", "localName": "iOS вкладки", "info": "TabBar в стиле iOS, обычно используется в CupertinoTabScaffold. Можно указать цвет, размер иконок, границы и другие данные. Получает события кликов по элементам.", "lever": 3, "family": 1, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "Основное использование CupertinoTabBar", "desc": ["【currentIndex】 : текущий активный индекс   【Widget】", "【items】 : компоненты элементов   【Widget】", "【backgroundColor】 : цвет фона   【Color】", "【inactiveColor】 : цвет неактивного элемента   【Color】", "【activeColor】 : цвет активного элемента   【Color】", "【iconSize】 : размер иконки    【double】", "【border】 : граница   【Border】", "【onTap】 : событие клика   【Function(int)】"]}]}
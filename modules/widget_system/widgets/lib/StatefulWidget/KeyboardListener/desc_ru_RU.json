{"id": 284, "name": "KeyboardListener", "localName": "Слушатель клавиатуры", "info": "После получения фокуса, прослушивает события нажатия клавиш через onKeyEvent.", "lever": 5, "family": 1, "linkIds": [282, 283], "nodes": [{"file": "node1.dart", "name": "Прослушивание событий клавиатуры", "desc": ["В примере область получает фокус при нажатии, затем при нажатии клавиш можно увидеть информацию о срабатывании события.", "【focusNode】 : Фокус   【FocusNode】", "【autofocus】 : Автоматический фокус   【bool】", "【includeSemantics】 : Включать семантику   【bool】", "【onKeyEvent】 : Событие обратного вызова нажатия клавиши   【ValueChanged<KeyEvent>?】", "【child】 : До<PERSON><PERSON><PERSON>ний компонент   【Widget】"]}]}
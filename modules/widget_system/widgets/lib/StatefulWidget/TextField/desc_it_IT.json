{"id": 54, "name": "TextField", "localName": "Campo di testo", "info": "Componente per l'input, con proprietà complesse. Puoi specificare il controller, lo stile del testo, la decorazione della linea, il limite di righe, lo stile del cursore, ecc. Riceve eventi di cambiamento dell'input, completamento dell'input, ecc.", "lever": 5, "family": 1, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "Utilizzo di base di TextField", "desc": ["【controller】 : Controller   【TextEditingController】", "【style】 : <PERSON><PERSON> del testo   【TextStyle】", "【decoration】 : Decorazione della linea   【InputDecoration】", "【onEditingComplete】 : Evento di completamento dell'input   【Function()】", "【onSubmitted】 : Evento di invio   【Function(String)】", "【onChanged】 : <PERSON>o di input   【Function(String)】"]}, {"file": "node2_cursor.dart", "name": "Numero di righe e cursore di TextField", "desc": ["【minLines】 : Numero minimo di righe   【int】", "【maxLines】 : Numero massimo di righe   【int】", "【cursorRadius】 : Ra<PERSON> del cursore   【Radius】", "【cursorColor】 : Colore del cursore   【Color】", "【cursorWidth】 : <PERSON><PERSON><PERSON><PERSON> del cursore   【double】", "【showCursor】 : Most<PERSON>e il cursore   【bool】", "【autofocus】 : Autofocus   【bool】"]}, {"file": "node3_decoration.dart", "name": "Decorazione complessa di decoration", "desc": ["InputDecoration ha molti punti di decorazione, i dettagli sono nel codice:", "border: Relativo al bordo", "helper: <PERSON><PERSON><PERSON><PERSON> nell'angolo inferiore sinistro", "counter: <PERSON><PERSON><PERSON><PERSON> nell'angolo inferiore destro", "prefix: Parte più interna a sinistra del campo di input", "suffix: Parte più interna a destra del campo di input", "label: Testo senza focus", "label: Testo senza focus", "hint: Relativo al testo di suggerimento", "border: Relativo al bordo"]}]}
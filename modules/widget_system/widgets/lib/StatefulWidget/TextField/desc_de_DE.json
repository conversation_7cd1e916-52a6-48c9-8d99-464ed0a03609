{"id": 54, "name": "TextField", "localName": "Eingabefeld", "info": "Eine Komponente zur Eingabe mit komplexen Eigenschaften. Kann Controller, Textstil, Dekorationslinien, Zeilenbeschränkungen, Cursor-Stil usw. angeben. Empfängt Ereignisse wie Eingabeänderungen und Eingabeabschluss.", "lever": 5, "family": 1, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "Grundlegende Verwendung von TextF<PERSON>", "desc": ["【controller】 : Controller   【TextEditingController】", "【style】 : Textstil   【TextStyle】", "【decoration】 : Dekorationslinie   【InputDecoration】", "【onEditingComplete】 : Ereignis bei Eingabeabschluss   【Function()】", "【onSubmitted】 : Ereignis bei Übermittlung   【Function(String)】", "【onChanged】 : <PERSON><PERSON><PERSON><PERSON> bei Eingabe   【Function(String)】"]}, {"file": "node2_cursor.dart", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON> und <PERSON><PERSON><PERSON>", "desc": ["【minLines】 : Minimal<PERSON> Zeilenanzahl   【int】", "【maxLines】 : Maximale Zeilenanzahl   【int】", "【cursorRadius】 : <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>   【<PERSON>dius】", "【cursorColor】 : Cursor-Farbe   【Color】", "【cursorWidth】 : Cursor-Breite   【double】", "【showCursor】 : Cursor anzeigen   【bool】", "【autofocus】 : Automatischer Fokus   【bool】"]}, {"file": "node3_decoration.dart", "name": "Komplexe Dekoration von InputDecoration", "desc": ["InputDecoration hat viele Dekorationspunkte, siehe Code für Details:", "border: Bezogen auf den Rand", "helper: Bezogen auf die Hinweise unten links", "counter: Bezogen auf die Hinweise unten rechts", "prefix: Ganz links innerhalb des Eingabefelds", "suffix: Ganz rechts innerhalb des Eingabefelds", "label: Text ohne Fokus", "label: Text ohne Fokus", "hint: Bezogen auf Hinweistext", "border: Bezogen auf den Rand"]}]}
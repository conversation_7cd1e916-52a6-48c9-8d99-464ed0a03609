{"id": 123, "name": "AnimatedContainer", "localName": "Animazione del contenitore", "info": "Raccoglie alignment, padding, color, decoration, width, height, constraints, margin, transform in un unico componente. Tutti questi attributi possono essere animati, è possibile specificare la durata e la curva, e c'è un evento di fine animazione.", "lever": 5, "family": 1, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "Uso di base di AnimatedContainer", "desc": ["【child】 : Componente figlio   【Widget】", "【duration】 : Durata dell'animazione   【Duration】", "【onEnd】 : Callback di fine animazione   【Function()】", "【curve】 : Curva dell'animazione   【Duration】", "【color】 : Colore   【Color】", "【width】 : Larg<PERSON>zza   【double】", "【height】 : Altezza   【double】", "【alignment】 : Allineamento   【AlignmentGeometry】", "【decoration】 : Decorazione   【Decoration】", "【constraints】 : Vincoli   【BoxConstraints】", "【transform】 : Trasformazione   【Matrix4】", "【margin】 : <PERSON><PERSON>e esterno   【EdgeInsetsGeometry】", "【padding】 : <PERSON><PERSON><PERSON> interno   【EdgeInsetsGeometry】"]}]}
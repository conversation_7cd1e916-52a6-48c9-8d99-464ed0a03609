{"id": 123, "name": "AnimatedContainer", "localName": "Анимация контейнера", "info": "Объединяет alignment, padding, color, decoration, width, height, constraints, margin, transform. Все эти свойства могут быть анимированы, можно указать длительность и кривую, есть событие завершения анимации.", "lever": 5, "family": 1, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "Основное использование AnimatedContainer", "desc": ["【child】 : До<PERSON><PERSON><PERSON>ний компонент   【Widget】", "【duration】 : Длительность анимации   【Duration】", "【onEnd】 : Обратный вызов завершения анимации   【Function()】", "【curve】 : Кривая анимации   【Duration】", "【color】 : Цвет   【Color】", "【width】 : <PERSON><PERSON><PERSON><PERSON><PERSON>   【double】", "【height】 : Высота   【double】", "【alignment】 : Выравнивание   【AlignmentGeometry】", "【decoration】 : Декорация   【Decoration】", "【constraints】 : Ограничения   【BoxConstraints】", "【transform】 : Преобразование   【Matrix4】", "【margin】 : Вне<PERSON>ний отступ   【EdgeInsetsGeometry】", "【padding】 : Внутренний отступ   【EdgeInsetsGeometry】"]}]}
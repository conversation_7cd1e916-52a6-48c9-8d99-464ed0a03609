{"id": 122, "name": "AnimatedPositionedDirectional", "localName": "Richtungsbasierte Positionierungsanimation", "info": "Ermöglicht es Kindkomponenten, eine PositionedDirectional (richtungsbasierte Positionierung) Animation durchzuführen, wobei Dauer und Kurve angegeben werden können und ein Ereignis am Ende der Animation vorhanden ist. Kann nur innerhalb eines Stacks verwendet werden.", "lever": 3, "family": 1, "linkIds": [121, 159], "nodes": [{"file": "node1_base.dart", "name": "Grundlegende Verwendung von AnimatedPositionedDirectional", "desc": ["【child】 : Kindkomponente   【Widget】", "【duration】 : Animationsdauer   【Duration】", "【onEnd】 : Rückruf am Ende der Animation   【Function()】", "【curve】 : Animationskurve   【Duration】", "【top】 : Abstand zum oberen Rand des Elternteils   【double】", "【end】 : Abstand zum rechten Rand des Elternteils   【double】", "【start】 : Abstand zum linken Rand des Elternteils   【double】", "【bottom】 : Abstand zum unteren Rand des Elternteils   【double】"]}]}
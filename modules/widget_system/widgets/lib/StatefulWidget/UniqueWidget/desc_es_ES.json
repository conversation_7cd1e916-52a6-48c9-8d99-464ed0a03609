{"id": 243, "name": "UniqueWidget", "localName": "Componente Único", "info": "Clase abstracta, debe proporcionar una GlobalKey para la identificación. Este tipo de componente solo se inflará en una instancia y solo tendrá un estado en un momento dado. El estado se puede obtener a través de la propiedad currentState.", "lever": 1, "family": 1, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "Introducción a UniqueWidget", "desc": ["【child】 : <PERSON>mpo<PERSON><PERSON> hijo   【Widget】"]}]}
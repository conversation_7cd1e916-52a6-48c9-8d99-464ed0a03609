{"id": 57, "name": "AppBar", "localName": "Anwendungsleist<PERSON>", "info": "Eine allgemeine Struktur für die obere Leiste einer Anwendung, in der entsprechende Komponenten an bestimmten Stellen platziert werden können. Wird häufig in Scaffold-Komponenten verwendet.", "lever": 4, "family": 1, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "Grundlegende Verwendung von AppBar", "desc": ["【leading】 : <PERSON><PERSON>   【Widget】", "【title】 : <PERSON><PERSON><PERSON>   【Widget】", "【actions】 : <PERSON><PERSON><PERSON> Komponente   【List<Widget>】", "【elevation】 : Sc<PERSON>tentiefe   【double】", "【shape】 : Form   【ShapeBorder】", "【backgroundColor】 : Hintergrundfarbe   【Hintergrundfarbe】", "【centerTitle】 : Zentrierung in der Mitte   【bool】"]}, {"file": "node2_tab.dart", "name": "<PERSON>er<PERSON><PERSON><PERSON> von AppBar mit TabBar und TabBarView", "desc": ["【bottom】 : Untere Komponente   【PreferredSizeWidget】"]}]}
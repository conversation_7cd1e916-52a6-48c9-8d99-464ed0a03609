{"id": 251, "name": "NestedScrollView", "localName": "Visão de Rolagem Aninhada", "info": "Usado para o tratamento de rolagem aninhada de várias visualizações, pode especificar o cabeçalho, controlador de rolagem, direção de rolagem, etc., onde o corpo deve ser um componente do tipo rolável.", "lever": 4, "family": 4, "linkIds": [183, 344], "nodes": [{"file": "node1_base.dart", "name": "Uso Básico do NestedScrollView", "desc": ["【controller】 : Controlador de Rolagem   【ScrollController】", "【scrollDirection】 : Direção de Rolagem   【Axis】", "【reverse】 : Inverter   【bool】", "【physics】 : <PERSON><PERSON><PERSON>   【ScrollPhysics】", "【dragStartBehavior】 : Comportamento de Início de Arrasto   【DragStartBehavior】", "【headerSliverBuilder】 : *Construtor de Cabeçalho   【NestedScrollViewHeaderSliversBuilder】", "【body】 : *Conte<PERSON>do   【Widget】"]}]}
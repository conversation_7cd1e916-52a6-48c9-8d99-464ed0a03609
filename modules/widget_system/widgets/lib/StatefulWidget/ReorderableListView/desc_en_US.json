{"id": 177, "name": "ReorderableListView", "localName": "Reorderable List", "info": "A ListView that can be reordered by long-pressing, with properties such as scroll direction, reverse, and scroll controller.", "lever": 4, "family": 1, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "Basic Usage of ReorderableListView", "desc": ["【children】 : List of child components   【List<Widget>】", "【header】 : Header component   【Widget】", "【padding】 : Padding   【EdgeInsets】", "【onReorder】 : Callback when reordering  【ReorderCallback】"]}, {"file": "node2_direction.dart", "name": "Scroll Direction of ReorderableListView", "desc": ["【scrollDirection】 : Scroll direction   【Axis】", "【reverse】 : Whether to reverse  【bool】"]}]}
{"id": 135, "name": "MonthPicker", "localName": "月份选择器", "info": "月份的选择组件，自带上下月切换的监听。可指定选择的日期范围、选中日期等，接收日期选中事件。", "lever": 3, "family": 1, "deprecated": -1, "linkIds": [134, 136], "nodes": [{"file": "node1_base.dart", "name": "MonthPicker基本使用", "desc": ["【selectedDate】 : 选中日期   【DateTime】", "【firstDate】 : 最前日期限制   【DateTime】", "【lastDate】 : 最后日期限制   【DateTime】", "【onChanged】 : 点击回调  【Function(DateTime)】", "    ", "class CustomMonthPicker extends StatelessWidget{", "  const CustomMonthPicker({Key? key) : super(key: key);", "", "  final String info =", "      'MonthPicker 月份期选择器于 Flutter3.0 退出历史舞台。取代者为 CalendarDatePicker 日历选择器。';", "", "  @override", "  Widget build(BuildContext context) {", "    return Container(", "      color: Colors.blue.withOpacity(0.1),", "      padding: const EdgeInsets.all(10),", "      margin: const EdgeInsets.all(10),", "      child: Text(info),", "    );", "  ", "", "   final DateTime _date = DateTime.now();", "  ", "   @override", "   Widget build(BuildContext context) {", "     return SizedBox(", "       height: 350,", "       child: <PERSON><PERSON><PERSON>(", "         selectedDate: _date,", "         onChanged: (date) => setState(() => _date = date),", "         firstDate: DateTime(2018),", "         lastDate: DateTime(2030),", "       ),"]}]}
{"id": 358, "name": "NavigationRail", "localName": "Seitennavigation", "info": "Seitennavigationsleiste, wird im Allgemeinen für Desktop-Navigationsmenüs verwendet. Unterstützt erweiterbare und reduzierbare Bereiche, kann Komponenten für die Anfangsposition und Endposition angeben.", "lever": 4, "family": 1, "linkIds": [60, 61], "nodes": [{"file": "node1_base.dart", "name": "NavigationRail Grundlegende Verwendung", "desc": ["【destinations】 : Menüdatenliste   【List<NavigationRailDestination>】", "【selectedIndex】: Aktivierter Index    【int】", "【labelType】: Label-Stil    【NavigationRailLabelType?】", "【onDestinationSelected】: <PERSON><PERSON><PERSON><PERSON><PERSON>-Ereignis    【ValueChanged<int>?】"]}, {"file": "node2_extend.dart", "name": "NavigationRail Falt-Effekt", "desc": ["【elevation】 : Sc<PERSON>tentiefe   【double】", "【leading】: Startkomponente    【Widget?】", "【trailing】: Endkomponente    【Widget?】", "【extended】: <PERSON><PERSON> er<PERSON><PERSON>t    【bool】"]}, {"file": "node3_dark.dart", "name": "NavigationRail Stil", "desc": ["【useIndicator】 : Ob Indikator angezeigt wird   【bool】", "【indicatorColor】: Indikatorfarbe    【Color?】", "【backgroundColor】: Hintergrundfarbe    【Color?】", "【labelType】: Label-Stil    【NavigationRailLabelType?】", "【selectedIconTheme】: Ausgewähltes Icon-Thema    【IconThemeData?】", "【unselectedIconTheme】: Nicht ausgewähltes Icon-Thema    【IconThemeData?】", "【selectedLabelTextStyle】: Ausgewählter Textstil    【TextStyle?】", "【unselectedLabelTextStyle】: Nicht ausgewählter Textstil    【TextStyle?】", "【minExtendedWidth】: <PERSON><PERSON>weiterte Breite    【double?】", "【minWidth】: Nicht erweiterte Breite    【double?】"]}]}
{"id": 222, "name": "FormField", "localName": "Formularfeld", "info": "Ein Formularfeld, das in der Form-Komponente verwendet werden muss, enthält ein Feld des generischen Typs T als Zustandsvariable. Aktualisierungen und Validierungen des Felds lösen entsprechende Rückrufe aus.", "lever": 2, "family": 1, "linkIds": [198, 199, 223], "nodes": [{"file": "node1_base.dart", "name": "Einführung in FormField", "desc": ["【builder】 : Inhaltsersteller   【FormFieldBuilder<T>】", "【initialValue】 : Anfangswert   【T】", "【validator】 : Validierungsfunktion   【FormFieldValidator<String> 】", "【enabled】 : Ist aktiviert   【bool】", "【onSaved】 : <PERSON><PERSON><PERSON><PERSON> beim Speichern des Formulars  【FormFieldSetter<String>】"]}]}
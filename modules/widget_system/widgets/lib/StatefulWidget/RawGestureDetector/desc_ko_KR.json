{"id": 248, "name": "RawGestureDetector", "localName": "원 제스처 감지기", "info": "주어진 제스처 팩토리 설명에 따라 제스처를 감지하는 데 사용할 수 있으며, 자신만의 제스처 인식기를 개발할 때 매우 유용합니다. 일반적인 제스처의 경우 GestureRecognizer를 사용하세요.", "lever": 3, "family": 1, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "RawGestureDetector 기본 사용법", "desc": ["【behavior】 : 감지 행동   【HitTestBehavior】", "【gestures】 : 제스처 매핑   【Map<Type, GestureRecognizerFactory>】", "【child】 : 자식 위젯   【Widget】"]}]}
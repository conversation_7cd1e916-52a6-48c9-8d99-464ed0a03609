{"id": 178, "name": "ExpansionPanelList", "localName": "Lista Espandibile", "info": "Componente lista espandibile, può essere implementato per espansione singola o multipla in base alla logica. È possibile specificare la durata dell'animazione di espansione e ricevere un callback di espansione", "lever": 3, "family": 1, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "Uso Base di ExpansionPanelList", "desc": ["【children】 : Lista dei componenti figli   【List<Widget>】", "【animationDuration】 : Durata dell'animazione   【Duration】", "【expansionCallback】 : Callback di espansione   【List<Widget>】", "【onPressed】 : <PERSON><PERSON> di click  【Function()】"]}]}
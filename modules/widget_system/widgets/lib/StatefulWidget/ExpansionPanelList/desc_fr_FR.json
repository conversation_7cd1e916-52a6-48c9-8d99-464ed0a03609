{"id": 178, "name": "ExpansionPanelList", "localName": "Liste de déploiement", "info": "Composant de liste extensible, pouvant être implémenté pour un déploiement unique ou multiple selon la logique. Permet de spécifier la durée de l'animation de déploiement et reçoit un rappel de déploiement.", "lever": 3, "family": 1, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "Utilisation de base de ExpansionPanelList", "desc": ["【children】 : Liste des composants enfants   【List<Widget>】", "【animationDuration】 : <PERSON><PERSON>e de l'animation   【Duration】", "【expansionCallback】 : <PERSON>pel de déploiement   【List<Widget>】", "【onPressed】 : Événement de clic  【Function()】"]}]}
{"id": 170, "name": "WillPopScope", "localName": "Return Intercept", "info": "When a WillPopScope component is present in an interface, a callback is triggered when the page returns, determining whether to return. It can be used for scenarios requiring secondary confirmation before exiting.", "lever": 5, "family": 1, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "WillPopScope Usage", "desc": ["【child】: Child component 【Widget】", "【onWillPop】: Return callback 【WillPopCallback】"]}]}
import 'package:flutter/cupertino.dart';

/// create by 张风捷特烈 on 2020-03-25
/// contact me <NAME_EMAIL>

class CustomCupertinoContextMenu extends StatelessWidget {
  const CustomCupertinoContextMenu({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: 100,
      height: 100,
      child: DecoratedBox(
          decoration: const BoxDecoration(
              image: DecorationImage(
                  image: AssetImage('assets/images/sabar_bar.webp'),
                  fit: BoxFit.cover),
              borderRadius: BorderRadius.all(Radius.circular(50))),
          child: _buildCupertinoContextMenu(context)),
    );
  }

  final List<String> info = const ['保存图片', '立刻呼叫', '添加到收藏夹'];

  Widget _buildCupertinoContextMenu(context) => CupertinoContextMenu(
      child: Container(
        decoration: const BoxDecoration(
            image: DecorationImage(
                image: AssetImage('assets/images/sabar_bar.webp'),
                fit: BoxFit.cover),
            borderRadius: BorderRadius.all(Radius.circular(50))),
      ),
      actions: info
          .map((e) => CupertinoContextMenuAction(
                child: Center(child: Text(e)),
                onPressed: () => Navigator.pop(context),
              ))
          .toList());
}

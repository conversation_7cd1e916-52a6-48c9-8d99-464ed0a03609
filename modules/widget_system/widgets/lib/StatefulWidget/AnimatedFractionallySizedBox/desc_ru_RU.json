{"id": 260, "name": "AnimatedFractionallySizedBox", "localName": "Анимированный FractionallySizedBox", "info": "Анимированная версия FractionallySizedBox, которая автоматически плавно переходит к целевому размеру и положению дочернего компонента в течение указанной продолжительности при изменении widthFactor или heightFactor, а также при изменении alignment.", "lever": 3, "family": 1, "linkIds": [82, 120, 123, 121], "nodes": [{"file": "node1.dart", "name": "Анимационный переход", "desc": ["В этом примере при нажатии изменяются параметры heightFactor, widthFactor и alignment, чтобы увидеть эффект анимации.", "【child】 : До<PERSON><PERSON><PERSON>ний компонент   【Widget】", "【duration】 : Длительность анимации   【Duration】", "【onEnd】 : Обратный вызов по завершении анимации   【Function()】", "【alignment】 : Центр анимационного преобразования   【Alignment】", "【curve】 : Кривая анимации   【Duration】", "【heightFactor】 : Коэффициент высоты   【double?】", "【widthFactor】 : Коэффициент ширины   【double?】", "【turns】 : Количество поворотов   【double】"]}]}
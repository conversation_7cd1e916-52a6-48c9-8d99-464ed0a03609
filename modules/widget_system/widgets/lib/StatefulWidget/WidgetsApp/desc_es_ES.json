{"id": 236, "name": "WidgetsApp", "localName": "Aplicación de Componentes", "info": "Agrupa los componentes necesarios para una aplicación, como el enrutamiento, el idioma, algunos interruptores de depuración, etc. También es el componente central para implementar MaterialApp y CupertinoApp.", "lever": 2, "family": 1, "linkIds": [65, 156], "nodes": [{"file": "node1_base.dart", "name": "Uso Básico de WidgetsApp", "desc": ["【pageRouteBuilder】 : *Constructor de rutas   【PageRouteFactory】", "【color】: *Color    【Color】", "【debugShowWidgetInspector】: Mostrar el inspector de componentes z   【bool】", "<PERSON><PERSON> propiedades son básicamente las mismas que MaterialApp, consulte allí para más detalles."]}]}
{"id": 120, "name": "AnimatedAlign", "localName": "Анимация выравнивания", "info": "Позволяет дочернему компоненту выполнять анимацию выравнивания (Align), можно указать продолжительность и кривую, есть событие завершения анимации.", "lever": 3, "family": 1, "linkIds": [85, 111], "nodes": [{"file": "node1_base.dart", "name": "Основное использование AnimatedAlign", "desc": ["【child】 : До<PERSON><PERSON><PERSON>ний компонент   【Widget】", "【duration】 : Продолжительность анимации   【Duration】", "【onEnd】 : Обратный вызов завершения анимации   【Function()】", "【alignment】 : Способ выравнивания   【AlignmentGeometry】", "【curve】 : Кривая анимации   【Duration】", "【padding】 : Внутренний отступ   【EdgeInsetsGeometry】"]}]}
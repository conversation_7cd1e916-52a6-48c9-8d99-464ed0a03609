{"id": 116, "name": "AnimatedSwitcher", "localName": "动画切换", "info": "当子组件变化时执行动画，需要指定子组件的key进行标识。动画方式可以自定义,能指定动画时长、动画曲线等属性。", "lever": 4, "family": 1, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "AnimatedSwitcher基本使用", "desc": ["【child】 : 孩子组件   【Widget】", "【duration】 : 动画时长  【Duration】", "【switchOutCurve】 : 切出曲线  【Curves】", "【switchInCurve】 : 切入曲线  【Curves】", "【switchInCurve】 : 切入曲线  【Curves】", "【transitionBuilder】 : 动画构造器  【Widget Function(Widget, Animation<double>)】"]}]}
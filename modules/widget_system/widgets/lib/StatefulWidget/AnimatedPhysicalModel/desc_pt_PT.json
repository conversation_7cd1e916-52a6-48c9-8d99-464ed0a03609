{"id": 225, "name": "AnimatedPhysicalModel", "localName": "Modelo Físico Animado", "info": "Componente PhysicalModel com efeitos de animação quando as propriedades relacionadas mudam, essencialmente uma combinação de PhysicalModel e animação. Pode especificar propriedades como sombra, profundidade de sombra, cantos arredondados, duração da animação, callback de término, etc.", "lever": 2, "family": 1, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "Uso Básico do AnimatedPhysicalModel", "desc": ["【color】 : Cor de fundo   【Color】", "【duration】 : Duração da animação   【Duration】", "【onEnd】 : Callback de término da animação   【Function()】", "【curve】 : Curva de animação   【Duration】", "【shape】 : Forma   【BoxShape】", "【elevation】 : Profundidade de sombra   【double】", "【borderRadius】 : Cantos arredondados   【BorderRadius】", "【shadowColor】 : <PERSON><PERSON> da sombra   【Color】", "【child】 : Compo<PERSON><PERSON> filho   【Widget】"]}]}
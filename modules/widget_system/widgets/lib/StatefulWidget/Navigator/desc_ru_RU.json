{"id": 232, "name": "Navigator", "localName": "Навигатор", "info": "Navigator управляет группой дочерних компонентов по правилам стека, может добавлять и удалять дочерние компоненты, а также отслеживать события входа и выхода из стека. Источник управления маршрутами MaterialApp использует Navigator.", "lever": 4, "family": 1, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "Основное использование Navigator", "desc": ["【initialRoute】 : Первоначальный маршрут отображения   【String】", "【onGenerateRoute】 : Генератор маршрутов   【RouteFactory】", "【observers】 : Наблюдатели маршрутов   【List<NavigatorObserver>】", "【onPopPage】 : Обратный вызов выхода из стека   【PopPageCallback】"]}]}
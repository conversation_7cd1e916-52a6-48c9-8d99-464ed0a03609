{"id": 370, "name": "DropdownMenu", "localName": "Menu a tendina", "info": "Componente di selezione a tendina, supporta il filtraggio tramite input di testo e consente la personalizzazione delle voci del menu. Si basa principalmente su MenuAnchor e TextFiled per l'implementazione.", "lever": 4, "family": 1, "linkIds": [], "nodes": [{"file": "node1.dart", "name": "Uso semplice del menu a tendina", "desc": ["【dropdownMenuEntries】 : Lista delle voci del menu   【List<DropdownMenuEntry<T>>】", "【initialSelection】 : Callback di convalida del modulo   【T?】", "【onSelected】 : Callback di salvataggio del modulo   【ValueChanged<T?>?】", "【menuHeight】 : <PERSON>ez<PERSON> del menu   【double】", "【width】 : Larghezza del campo di input   【double】"]}, {"file": "node2.dart", "name": "Impostazione dello stile del menu a tendina", "desc": ["【controller】 : Controller di input del testo   【TextEditingController?】", "【label】 : Etichetta del campo di input   【Widget?】", "【textStyle】 : Stile del testo del campo di input   【TextStyle?】", "【inputDecorationTheme】 : Tema di decorazione del campo di input   【InputDecorationTheme?】", "【leadingIcon】 : Icona a sinistra   【Widget?】", "【trailingIcon】 : Icona a destra quando il menu è espanso   【Widget?】", "【selectedTrailingIcon】 : Icona a destra quando il menu è espanso   【Widget?】", "【hintText】 : Testo di suggerimento del campo di input   【String?】", "【helperText】 : Testo di supporto del campo di input   【String?】", "【errorText】 : Testo di errore del campo di input   【String?】", "【menuStyle】 : Stile del menu a comparsa   【MenuStyle?】"]}, {"file": "node3.dart", "name": "Personalizzazione delle voci del menu a tendina", "desc": ["È possibile personalizzare la costruzione delle voci del menu utilizzando il labelWidget di DropdownMenuEntry."]}]}
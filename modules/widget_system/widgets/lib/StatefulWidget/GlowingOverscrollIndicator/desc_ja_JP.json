{"id": 250, "name": "GlowingOverscrollIndicator", "localName": "スクロールオーバーインジケーター", "info": "子要素がスクロール可能なリストで、上部または下部にスクロールした際のインジケーター効果です。色を指定できますが、あまり役に立ちません。AndroidとFuchsiaシステムのデフォルトのスクロール効果です。", "lever": 1, "family": 1, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "基本使用", "desc": ["【showLeading】 : ヘッダーが有効かどうか   【bool】", "【showTrailing】 : フッターが有効かどうか   【bool】", "【axisDirection】 : 軸方向   【AxisDirection】", "【color】 : 色   【Color】", "【child】 : 子ウィジェット   【Widget】"]}]}
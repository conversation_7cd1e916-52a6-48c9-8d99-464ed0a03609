{"id": 229, "name": "CupertinoTabView", "localName": "Página de Cupertino", "info": "<PERSON><PERSON>e mantener una pila de rutas como MaterialApp. Construye rutas a través de routes y onGenerateRoute, y puede escuchar rutas a través de navigatorObservers.", "lever": 3, "family": 1, "linkIds": [65, 158], "nodes": [{"file": "node1_base.dart", "name": "Uso básico de CupertinoTabView", "desc": ["【builder】 : Con<PERSON><PERSON><PERSON> de la página principal   【WidgetBuilder】", "【navigatorObservers】 : Observador de rutas   【List<NavigatorObserver>】", "【routes】 : Mapeo de rutas   【Map<String, WidgetBuilder>】", "【onGenerateRoute】 : Fábrica de rutas   【RouteFactory】"]}]}
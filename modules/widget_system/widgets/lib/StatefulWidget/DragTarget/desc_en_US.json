{"id": 104, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "localName": "Drag Target", "info": "A target area for dragging, which can receive information from the Draggable component. It can get callbacks during dragging.", "lever": 4, "family": 1, "linkIds": [103, 105], "nodes": [{"file": "node1_base.dart", "name": "Basic Usage of DragTarget", "desc": ["【builder】: Component builder 【DragTargetBuilder<T>】", "【onWillAccept】: When dragging in 【Function(T)】", "【onAccept】: Drag success 【Function(T)】", "【onLeave】: Drag in and then out 【Function(T)】"]}]}
{"id": 104, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "localName": "Цель перетаскивания", "info": "Область цели для перетаскивания, которая может принимать информацию от компонента Draggable. Можно получить обратные вызовы при перетаскивании.", "lever": 4, "family": 1, "linkIds": [103, 105], "nodes": [{"file": "node1_base.dart", "name": "Основное использование DragTarget", "desc": ["【builder】 : Конструктор компонента   【DragTargetBuilder<T>】", "【onWillAccept】 : При перетаскивании   【Function(T)】", "【onAccept】 : Успешное перетаскивание   【Function(T)】", "【onLeave】 : Перетаскивание и выход   【Function(T)】"]}]}
{"id": 117, "name": "AnimatedList", "localName": "动画列表", "info": "强化版的ListView,可以对item进行动画处理。比如在添加、删除是item的动画。", "lever": 3, "family": 1, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "AnimatedList基本使用", "desc": ["【itemBuilder】 : 组件构造器   【AnimatedListItemBuilder】", "【initialItemCount】 : 子组件数量   【int】", "【scrollDirection】 : 滑动方向   【Axis】", "【controller】 : 滑动控制器   【ScrollController】", "【reverse】 : 数据是否反向   【bool】", "【padding】 : 内边距   【EdgeInsetsGeometry】"]}]}
{"id": 117, "name": "AnimatedList", "localName": "애니메이션 리스트", "info": "강화된 ListView, 아이템에 애니메이션을 적용할 수 있습니다. 예를 들어 추가, 삭제 시 아이템의 애니메이션.", "lever": 3, "family": 1, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "AnimatedList 기본 사용", "desc": ["【itemBuilder】 : 컴포넌트 생성기   【AnimatedListItemBuilder】", "【initialItemCount】 : 하위 컴포넌트 수   【int】", "【scrollDirection】 : 스크롤 방향   【Axis】", "【controller】 : 스크롤 컨트롤러   【ScrollController】", "【reverse】 : 데이터 반전 여부   【bool】", "【padding】 : 내부 여백   【EdgeInsetsGeometry】"]}]}
{"id": 60, "name": "BottomNavigationBar", "localName": "底部导航", "info": "一个底部导航栏，通常用于Scaffold组件的底部，可指定颜色和模式，接受点击回调，可与PageView实现切页效果。", "lever": 4, "family": 1, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "BottomNavigationBar基本使用", "desc": ["【currentIndex】 : 当前索引   【int】", "【elevation】 : 影深   【double】", "【type】 : 类型*2   【BottomNavigationBarType】", "【fixedColor】 : type为fix的颜色   【Color】", "【backgroundColor】 : 背景色   【Color】", "【iconSize】 : 图标大小   【double】", "【selectedLabelStyle】 : 选中文字样式   【TextStyle】", "【unselectedLabelStyle】 : 未选中文字样式   【TextStyle】", "【showUnselectedLabels】 : 显示未选中标签   【bool】", "【showSelectedLabels】 : 显示选中标签   【bool】", "【items】 : 条目   【List<BottomNavigationBarItem>】", "【onTap】 : 点击事件   【Function(int)】"]}, {"file": "node2_page.dart", "name": "可结合PageView进行切页", "desc": ["在onTap时进行使用控制器进行切页"]}]}
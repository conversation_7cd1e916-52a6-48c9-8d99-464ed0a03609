{"id": 179, "name": "ListWheelScrollView", "localName": "Wheel List", "info": "A high-end cylindrical sliding list, exquisitely designed, allowing you to specify properties such as item height, perspective, and extrusion, and receive selection events during scrolling.", "lever": 4, "family": 1, "linkIds": [139, 291], "nodes": [{"file": "node1_base.dart", "name": "Basic Usage of ListWheelScrollView", "desc": ["【children】: List of child components 【List<Widget>】", "【perspective】: Perspective 【double】", "【itemExtent】: Item height 【EdgeInsets】", "【onSelectedItemChanged】: Selection callback 【ValueChanged<int>】"]}]}
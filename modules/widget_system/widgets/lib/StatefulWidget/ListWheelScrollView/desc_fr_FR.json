{"id": 179, "name": "ListWheelScrollView", "localName": "Liste à roulette", "info": "Une liste de défilement cylindrique haut de gamme, très ingénieu<PERSON>, permettant de spécifier des propriétés telles que la hauteur des éléments, la perspective, la compression, etc., et recevant des événements de sélection lors du défilement.", "lever": 4, "family": 1, "linkIds": [139, 291], "nodes": [{"file": "node1_base.dart", "name": "Utilisation de base de ListWheelScrollView", "desc": ["【children】 : Liste des composants enfants   【List<Widget>】", "【perspective】 : <PERSON><PERSON><PERSON> de perspective   【double】", "【itemExtent】 : Hauteur de l'élément   【EdgeInsets】", "【onSelectedItemChanged】 : Rappel de sélection  【ValueChanged<int>】"]}]}
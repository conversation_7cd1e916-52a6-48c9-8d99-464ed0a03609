{"id": 256, "name": "CupertinoSlidingSegmentedControl", "localName": "iOS Sliding Tabs", "info": "iOS-style sliding tabs, supporting click and slide to switch. You can specify tab color, background color, margin and other properties.", "lever": 3, "family": 1, "linkIds": [33, 262], "nodes": [{"file": "node1_base.dart", "name": "Basic Usage of iOS Sliding Tabs", "desc": ["【children】: Component Map   【Map<T, Widget>】", "【onValueChanged】: Value Change Callback   【ValueChanged<T>】", "【groupValue】: Selected Value   【T】", "【thumbColor】: Selected Color   【Color】", "【backgroundColor】: Background Color   【Color】", "【padding】: Padding   【EdgeInsetsGeometry】"]}]}
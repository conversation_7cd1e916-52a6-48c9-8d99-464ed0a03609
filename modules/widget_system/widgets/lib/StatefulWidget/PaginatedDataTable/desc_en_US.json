{"id": 235, "name": "PaginatedDataTable", "localName": "Paginated Table", "info": "A feature-rich paginated table component that allows specifying the number of pages, sorting, and switching between pages.", "lever": 4, "family": 1, "linkIds": [110, 102], "nodes": [{"file": "node1_base.dart", "name": "PaginatedDataTable Usage", "desc": ["【header】 : Table Name   【Widget】", "【rowsPerPage】 : Number of records per page   【int】", "【actions】 : Action components   【List<Widget>】", "【columns】 : Data columns   【List<DataColumn>】", "【sortColumnIndex】 : Sort column index   【int】", "【sortAscending】 : Whether ascending   【bool】", "【onSelectAll】 : Select all callback   【ValueSetter<bool>】", "【onRowsPerPageChanged】 : Page change listener   【ValueChanged<int>】", "【availableRowsPerPage】 : Available page list   【List<int>】", "【source】 : Data source   【DataTableSource】"]}]}
{"id": 160, "name": "Material", "localName": "재료 컴포넌트", "info": "Material 스타일 컴포넌트의 선두주자, 핵심 요소. 색상, 그림자 깊이, 유형, 그림자 색상, 모양 등 속성을 지정할 수 있습니다.", "lever": 5, "family": 1, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "Material 기본 사용", "desc": ["【child】 : 자식 컴포넌트   【Widget】", "【type】 : 유형   【MaterialType】", "【elevation】 : 그림자 깊이   【double】", "【shadowColor】 : 그림자 색상   【Color】", "【color】 : 색상   【Color】"]}, {"file": "node2_shape.dart", "name": "Material의 shape 속성", "desc": ["【shape】 : 모양   【ShapeBorder】,"]}]}
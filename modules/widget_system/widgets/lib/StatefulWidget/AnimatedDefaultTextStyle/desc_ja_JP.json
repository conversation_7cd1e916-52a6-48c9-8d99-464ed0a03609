{"id": 124, "name": "AnimatedDefaultTextStyle", "localName": "コンテナアニメーション", "info": "子テキストコンポーネントにTextStyle（テキストスタイル）アニメーションを適用できます。時間と曲線を指定でき、アニメーション終了イベントがあります。", "lever": 3, "family": 1, "linkIds": [114, 324], "nodes": [{"file": "node1_base.dart", "name": "AnimatedDefaultTextStyleの基本的な使用", "desc": ["【child】 : 子コンポーネント   【Widget】", "【duration】 : アニメーション時間   【Duration】", "【onEnd】 : アニメーション終了コールバック   【Function()】", "【curve】 : アニメーション曲線   【Duration】", "【textAlign】 : テキストの配置   【TextAlign】", "【softWrap】 : ラップするかどうか   【bool】", "【maxLines】 : 最大行数   【int】", "【overflow】 : オーバーフローモード   【TextOverflow】", "【style】 : テキストスタイル   【TextStyle】"]}]}
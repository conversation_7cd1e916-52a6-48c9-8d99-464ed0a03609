{"id": 55, "name": "DropdownButton", "localName": "<PERSON><PERSON><PERSON>", "info": "Bouton pour la sélection déroulante, pouvant spécifier des attributs tels que l'icône, la profondeur d'ombre, l'indice, etc., et recevoir des événements de changement de sélection.", "lever": 4, "family": 1, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "Utilisation de base de DropdownButton", "desc": ["【value】 : Valeur actuelle   【T】", "【items】 : Liste déroulante   【List<DropdownMenuItem<T>>】", "【icon】 : Icône   【Widget】", "【elevation】 : Profondeur d'ombre   【double】", "【onChanged】 : Événement de sélection d'élément   【Function(T)】", "【backgroundColor】 : <PERSON>uleur de fond   【Color】"]}, {"file": "node2_style.dart", "name": "Spécification du style de DropdownButton", "desc": ["【isDense】 : Si compact   【bool】", "【iconSize】 : <PERSON><PERSON> de l'icône   【double】", "【hint】 : Composant d'indice   【Widget】", "【iconEnabledColor】 : Couleur de l'icône   【Color】"]}]}
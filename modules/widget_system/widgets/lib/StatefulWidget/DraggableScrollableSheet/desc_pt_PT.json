{"id": 252, "name": "DraggableScrollableSheet", "localName": "Folha <PERSON> e Arrastável", "info": "Uma folha que pode ser arrastada e deslizada, permitindo especificar a divisão máxima, mínima e inicial dentro da faixa de deslizamento. O construtor builder precisa retornar um componente deslizável.", "lever": 2, "family": 1, "linkIds": [221, 142], "nodes": [{"file": "node1_base.dart", "name": "Uso Básico do DraggableScrollableSheet", "desc": ["【initialChildSize】: Divisão inicial   【double】", "【minChildSize】: Divisão mínima   【double】", "【maxChildSize】: Divisão máxima   【double】", "【builder】: Construtor do componente deslizável   【ScrollableWidgetBuilder】", "【expand】: Estender   【bool】"]}]}
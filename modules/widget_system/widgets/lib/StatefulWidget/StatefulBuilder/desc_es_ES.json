{"id": 242, "name": "StatefulBuilder", "localName": "<PERSON><PERSON><PERSON><PERSON>", "info": "Requiere que se pase la propiedad builder para construir el componente. En el builder, se puede usar StateSetter para cambiar el estado del subcomponente, lo que permite implementar un componente con actualización local sin necesidad de crear una clase.", "lever": 3, "family": 1, "linkIds": [202, 203, 280, 255], "nodes": [{"file": "node1_base.dart", "name": "Uso básico de StatefulBuilder", "desc": ["【builder】 : Con<PERSON><PERSON>ctor del componente   【StatefulWidgetBuilder】"]}]}
{"id": 114, "name": "DefaultTextStyleTransition", "localName": "Transition de style de texte", "info": "Sous-classe de AnimatedWidget, utilise un animateur de type TextStyle pour permettre à un composant de texte de faire une transition animée entre deux objets TextStyle.", "lever": 3, "family": 1, "linkIds": [124, 324], "nodes": [{"file": "node1_base.dart", "name": "Utilisation de base de DefaultTextStyleTransition", "desc": ["【child】 : <PERSON><PERSON><PERSON><PERSON> enfant   【Widget】", "【textAlign】 : Alignement du texte  【TextAlign】", "【softWrap】 : Enveloppement  【bool】", "【maxLines】 : Nombre maximum de lignes  【int】", "【overflow】 : Mode de débordement  【TextOverflow】", "【style】 : Animation   【Animation<TextStyle>】"]}]}
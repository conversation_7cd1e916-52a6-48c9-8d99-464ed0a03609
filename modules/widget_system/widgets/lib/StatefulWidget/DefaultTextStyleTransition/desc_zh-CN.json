{"id": 114, "name": "DefaultTextStyleTransition", "localName": "文字样式变换", "info": "AnimatedWidget的子类,使用TextStyle类型的动画器让文字组件在两个TextStyle对象之间进行过渡动画。", "lever": 3, "family": 1, "linkIds": [124, 324], "nodes": [{"file": "node1_base.dart", "name": "DefaultTextStyleTransition基本使用", "desc": ["【child】 : 孩子组件   【Widget】", "【textAlign】 : 文字对齐方式  【TextAlign】", "【softWrap】 : 是否包裹  【bool】", "【maxLines】 : 最大行数  【int】", "【overflow】 : 溢出模式  【TextOverflow】", "【style】 : 动画   【Animation<TextStyle>】"]}]}
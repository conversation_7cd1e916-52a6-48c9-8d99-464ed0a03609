{"id": 114, "name": "DefaultTextStyleTransition", "localName": "Transición de Estilo de Texto", "info": "Subclase de AnimatedWidget, utiliza un animador de tipo TextStyle para permitir que los componentes de texto realicen una transición animada entre dos objetos TextStyle.", "lever": 3, "family": 1, "linkIds": [124, 324], "nodes": [{"file": "node1_base.dart", "name": "Uso básico de DefaultTextStyleTransition", "desc": ["【child】 : Widget hijo   【Widget】", "【textAlign】 : Alineación del texto  【TextAlign】", "【softWrap】 : Si debe envolver  【bool】", "【maxLines】 : Número máximo de líneas  【int】", "【overflow】 : Modo de desbordamiento  【TextOverflow】", "【style】 : Animación   【Animation<TextStyle>】"]}]}
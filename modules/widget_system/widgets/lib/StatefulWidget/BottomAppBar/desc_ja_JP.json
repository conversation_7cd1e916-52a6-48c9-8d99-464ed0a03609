{"id": 61, "name": "BottomAppBar", "localName": "ボトムナビゲーション", "info": "凹型のボトムナビゲーションバーで、通常Scaffoldコンポーネントの下部に使用され、色、影の深さ、形状などの属性を指定できます。PageViewと組み合わせてページ切り替え効果を実現できます。", "lever": 4, "family": 1, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "BottomAppBarの基本使用法", "desc": ["【elevation】 : 影の深さ   【double】", "【shape】 : 形状   【NotchedShape】", "【notchMargin】 : 間隔距離   【double】", "【color】 : 色   【Color】", "【child】 : 子   【Widget】"]}]}
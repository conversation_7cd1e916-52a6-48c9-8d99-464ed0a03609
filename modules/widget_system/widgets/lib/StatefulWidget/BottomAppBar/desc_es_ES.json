{"id": 61, "name": "BottomAppBar", "localName": "Barra de navegación inferior", "info": "Una barra de navegación inferior que se puede incrustar, generalmente se usa en la parte inferior del componente Scaffold, se pueden especificar propiedades como color, profundidad de sombra, forma, etc., y se puede implementar un efecto de cambio de página con PageView.", "lever": 4, "family": 1, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "Uso básico de BottomAppBar", "desc": ["【elevation】 : Profundidad de sombra   【double】", "【shape】 : Forma   【NotchedShape】", "【notchMargin】 : Distancia de separación   【double】", "【color】 : Color   【Color】", "【child】 : <PERSON><PERSON>   【Widget】"]}]}
{"id": 339, "name": "DateRangePickerDialog", "localName": "Диа<PERSON>азон дат", "info": "Селектор диапазона дат в стиле Material, поддерживает выбор через календарь и ввод.", "lever": 4, "family": 1, "linkIds": [135, 136, 137], "nodes": [{"file": "node1_base.dart", "name": "Основное использование DateRangePickerDialog", "desc": ["【firstDate】 : Самая ранняя дата   【DateTime】", "【lastDate】 : Самая поздняя дата   【DateTime】", "【initialDateRange】 : Начальный диапазон   【DateTimeRange?】", "【saveText】 : Текст сохранения  【String?】"]}, {"file": "node2_diy.dart", "name": "Модификация DateRangePickerDialog", "desc": ["Изменение исходного кода DateRangePickerDialog для отображения числового фона для элементов месяца."]}]}
{"id": 93, "name": "PositionedTransition", "localName": "Transición de Posición", "info": "Solo se puede usar en Stack, puede contener un componente hijo y permite que realice una animación de posición entre dos rectángulos, se requiere proporcionar un animador rect.", "lever": 3, "family": 1, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "Uso básico de PositionedTransition", "desc": ["【child】 : <PERSON>mpo<PERSON><PERSON> hijo   【Widget】", "【rect】 : Animación   【Animation<RelativeRect>】", "    El componente PositionedTransition solo funciona dentro de Stack"]}]}
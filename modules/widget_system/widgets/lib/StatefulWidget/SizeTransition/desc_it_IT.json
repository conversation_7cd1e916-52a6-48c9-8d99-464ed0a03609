{"id": 92, "name": "SizeTransition", "localName": "Transizione Dimensionale", "info": "P<PERSON>ò contenere un componente figlio e fargli eseguire un'animazione di dimensione, richiede un animatore sizeFactor, è possibile specificare l'asse di cambiamento delle dimensioni e l'allineamento assiale.", "lever": 3, "family": 1, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "Uso Base di SizeTransition", "desc": ["【child】 : Componente figlio   【Widget】", "【axis】 : Asse*2   【Axis】", "【sizeFactor】 : Animazione   【Animation<double>】"]}]}
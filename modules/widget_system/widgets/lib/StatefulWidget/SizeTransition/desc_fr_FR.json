{"id": 92, "name": "SizeTransition", "localName": "Transition de taille", "info": "Peut contenir un composant enfant et lui faire subir une animation de taille. Nécessite un animateur sizeFactor, peut spécifier l'axe de changement de taille et l'alignement axial axisAlignment.", "lever": 3, "family": 1, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "Utilisation de base de SizeTransition", "desc": ["【child】 : <PERSON><PERSON><PERSON><PERSON> enfant   【Widget】", "【axis】 : Axe*2   【Axis】", "【sizeFactor】 : Animation   【Animation<double>】"]}]}
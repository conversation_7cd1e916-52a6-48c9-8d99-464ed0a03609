{"id": 105, "name": "LongPressDraggable", "localName": "Drag Target", "info": "Allows the component to be dragged anywhere on the interface when long-pressed, and can store data of a generic type T. It is usually used in combination with DragTarget to achieve the drag effect.", "lever": 4, "family": 1, "linkIds": [103, 104], "nodes": [{"file": "node1_base.dart", "name": "LongPressDraggable used with DragTarget", "desc": ["【child】 : child   【Widget】", "【feedback】 : child during drag   【Widget】", "【axis】 : axis of drag   【Axis】", "【data】 : data   【T】", "【onDragStarted】 : start drag   【Function()】", "【onDragEnd】 : end drag   【Function(DraggableDetails)】", "【onDragCompleted】 : drag completed   【Function()】", "【onDraggableCanceled】 : drag canceled   【Function(Velocity,Offset)】"]}]}
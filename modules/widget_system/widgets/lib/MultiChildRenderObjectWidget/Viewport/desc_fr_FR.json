{"id": 340, "name": "Viewport", "localName": "Composant de fenêtre", "info": "Généralement utilisé pour fournir une fenêtre à une vue défilante, ne construit que les parties affichées et préchargées. Peut spécifier la longueur de préchargement, l'axe de défilement, etc. Est l'un des composants de base de l'implémentation de ScrollView, généralement non utilisé directement.", "lever": 1, "family": 3, "linkIds": [253, 349], "nodes": [{"file": "node1_base.dart", "name": "Utilisation de base de Viewport", "desc": ["【offset】 : *Décalage de la fenêtre   【ViewportOffset】", "【cacheExtentStyle】: Type de préchargement   【CacheExtentStyle】", "【cacheExtent】: Quantité de préchargement   【double】", "【axisDirection】: Direction de défilement   【AxisDirection】", "【slivers】: Ensemble de composants Sliver enfants   【List<Widget>】", "【anchor】: Point d'ancrage    【double】", "Vous pouvez exécuter ce code pour voir la construction de ColorItem, les 128 barres de couleur ne sont pas toutes construites en une seule fois."]}]}
{"id": 340, "name": "Viewport", "localName": "뷰포트 컴포넌트", "info": "일반적으로 스크롤 뷰에 뷰포트를 제공하기 위해 사용되며, 표시 및 미리 로드할 부분만 구성합니다. 미리 로드할 길이, 스크롤 방향 등을 지정할 수 있습니다. ScrollView의 핵심 구현 컴포넌트 중 하나로, 일반적으로 직접 사용하지 않습니다.", "lever": 1, "family": 3, "linkIds": [253, 349], "nodes": [{"file": "node1_base.dart", "name": "Viewport의 기본 사용", "desc": ["【offset】 : *뷰포트 오프셋   【ViewportOffset】", "【cacheExtentStyle】: 미리 로드 유형   【CacheExtentStyle】", "【cacheExtent】: 미리 로드량   【double】", "【axisDirection】: 스크롤 방향   【AxisDirection】", "【slivers】: 하위 Sliver 컴포넌트 집합   【List<Widget>】", "【anchor】: 앵커    【double】", "이 코드를 실행하여 ColorItem의 구성을 확인할 수 있으며, 128개의 색상 바가 한 번에 모두 구성되지는 않습니다."]}]}
{"id": 161, "name": "IndexedStack", "localName": "Pilha Indexada", "info": "Subclasse do componente Stack, pode empilhar vários componentes e especificar o índice do componente a ser exibido através do index, os restantes serão ocultados.", "lever": 4, "family": 1, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "Uso básico do IndexedStack", "desc": ["【children】 : Lista de componentes filhos   【Lis<Widget>】", "【alignment】 : Alinhamento   【AlignmentGeometry】", "【index】 : Componente atualmente exibido  【int】"]}]}
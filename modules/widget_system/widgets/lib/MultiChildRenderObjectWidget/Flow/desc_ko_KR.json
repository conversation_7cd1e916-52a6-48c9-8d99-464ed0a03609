{"id": 99, "name": "Flow", "localName": "플로우 레이아웃", "info": "여러 컴포넌트를 수용할 수 있으며, 배치를 위한 대리자를 직접 지정해야 합니다. 컴포넌트 배치를 고도로 커스터마이징할 수 있어 일반 레이아웃으로는 달성할 수 없는 효과를 구현할 수 있습니다. 레이아웃의 왕, 그 이름에 걸맞습니다.", "lever": 5, "family": 3, "linkIds": [98, 94], "nodes": [{"file": "node_01.dart", "name": "Flow 원형 배치", "desc": ["【children】 : 컴포넌트 리스트   【List<Widget>】", "【delegate】 : 대리자   【FlowDelegate】"]}, {"file": "node_02.dart", "name": "Flow 원형과 애니메이션 결합", "desc": ["애니메이션을 통해 주변 컴포넌트의 위치를 변경하여 효과를 구현"]}]}
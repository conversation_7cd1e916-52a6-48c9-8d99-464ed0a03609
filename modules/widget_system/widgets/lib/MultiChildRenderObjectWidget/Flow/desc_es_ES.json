{"id": 99, "name": "Flow", "localName": "Diseño de flujo", "info": "<PERSON>uede contener múltiples componentes, requiere que especifiques un delegado para la disposición, permite una alta personalización en la distribución de los componentes, logrando efectos que los diseños comunes no pueden alcanzar. El rey de los diseños, sin duda.", "lever": 5, "family": 3, "linkIds": [98, 94], "nodes": [{"file": "node_01.dart", "name": "Disposición circular de Flow", "desc": ["【children】: Lista de componentes 【List<Widget>】", "【delegate】: Delegado 【FlowDelegate】"]}, {"file": "node_02.dart", "name": "Combinación de Flow circular con animación", "desc": ["Logra el efecto cambiando la posición de los componentes circundantes a través de la animación"]}]}
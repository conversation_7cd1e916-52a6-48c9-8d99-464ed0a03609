{"id": 94, "name": "Flex", "localName": "Flexibles Layout", "info": "Übergeordnete Klasse von Row und Column, die leistungsstärkste Layout-Methode in Flutter. Kann mehrere Komponenten aufnehmen und kann mit Spacer, Expended, Flexible Komponenten für flexibles Layout verwendet werden", "lever": 5, "family": 3, "linkIds": [95, 96, 106, 107, 109], "nodes": [{"file": "node_01.dart", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "desc": ["【children】 : Komponentenliste   【List<Widget>】", "【direction】 : <PERSON>tung   【Axis】"]}, {"file": "node_02.dart", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "desc": ["【mainAxisAlignment】 : Hauptachsenausrichtung   【MainAxisAlignment】"]}, {"file": "node_03.dart", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "desc": ["【crossAxisAlignment】 : Kreuzachsenausrichtung   【CrossAxisAlignment】"]}, {"file": "node_04.dart", "name": "<PERSON>ert<PERSON><PERSON> Richtungsreihen<PERSON><PERSON> von <PERSON>", "desc": ["【verticalDirection】 : Vertikale Richtungsreihenfolge   【VerticalDirection】"]}, {"file": "node_05.dart", "name": "Horizontale Richtungsreih<PERSON><PERSON><PERSON>", "desc": ["【textDirection】 : Horizontale Richtungsreihenfolge   【TextDirection】"]}]}
{"id": 95, "name": "Row", "localName": "Layout a righe", "info": "Layout Flex con direzione orizzontale, può contenere più componenti. Tutte le altre proprietà sono le stesse, vedi Flex per i dettagli.", "lever": 4, "family": 3, "linkIds": [94, 96], "nodes": [{"file": "node1_base.dart", "name": "Uso di base di Row", "desc": ["【children】 : Lista di componenti   【List<Widget>】", "【mainAxisAlignment】 : Allineamento dell'asse principale   【MainAxisAlignment】", "【crossAxisAlignment】 : Allineamento dell'asse trasversale   【CrossAxisAlignment】", "【textBaseline】 : Linea di base del testo   【TextBaseline】", "【verticalDirection】 : Direzione verticale   【VerticalDirection】", "【mainAxisSize】 : Dimensione dell'asse principale   【MainAxisSize】"]}]}
{"id": 97, "name": "<PERSON><PERSON>", "localName": "堆叠布局", "info": "可容纳多个组件,以堆叠的方式摆放子组件，后者居上。拥有alignment属性，可与Positioned组件联合使用，精确定位。", "lever": 5, "family": 3, "linkIds": [94, 95, 161], "nodes": [{"file": "node1_base.dart", "name": "Stack基本使用", "desc": ["【children】 : 组件列表   【List<Widget>】", "【textDirection】 : 孩子排布方向   【MainAxisAlignment】", "【alignment】 : 对齐方式   【AlignmentGeometry】", "【overflow】 : 溢出模式   【Overflow】", "【fit】 : 适应模式   【StackFit】"]}, {"file": "node2_positioned.dart", "name": "Stack和Positioned结合使用", "desc": ["Positioned组件只能用与Stack中，可以指定左上右下的距离对某个组件进行位置精确安放。"]}]}
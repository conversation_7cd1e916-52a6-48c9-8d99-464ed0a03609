{"id": 98, "name": "Wrap", "localName": "包裹布局", "info": "可容纳多个组件,按照指定方向依次排布，可以很方便处理孩子的间距,当越界时可以自动换行。拥有主轴和交叉轴的对齐方式，比较灵活。", "lever": 5, "family": 3, "linkIds": [94, 95], "nodes": [{"file": "node1_base.dart", "name": "Wrap的基础用法", "desc": ["【children】 : 组件列表   【List<Widget>】", "【spacing】 : 主轴条目间距   【double】", "【runSpacing】 : 交叉轴条目间距   【double】", "【direction】 : 主轴对齐   【Axis】"]}, {"file": "node2_alignment.dart", "name": "Wrap的alignment属性", "desc": ["【alignment】 : 主轴对齐   【WrapAlignment】"]}, {"file": "node3_crossAxisAlignment.dart", "name": "Wrap的crossAxisAlignment属性", "desc": ["【crossAxisAlignment】 : 交叉轴对齐   【CrossAxisAlignment】"]}, {"file": "node4_textDirection.dart", "name": "Wrap的textDirection属性", "desc": ["【textDirection】 : 文字方向   【TextDirection】"]}, {"file": "node5_verticalDirection.dart", "name": "Wrap的verticalDirection属性", "desc": ["【verticalDirection】 : 竖直方向  【VerticalDirection】"]}]}
{"id": 101, "name": "RichText", "localName": "Texto enriquecido", "info": "Componente de texto enriquecido que puede contener varios estilos de texto o varios componentes, ampliamente utilizado.", "lever": 5, "family": 3, "linkIds": [], "nodes": [{"file": "node1_base.dart", "name": "Uso básico de RichText", "desc": ["【text】 : texto   【TextSpan】", "    <PERSON><PERSON> propiedades son las mismas que Text, consulte para más detalles."]}, {"file": "node2_widget.dart", "name": "RichText contiene otros componentes", "desc": ["Usa WidgetSpan para contener componentes comunes como contenido de RichText"]}]}
// lib/bloc/measurement_chart_bloc.dart
import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../model/measurement.dart';
import '../model/measurement_chart_data.dart';
import '../model/measurement_query.dart';
import '../repository/telemetry_repository.dart';

// 事件
abstract class MeasurementChartEvent {}

class AddMeasurementData extends MeasurementChartEvent {
  final Measurement data;
  AddMeasurementData(this.data);
}

class DisplacementUpdateData extends MeasurementChartEvent{
  final Map<String, dynamic> data;
  DisplacementUpdateData(this.data);
}

class MeasurementChartLoaded extends MeasurementChartEvent {
  final List<Measurement> measurements;
  MeasurementChartLoaded(this.measurements);
}

class ClearMeasurementData extends MeasurementChartEvent {}

class FetchHistoricalData extends MeasurementChartEvent {
  final DateTime start;
  final DateTime end;
  final String? targetId;
  final List<String> types;
  final String deviceId;

  FetchHistoricalData({
    required this.deviceId,
    required this.start,
    required this.end,
    this.targetId,
    required this.types,
  });
}

// 状态
class MeasurementChartState {
  final Map<String, List<Measurement>> dataMap;
  final bool isLoading;
  final MeasurementQuery query;

  MeasurementChartState({
    required this.dataMap,
    MeasurementQuery? query,
    this.isLoading = false,
  }) : query = query ?? MeasurementQuery.defaultQuery();


  MeasurementChartState copyWith({
    Map<String, List<Measurement>>? dataMap,
    bool? isLoading,
    MeasurementQuery? query,
  }) {
    return MeasurementChartState(
      query: query ?? this.query,
      dataMap: dataMap ?? this.dataMap,
      isLoading: isLoading ?? this.isLoading,
    );
  }
}

class MeasurementChartBloc extends Bloc<MeasurementChartEvent, MeasurementChartState> {
  // 最大数据点数量
  final int maxDataPoints;
  final telemetryRepository = TelemetryRepository();
  final StreamController<Map<String, dynamic>> telemetryStreamController = StreamController<Map<String, dynamic>>.broadcast();

  MeasurementChartBloc({this.maxDataPoints = 500000})
      : super(MeasurementChartState(dataMap: {})) {
    on<AddMeasurementData>(_onAddMeasurementData);
    on<DisplacementUpdateData>(_onUpdateDisplacementData);
    on<ClearMeasurementData>(_onClearMeasurementData);
    on<FetchHistoricalData>(_onFetchHistoricalData);
  }

  void _onUpdateDisplacementData(
      DisplacementUpdateData event,
      Emitter<MeasurementChartState> emit,
      ) {
    telemetryStreamController.add(event.data);

  }

  void _onAddMeasurementData(
      AddMeasurementData event,
      Emitter<MeasurementChartState> emit,
      ) {
    final dataMap = Map<String, List<Measurement>>.from(state.dataMap);
    final key = _getKey(event.data);

    if (!dataMap.containsKey(key)) {
      dataMap[key] = [];
    }

    dataMap[key] = [...dataMap[key]!, event.data];

    // 限制数据量
    if (dataMap[key]!.length > maxDataPoints) {
      dataMap[key] = dataMap[key]!.skip(dataMap[key]!.length - maxDataPoints).toList();
    }


    emit(state.copyWith(dataMap: dataMap));
  }

  void _onClearMeasurementData(
      ClearMeasurementData event,
      Emitter<MeasurementChartState> emit,
      ) {
    emit(MeasurementChartState(dataMap: {}));
  }

  Future<void> _onFetchHistoricalData(
      FetchHistoricalData event,
      Emitter<MeasurementChartState> emit,
      ) async {
    emit(state.copyWith(isLoading: true));

    try {
      // 这里只是预留类型，实际应该从数据源获取
      final startTs = event.start.millisecondsSinceEpoch;
      final endTs = event.end.millisecondsSinceEpoch;

      List<Map<String, dynamic>> result = await telemetryRepository.queryDisplacement(
        event.deviceId,
        targetId: event.targetId,
        startTs: startTs,
        endTs: endTs,
      );

      List<Measurement> x= [];
      List<Measurement> y = [];

    result.map((row) {
          final dt = DateTime.fromMillisecondsSinceEpoch(row['ts']);
          final x = row['sigma_x'];
          final y = row['sigma_y'];
         final mx = Measurement(
          targetId: event.targetId,
          type: event.types.first,
          value: x,
          timestamp: dt,
        );
          final my = Measurement(
            targetId: event.targetId,
            type: event.types.first,
            value: x,
            timestamp: dt,
          );
         x.add(mx);
         y.add(my);
      });

      final dataMap = Map<String, List<Measurement>>.from(state.dataMap);
      // 处理每个类型的测量数据
      for (final type in event.types) {
        final x_key = event.targetId != null ? '${event.targetId}_${type}_sigmaX' : type;
        final y_key = event.targetId != null ? '${event.targetId}_${type}_sigmaY' : type;
        if (!dataMap.containsKey(x_key)) {
          dataMap[x_key] = [];
        }
        // 仅为第一个类型添加数据（与上面创建 measurements 时使用的类型一致）
        if (type == event.types.first) {
          debugPrint('添加测量数据: ${x_key}');
          dataMap[x_key] = x;
          dataMap[y_key] = y;
        }
      }
      emit(state.copyWith(dataMap: dataMap, isLoading: false));
    } catch (e) {
      debugPrint('加载历史数据错误: $e');
      emit(state.copyWith(isLoading: false));
    }
  }

  String _getKey(Measurement data) {
    return data.targetId != null ? '${data.targetId}_${data.type}' : data.type;
  }
}

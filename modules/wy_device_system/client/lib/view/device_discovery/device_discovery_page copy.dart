import 'dart:io';
import '../../model/discovered_device.dart';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:provider/provider.dart';
import 'package:tolyui/tolyui.dart';
import 'package:inteagle_monitoring_robot_app/src/bindings/bindings.dart';
import '../../bloc/device_discovery_bloc.dart';
import '../../common/utils.dart';
import '../../services/device_discover_service.dart';
import '../home/<USER>';
import '../home/<USER>';
import 'package:utils/utils.dart';
import 'device_search.dart';
import 'package:path_provider/path_provider.dart';

// 主页面类
class DeviceDiscoveryPage extends StatefulWidget {
  const DeviceDiscoveryPage({Key? key}) : super(key: key);

  @override
  State<DeviceDiscoveryPage> createState() => _DeviceDiscoveryPageState();
}

class _DeviceDiscoveryPageState extends State<DeviceDiscoveryPage> {
  late DeviceDiscoveryBloc _discoveryBloc;
  final TextEditingController _ipController = TextEditingController();
  final TextEditingController _portController =
      TextEditingController(text: '9999');
  final TextEditingController _searchController = TextEditingController();
  final _formKey = GlobalKey<FormState>();
  String _searchQuery = '';

  @override
  void initState() {
    super.initState();
    _discoveryBloc = DeviceDiscoveryBloc(DeviceDiscoveryService());
    _discoveryBloc.add(StartDiscovery());
  }

  @override
  void dispose() {
    _discoveryBloc.close();
    _ipController.dispose();
    _portController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider.value(
      value: _discoveryBloc,
      child: LayoutBuilder(
        builder: (context, constraints) {
          // 确定当前布局模式
          final bool isTabletOrDesktop = constraints.maxWidth > 600;

          return Scaffold(
            appBar: AppBar(
              title: const Text('视觉位移计设备发现'),
              actions: [
                // 搜索按钮
                IconButton(
                  icon: const Icon(Icons.search),
                  tooltip: '搜索设备',
                  onPressed: () {
                    showSearch(
                      context: context,
                      delegate: DeviceSearchDelegate(
                        devices: _getDevices(),
                        onDeviceSelected: _connectToDevice,
                        onDeviceDeleted: _deleteDevice,
                      ),
                    );
                  },
                ),
                // 添加设备按钮
                IconButton(
                  icon: const Icon(Icons.add),
                  tooltip: '添加设备',
                  onPressed: () => showAddDeviceByIdentifierDialog(context),
                ),
                BlocBuilder<DeviceDiscoveryBloc, DeviceDiscoveryState>(
                  builder: (context, state) {
                    final isDiscovering = state is DeviceDiscoveryLoaded
                        ? state.isDiscovering
                        : false;
                    return IconButton(
                      icon: isDiscovering
                          ? const Icon(Icons.search_off)
                          : const Icon(Icons.wifi_find),
                      tooltip: isDiscovering ? '停止扫描' : '开始扫描',
                      onPressed: () {
                        if (isDiscovering) {
                          context
                              .read<DeviceDiscoveryBloc>()
                              .add(StopDiscovery());
                        } else {
                          context
                              .read<DeviceDiscoveryBloc>()
                              .add(StartDiscovery());
                        }
                      },
                    );
                  },
                ),
              ],
            ),
            body: BlocBuilder<DeviceDiscoveryBloc, DeviceDiscoveryState>(
              builder: (context, state) {
                if (state is DeviceDiscoveryInitial ||
                    state is DeviceDiscoveryLoading) {
                  return const Center(child: CircularProgressIndicator());
                } else if (state is DeviceDiscoveryError) {
                  return Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        const Icon(Icons.error_outline,
                            size: 48, color: Colors.red),
                        const SizedBox(height: 16),
                        Text(state.message,
                            style: const TextStyle(fontSize: 16)),
                        const SizedBox(height: 24),
                        ElevatedButton(
                          onPressed: () => context
                              .read<DeviceDiscoveryBloc>()
                              .add(StartDiscovery()),
                          child: const Text('重试'),
                        ),
                      ],
                    ),
                  );
                } else if (state is DeviceDiscoveryLoaded) {
                  // 根据屏幕大小使用不同的布局
                  return isTabletOrDesktop
                      ? _buildTabletLayout(context, state)
                      : _buildMobileDeviceList(context, state);
                }

                return const Center(child: Text('未知状态'));
              },
            ),
            floatingActionButton: FloatingActionButton(
              onPressed: () => showAddDeviceByIdentifierDialog(context),
              tooltip: '添加设备',
              child: const Icon(Icons.add),
            ),
          );
        },
      ),
    );
  }

  void showAddDeviceByIdentifierDialog(BuildContext context) {
    final TextEditingController identifierController = TextEditingController();
    final TextEditingController nameController = TextEditingController();
    final TextEditingController ipController = TextEditingController();
    final TextEditingController portController =
        TextEditingController(text: '9999');
    bool hasIPAddress = false;
    final bool isSmallScreen = MediaQuery.of(context).size.width < 600;
    final bloc = _discoveryBloc;

    // 表单验证状态
    bool isIdentifierValid = true;
    String identifierErrorText = '';

    // 验证设备ID函数
    void validateIdentifier(String value, Function(bool, String) updateState) {
      if (value.trim().isEmpty) {
        updateState(false, '设备ID/序列号不能为空');
      } else {
        updateState(true, '');
      }
    }

    if (isSmallScreen) {
      // 移动设备布局
      showDialog(
        context: context,
        builder: (context) => StatefulBuilder(
          builder: (context, setState) => AlertDialog(
            title: const Text('添加设备'),
            content: SingleChildScrollView(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  TextField(
                    controller: identifierController,
                    decoration: InputDecoration(
                      labelText: '设备ID/序列号 *',
                      hintText: '例如: SN12345678 或 ABC123',
                      errorText: isIdentifierValid ? null : identifierErrorText,
                      // 添加必填标记
                      suffixIcon: const Tooltip(
                        message: '必填项',
                        child: Icon(Icons.star, size: 10, color: Colors.red),
                      ),
                    ),
                    onChanged: (value) {
                      validateIdentifier(value, (isValid, errorText) {
                        setState(() {
                          isIdentifierValid = isValid;
                          identifierErrorText = errorText;
                        });
                      });
                    },
                  ),
                  TextField(
                    controller: nameController,
                    decoration: const InputDecoration(
                      labelText: '设备名称(可选)',
                      hintText: '自定义设备名称',
                    ),
                  ),
                  TextField(
                    controller: ipController,
                    decoration: const InputDecoration(
                      labelText: 'IP地址(可选)',
                      hintText: '例如: *************',
                    ),
                    onChanged: (value) {
                      setState(() {
                        hasIPAddress = value.trim().isNotEmpty;
                      });
                    },
                  ),
                  const SizedBox(height: 8),
                  Text(
                    '未知IP地址时，系统将自动尝试发现设备',
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey[600],
                      fontStyle: FontStyle.italic,
                    ),
                  ),
                ],
              ),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('取消'),
              ),
              FilledButton(
                onPressed: isIdentifierValid &&
                        identifierController.text.trim().isNotEmpty
                    ? () => _addDeviceAndClose(
                          context,
                          bloc,
                          identifierController.text,
                          nameController.text,
                          hasIPAddress ? ipController.text : "*************",
                          hasIPAddress
                              ? int.tryParse(portController.text) ?? 9999
                              : 9999,
                        )
                    : null, // 禁用按钮如果ID无效
                child: const Text('添加'),
              ),
            ],
          ),
        ),
      );
    } else {
      // 平板/桌面设备布局
      showDialog(
        context: context,
        builder: (context) => Dialog(
          shape:
              RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
          child: Container(
            width: 450,
            padding: const EdgeInsets.all(24),
            child: StatefulBuilder(
              builder: (context, setState) => SingleChildScrollView(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(
                          Icons.devices,
                          color: Theme.of(context).colorScheme.primary,
                          size: 28,
                        ),
                        const SizedBox(width: 16),
                        const Text(
                          '添加设备',
                          style: TextStyle(
                            fontSize: 22,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 24),
                    TextField(
                      controller: identifierController,
                      decoration: InputDecoration(
                        labelText: '设备ID/序列号 *',
                        hintText: '例如: SN12345678 或 ABC123',
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                        filled: true,
                        prefixIcon: const Icon(Icons.badge),
                        errorText:
                            isIdentifierValid ? null : identifierErrorText,
                        // 添加必填标记
                        suffixIcon: const Tooltip(
                          message: '必填项',
                          child: Icon(Icons.star, size: 10, color: Colors.red),
                        ),
                      ),
                      onChanged: (value) {
                        validateIdentifier(value, (isValid, errorText) {
                          setState(() {
                            isIdentifierValid = isValid;
                            identifierErrorText = errorText;
                          });
                        });
                      },
                    ),
                    const SizedBox(height: 16),
                    TextField(
                      controller: nameController,
                      decoration: InputDecoration(
                        labelText: '设备名称(可选)',
                        hintText: '自定义设备名称',
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                        filled: true,
                        prefixIcon: const Icon(Icons.edit),
                      ),
                    ),
                    const SizedBox(height: 16),
                    Padding(
                      padding: const EdgeInsets.fromLTRB(16, 8, 16, 8),
                      child: Text(
                        'IP地址设置',
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w500,
                          color: Theme.of(context).colorScheme.primary,
                        ),
                      ),
                    ),
                    const Padding(
                      padding: EdgeInsets.symmetric(horizontal: 16),
                      child: Text(
                        '可以指定设备IP地址，不填将自动发现',
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.grey,
                        ),
                      ),
                    ),
                    const SizedBox(height: 16),
                    Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Expanded(
                          flex: 7,
                          child: TextField(
                            controller: ipController,
                            decoration: InputDecoration(
                              labelText: 'IP地址 (可选)',
                              hintText: '例如: *************',
                              border: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(8),
                              ),
                              filled: true,
                              prefixIcon: const Icon(Icons.lan),
                            ),
                            onChanged: (value) {
                              setState(() {
                                hasIPAddress = value.trim().isNotEmpty;
                              });
                            },
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 24),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.end,
                      children: [
                        TextButton(
                          onPressed: () => Navigator.pop(context),
                          child: const Text('取消'),
                        ),
                        const SizedBox(width: 8),
                        FilledButton(
                          onPressed: isIdentifierValid &&
                                  identifierController.text.trim().isNotEmpty
                              ? () => _addDeviceAndClose(
                                    context,
                                    bloc,
                                    identifierController.text,
                                    nameController.text,
                                    hasIPAddress
                                        ? ipController.text
                                        : "*************",
                                    hasIPAddress
                                        ? int.tryParse(portController.text) ??
                                            9999
                                        : 9999,
                                  )
                              : null, // 禁用按钮如果ID无效
                          child: const Text('添加'),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
      );
    }
  }

// 辅助方法，添加设备并关闭对话框
  void _addDeviceAndClose(
    BuildContext context,
    DeviceDiscoveryBloc bloc,
    String identifier,
    String name,
    String? ipAddress,
    int port,
  ) {
    if (identifier.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('请输入设备ID或序列号')),
      );
      return;
    }

    // 添加设备
    final deviceIdentifier = identifier.trim();
    final deviceName = name.trim();

    bloc.add(
      AddDeviceByIdentifier(
        deviceIdentifier,
        name: deviceName.isNotEmpty ? deviceName : null,
        ipAddress: ipAddress,
        port: port,
      ),
    );

    Navigator.pop(context);
  }

  // 平板/桌面版布局 - 网格视图
  Widget _buildTabletLayout(BuildContext context, DeviceDiscoveryLoaded state) {
    final devices = state.devices;

    if (devices.isEmpty) {
      return _buildEmptyState(context, state);
    }

    // 按照已保存和未保存分组
    final savedDevices = devices.where((d) => d.isSaved).toList();
    final unsavedDevices = devices.where((d) => !d.isSaved).toList();

    // 计算每行显示的设备数量，根据屏幕宽度调整
    final screenWidth = MediaQuery.of(context).size.width;
    final itemsPerRow = (screenWidth / 350).floor().clamp(2, 4);

    return CustomScrollView(
      slivers: [
        if (state.isDiscovering)
          const SliverToBoxAdapter(
            child: LinearProgressIndicator(minHeight: 4),
          ),
        SliverToBoxAdapter(
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text('发现 ${devices.length} 个设备',
                    style: Theme.of(context).textTheme.titleMedium),
                if (state.isDiscovering)
                  const Text('正在扫描...', style: TextStyle(color: Colors.blue)),
              ],
            ),
          ),
        ),
        if (savedDevices.isNotEmpty) ...[
          SliverToBoxAdapter(
            child: Padding(
              padding: const EdgeInsets.fromLTRB(16, 8, 16, 4),
              child: Text(
                '已保存的设备',
                style: Theme.of(context).textTheme.titleSmall?.copyWith(
                      color: Theme.of(context).colorScheme.primary,
                    ),
              ),
            ),
          ),
          SliverGrid(
            gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: itemsPerRow,
              childAspectRatio: 1.6,
              crossAxisSpacing: 12,
              mainAxisSpacing: 12,
            ),
            delegate: SliverChildBuilderDelegate(
              (context, index) =>
                  _buildDeviceCard(context, savedDevices[index], true),
              childCount: savedDevices.length,
            ),
          ),
          SliverToBoxAdapter(
            child: const Divider(height: 32, indent: 16, endIndent: 16),
          ),
        ],
        if (unsavedDevices.isNotEmpty) ...[
          SliverToBoxAdapter(
            child: Padding(
              padding: const EdgeInsets.fromLTRB(16, 8, 16, 4),
              child: Text(
                '新发现的设备',
                style: Theme.of(context).textTheme.titleSmall,
              ),
            ),
          ),
          SliverGrid(
            gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: itemsPerRow,
              childAspectRatio: 1.6,
              crossAxisSpacing: 12,
              mainAxisSpacing: 12,
            ),
            delegate: SliverChildBuilderDelegate(
              (context, index) =>
                  _buildDeviceCard(context, unsavedDevices[index], false),
              childCount: unsavedDevices.length,
            ),
          ),
        ],
      ],
    );
  }

  // 网格视图中的设备卡片
  Widget _buildDeviceCard(
      BuildContext context, DiscoveredDevice device, bool isSaved) {
    final bool isManuallyAdded = device.id.startsWith('manual_');

    return Card(
      clipBehavior: Clip.antiAlias,
      margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      elevation: 2,
      child: InkWell(
        onTap: () => _connectToDevice(context, device),
        child: Padding(
          padding: const EdgeInsets.all(12),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Container(
                    width: 40,
                    height: 40,
                    decoration: BoxDecoration(
                      color: isManuallyAdded
                          ? Theme.of(context).colorScheme.tertiaryContainer
                          : Theme.of(context).colorScheme.primaryContainer,
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Icon(
                      isManuallyAdded ? Icons.edit : Icons.device_hub,
                      color: isManuallyAdded
                          ? Theme.of(context).colorScheme.onTertiaryContainer
                          : Theme.of(context).colorScheme.onPrimaryContainer,
                    ),
                  ),
                  const SizedBox(width: 10),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          device.name,
                          style: const TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                        Text(
                          '地址: ${device.ipAddress}',
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.grey[700],
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ],
                    ),
                  ),
                ],
              ),
              const Spacer(),
              Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  if (isManuallyAdded)
                    Container(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 6, vertical: 2),
                      decoration: BoxDecoration(
                        color: Theme.of(context).colorScheme.tertiaryContainer,
                        borderRadius: BorderRadius.circular(4),
                      ),
                      child: Text(
                        '手动添加',
                        style: TextStyle(
                          fontSize: 10,
                          color:
                              Theme.of(context).colorScheme.onTertiaryContainer,
                        ),
                      ),
                    ),
                  const Spacer(),
                  IconButton(
                    icon: Icon(
                      isSaved ? Icons.bookmark : Icons.bookmark_border,
                      color: isSaved ? Colors.amber : null,
                    ),
                    visualDensity: VisualDensity.compact,
                    tooltip: isSaved ? '取消保存' : '保存设备',
                    onPressed: () {
                      if (isSaved) {
                        context
                            .read<DeviceDiscoveryBloc>()
                            .add(UnsaveDevice(device.id));
                      } else {
                        context
                            .read<DeviceDiscoveryBloc>()
                            .add(SaveDevice(device.id));
                      }
                    },
                  ),
                  IconButton(
                    icon: const Icon(Icons.delete),
                    color: Colors.red,
                    visualDensity: VisualDensity.compact,
                    tooltip: '删除设备',
                    onPressed: () => _confirmDeleteDevice(context, device),
                  ),
                  IconButton(
                    icon: const Icon(Icons.login),
                    color: Theme.of(context).colorScheme.primary,
                    visualDensity: VisualDensity.compact,
                    tooltip: '连接设备',
                    onPressed: () => _connectToDevice(context, device),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  // 手机布局 - 列表视图
  Widget _buildMobileDeviceList(
      BuildContext context, DeviceDiscoveryLoaded state) {
    final devices = state.devices;

    if (devices.isEmpty) {
      return _buildEmptyState(context, state);
    }

    // 按照已保存和未保存分组
    final savedDevices = devices.where((d) => d.isSaved).toList();
    final unsavedDevices = devices.where((d) => !d.isSaved).toList();

    return CustomScrollView(
      slivers: [
        if (state.isDiscovering)
          const SliverToBoxAdapter(
            child: LinearProgressIndicator(
              minHeight: 4,
            ),
          ),
        SliverToBoxAdapter(
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text('发现 ${devices.length} 个设备',
                    style: Theme.of(context).textTheme.titleMedium),
                if (state.isDiscovering)
                  const Text('正在扫描...', style: TextStyle(color: Colors.blue)),
              ],
            ),
          ),
        ),
        if (savedDevices.isNotEmpty) ...[
          SliverToBoxAdapter(
            child: Padding(
              padding: const EdgeInsets.fromLTRB(16, 8, 16, 4),
              child: Text(
                '已保存的设备',
                style: Theme.of(context).textTheme.titleSmall?.copyWith(
                      color: Theme.of(context).colorScheme.primary,
                    ),
              ),
            ),
          ),
          SliverList(
            delegate: SliverChildBuilderDelegate(
              (context, index) =>
                  _buildDeviceItem(context, savedDevices[index], true),
              childCount: savedDevices.length,
            ),
          ),
          SliverToBoxAdapter(
            child: const Divider(height: 32, indent: 16, endIndent: 16),
          ),
        ],
        if (unsavedDevices.isNotEmpty) ...[
          SliverToBoxAdapter(
            child: Padding(
              padding: const EdgeInsets.fromLTRB(16, 8, 16, 4),
              child: Text(
                '新发现的设备',
                style: Theme.of(context).textTheme.titleSmall,
              ),
            ),
          ),
          SliverList(
            delegate: SliverChildBuilderDelegate(
              (context, index) =>
                  _buildDeviceItem(context, unsavedDevices[index], false),
              childCount: unsavedDevices.length,
            ),
          ),
        ],
      ],
    );
  }

  // 列表项 - 用于移动设备
  Widget _buildDeviceItem(
      BuildContext context, DiscoveredDevice device, bool isSaved) {
    final bool isManuallyAdded = device.id.startsWith('manual_');

    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 6),
      elevation: 2,
      child: InkWell(
        onTap: () => _connectToDevice(context, device),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              Container(
                width: 48,
                height: 48,
                decoration: BoxDecoration(
                  color: isManuallyAdded
                      ? Theme.of(context).colorScheme.tertiaryContainer
                      : Theme.of(context).colorScheme.primaryContainer,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  isManuallyAdded ? Icons.edit : Icons.device_hub,
                  color: isManuallyAdded
                      ? Theme.of(context).colorScheme.onTertiaryContainer
                      : Theme.of(context).colorScheme.onPrimaryContainer,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Expanded(
                          child: Text(
                            device.name,
                            style: const TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                        if (isManuallyAdded)
                          Container(
                            padding: const EdgeInsets.symmetric(
                                horizontal: 6, vertical: 2),
                            decoration: BoxDecoration(
                              color: Theme.of(context)
                                  .colorScheme
                                  .tertiaryContainer,
                              borderRadius: BorderRadius.circular(4),
                            ),
                            child: Text(
                              '添加',
                              style: TextStyle(
                                fontSize: 10,
                                color: Theme.of(context)
                                    .colorScheme
                                    .onTertiaryContainer,
                              ),
                            ),
                          ),
                      ],
                    ),
                    const SizedBox(height: 4),
                    Text(
                      'ID: ${device.id}',
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.grey[700],
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    Text(
                      '地址: ${device.ipAddress}',
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.grey[700],
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                ),
              ),
              Column(
                children: [
                  IconButton(
                    icon: Icon(
                      isSaved ? Icons.bookmark : Icons.bookmark_border,
                      color: isSaved ? Colors.amber : null,
                    ),
                    tooltip: isSaved ? '取消保存' : '保存设备',
                    onPressed: () {
                      if (isSaved) {
                        context
                            .read<DeviceDiscoveryBloc>()
                            .add(UnsaveDevice(device.id));
                      } else {
                        context
                            .read<DeviceDiscoveryBloc>()
                            .add(SaveDevice(device.id));
                      }
                    },
                  ),
                  IconButton(
                    icon: const Icon(Icons.delete),
                    color: Colors.red,
                    tooltip: '删除设备',
                    onPressed: () => _confirmDeleteDevice(context, device),
                  ),
                  IconButton(
                    icon: const Icon(Icons.login),
                    color: Theme.of(context).colorScheme.primary,
                    tooltip: '连接设备',
                    onPressed: () => _connectToDevice(context, device),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  // 空状态视图 - 未发现设备时显示
  Widget _buildEmptyState(BuildContext context, DeviceDiscoveryLoaded state) {
    return Center(
      child: ConstrainedBox(
        constraints: const BoxConstraints(maxWidth: 400),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.devices, size: 64, color: Colors.grey),
            const SizedBox(height: 16),
            const Text('未发现设备', style: TextStyle(fontSize: 18)),
            const SizedBox(height: 8),
            Text(
              state.isDiscovering ? '正在扫描中...' : '点击搜索按钮开始扫描',
              style: const TextStyle(color: Colors.grey),
            ),
            const SizedBox(height: 24),
            if (!state.isDiscovering)
              ElevatedButton(
                onPressed: () =>
                    context.read<DeviceDiscoveryBloc>().add(StartDiscovery()),
                child: const Text('开始扫描'),
              ),
            const SizedBox(height: 12),
            OutlinedButton(
              onPressed: () => showAddDeviceByIdentifierDialog(context),
              child: const Text('添加设备'),
            ),
          ],
        ),
      ),
    );
  }

  // 获取当前的设备列表
  List<DiscoveredDevice> _getDevices() {
    final state = _discoveryBloc.state;
    if (state is DeviceDiscoveryLoaded) {
      return state.devices;
    }
    return [];
  }

  // 确认删除设备
  void _confirmDeleteDevice(BuildContext context, DiscoveredDevice device) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('删除设备'),
        content: Text('确定要删除设备 "${device.name}" 吗？'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('取消'),
          ),
          FilledButton(
            style: FilledButton.styleFrom(
              backgroundColor: Colors.red,
            ),
            onPressed: () {
              _deleteDevice(device);
              Navigator.pop(context);
            },
            child: const Text('删除'),
          ),
        ],
      ),
    );
  }

  // 删除设备
  void _deleteDevice(DiscoveredDevice device) {
    _discoveryBloc.add(DeleteDevice(device.id));

    // 显示删除成功消息
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('已删除设备 "${device.name}"'),
        action: SnackBarAction(
          label: '撤销',
          onPressed: () {
            // 恢复设备
            _discoveryBloc.add(AddManualDevice(device));
          },
        ),
      ),
    );
  }

  void _connectToDevice(BuildContext context, DiscoveredDevice device) {
    // 连接到设备后，将用户导航到设备主界面
    final wsUrl = device.wsUrl;
    String httpUrl = "http://${device.ipAddress}:${device.port}/api";
    HttpUtil.instance.rebase(httpUrl);
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => VDMHomePage(
            initialWebSocketUrl: wsUrl,
            deviceId: device.id,
            ip: device.ipAddress),
      ),
    );
  }
}

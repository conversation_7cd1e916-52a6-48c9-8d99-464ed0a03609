import 'package:flutter/material.dart';
import 'package:tdesign_flutter/tdesign_flutter.dart';

class DeviceSaveItem {
  final String deviceIdentifier;
  final String? name;
  final String? ipAddress;
  final int? port;

  DeviceSaveItem(
      {required this.deviceIdentifier, this.name, this.ipAddress, this.port});
}

class DeviceSave extends StatefulWidget {
  final Function(DeviceSaveItem) onSave;
  const DeviceSave({
    super.key,
    required this.onSave,
  });

  @override
  State<DeviceSave> createState() => _DeviceSaveState();
}

class _DeviceSaveState extends State<DeviceSave> {
  late final TextEditingController _identifierController;
  late final TextEditingController _nameController;
  late final TextEditingController _ipController;

  final _formKey = GlobalKey<FormState>();
  bool loading = false;
  @override
  void initState() {
    super.initState();
    _identifierController = TextEditingController();
    _nameController = TextEditingController();
    _ipController = TextEditingController();
  }

  @override
  void dispose() {
    _identifierController.dispose();
    _nameController.dispose();
    _ipController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        backgroundColor: Colors.white,
        appBar: AppBar(
          title: const Text('添加设备'),
        ),
        body: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Form(
            key: _formKey,
            autovalidateMode: AutovalidateMode.onUserInteraction,
            child: Column(
              spacing: 12.0,
              children: <Widget>[
                TextFormField(
                    controller: _identifierController,
                    decoration: const InputDecoration(
                      labelText: '设备ID/序列号',
                      hintText: '例如: SN12345678 或 ABC123',
                      prefixIcon: Icon(Icons.badge),
                    ),
                    onChanged: (value) {
                      setState(() {});
                    },
                    // 校验不能为空
                    validator: (v) {
                      return v == null || v.trim().isNotEmpty
                          ? null
                          : '设备ID/序列号不能为空';
                    }),
                TextFormField(
                  controller: _nameController,
                  decoration: const InputDecoration(
                    labelText: '设备名称(可选)',
                    hintText: '自定义设备名称',
                    prefixIcon: Icon(Icons.edit),
                  ),
                  onChanged: (value) {
                    setState(() {});
                  },
                ),
                TextFormField(
                  controller: _ipController,
                  decoration: const InputDecoration(
                    labelText: 'IP地址(可选)',
                    hintText: '例如: *************',
                    prefixIcon: Icon(Icons.lan),
                  ),
                  onChanged: (value) {
                    setState(() {});
                  },
                ),
                Padding(
                  padding: const EdgeInsets.only(top: 24),
                  child: ConstrainedBox(
                    constraints: const BoxConstraints.expand(height: 55.0),
                    child: TDButton(
                      onTap: () => _onSave(context),
                      text: '添加',
                      size: TDButtonSize.large,
                      type: TDButtonType.fill,
                      theme: TDButtonTheme.primary,
                      isBlock: true,
                      disabled: loading || _identifierController.text.isEmpty,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ));
  }

  void _onSave(context) async {
    if (loading) return;

    // 表单验证
    if ((_formKey.currentState as FormState).validate()) {
      setState(() {
        loading = true;
      });
      try {
        // 添加设备
        final deviceIdentifier = _identifierController.text.trim();
        final deviceName = _nameController.text.trim();
        final ipAddress = _ipController.text.trim();

        widget.onSave(DeviceSaveItem(
            deviceIdentifier: deviceIdentifier,
            name: deviceName.isNotEmpty ? deviceName : null,
            ipAddress: ipAddress.isNotEmpty ? ipAddress : "*************",
            port: 9999));
        Navigator.pop(context);
        TDMessage.showMessage(
          context: context,
          content: '添加成功',
          theme: MessageTheme.success,
          duration: 3000,
        );
      } catch (e) {
        TDToast.showText(e.toString(), context: context);
      } finally {}
    } else {}
  }
}

import 'package:flutter/material.dart';
import 'package:intl/intl.dart'; // 用于格式化日期

/// Cron表达式构建器组件
/// 提供每日粒度的Cron表达式可视化选择界面
/// 包含表达式验证和下次执行时间预览功能
///sec   min   hour   day of month   month   day of week
/// *     *     *      *              *       *
class DailyCronBuilder extends StatefulWidget {
  /// 初始的Cron表达式，格式为 "分 时 * * *"
  final String? initialCronExpression;

  /// 当Cron表达式改变时的回调函数
  final Function(String cronExpression, String description)? onCronExpressionChanged;

  /// 主题颜色
  final Color? primaryColor;

  /// 背景颜色
  final Color? backgroundColor;

  /// 是否显示复制按钮
  final bool showCopyButton;

  /// 是否显示重置按钮
  final bool showResetButton;

  /// 是否显示生成按钮
  final bool showGenerateButton;

  /// 是否显示下次执行时间预览
  final bool showNextExecutionPreview;

  /// 预览显示的未来执行次数
  final int previewCount;

  /// 是否使用深色模式
  final bool? useDarkMode;

  const DailyCronBuilder({
    Key? key,
    this.initialCronExpression,
    this.onCronExpressionChanged,
    this.primaryColor,
    this.backgroundColor,
    this.showCopyButton = true,
    this.showResetButton = true,
    this.showGenerateButton = true,
    this.showNextExecutionPreview = true,
    this.previewCount = 5,
    this.useDarkMode,
  }) : super(key: key);

  @override
  _DailyCronBuilderState createState() => _DailyCronBuilderState();
}

class _DailyCronBuilderState extends State<DailyCronBuilder> {
  // Cron表达式组成部分
  String _minutes = "0";
  String _hours = "0";
  bool _everyHour = false;
  bool _everyMinute = false;

  // 小时多选值
  final List<int> _selectedHours = [];

  // 分钟多选值
  final List<int> _selectedMinutes = [];

  // 生成的Cron表达式
  String _generatedCronExpression = "0 0 * * * *";

  // 人类可读的描述
  String _humanReadableDescription = "每天 00:00 执行";

  // 下次执行时间列表
  List<DateTime> _nextExecutionTimes = [];

  // 表达式是否有效
  bool _isExpressionValid = true;

  // 错误信息
  String? _validationError;

  @override
  void initState() {
    super.initState();
    _parseInitialExpression();
  }

  /// 解析初始Cron表达式
  void _parseInitialExpression() {
    if (widget.initialCronExpression != null && widget.initialCronExpression!.isNotEmpty) {
      try {
        final parts = widget.initialCronExpression!.split(' ');
        if (parts.length >= 5) {
          // 确保前两个部分是我们感兴趣的（分钟和小时）
          _parseMinutesExpression(parts[0]);
          _parseHoursExpression(parts[1]);
          _updateCronExpression();
        }
      } catch (e) {
        print('解析初始Cron表达式失败: $e');
      }
    }

    // 生成初始的下次执行时间
    _generateNextExecutionTimes();
  }

  /// 解析分钟表达式
  void _parseMinutesExpression(String expression) {
    if (expression == "*") {
      _everyMinute = true;
      _minutes = "*";
      _selectedMinutes.clear();
    } else {
      _everyMinute = false;
      _minutes = expression;
      _selectedMinutes.clear();

      // 解析逗号分隔的值
      if (expression.contains(',')) {
        final values = expression.split(',');
        for (var value in values) {
          final parsed = int.tryParse(value.trim());
          if (parsed != null && parsed >= 0 && parsed < 60) {
            _selectedMinutes.add(parsed);
          }
        }
      }
      // 解析单一值
      else {
        final parsed = int.tryParse(expression.trim());
        if (parsed != null && parsed >= 0 && parsed < 60) {
          _selectedMinutes.add(parsed);
        }
      }
    }
  }

  /// 解析小时表达式
  void _parseHoursExpression(String expression) {
    if (expression == "*") {
      _everyHour = true;
      _hours = "*";
      _selectedHours.clear();
    } else {
      _everyHour = false;
      _hours = expression;
      _selectedHours.clear();

      // 解析逗号分隔的值
      if (expression.contains(',')) {
        final values = expression.split(',');
        for (var value in values) {
          final parsed = int.tryParse(value.trim());
          if (parsed != null && parsed >= 0 && parsed < 24) {
            _selectedHours.add(parsed);
          }
        }
      }
      // 解析单一值
      else {
        final parsed = int.tryParse(expression.trim());
        if (parsed != null && parsed >= 0 && parsed < 24) {
          _selectedHours.add(parsed);
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    // 是否使用深色模式
    final bool isDarkMode = widget.useDarkMode ??
        Theme.of(context).brightness == Brightness.dark;

    // 根据模式选择颜色
    final primaryColor = widget.primaryColor ??
        (isDarkMode ? Colors.tealAccent[700] : Theme.of(context).primaryColor);
    final backgroundColor = widget.backgroundColor ??
        (isDarkMode ? Colors.grey[800] : Colors.grey[200]);
    final textColor = isDarkMode ? Colors.white : Colors.black;
    final secondaryTextColor = isDarkMode ? Colors.grey[400] : Colors.grey[700];
    final cardColor = isDarkMode ? Colors.grey[900] : Colors.white;
    final chipBackgroundColor = isDarkMode ? Colors.grey[700] : Colors.grey[200];

    return Card(
      elevation: 2,
      margin: EdgeInsets.zero,
      color: cardColor,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 生成的表达式显示区域
            Container(
              padding: const EdgeInsets.all(16.0),
              decoration: BoxDecoration(
                color: backgroundColor,
                borderRadius: BorderRadius.circular(8.0),
                border: _isExpressionValid
                    ? null
                    : Border.all(color: Colors.red, width: 1.5),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Text(
                        'Cron 表达式: ',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          color: textColor,
                        ),
                      ),
                      Expanded(
                        child: Text(
                          _generatedCronExpression,
                          style: TextStyle(
                            fontFamily: 'monospace',
                            color: textColor,
                          ),
                        ),
                      ),
                      if (widget.showCopyButton)
                        IconButton(
                          icon: Icon(Icons.content_copy, color: textColor),
                          onPressed: () {
                            // 复制到剪贴板的功能可以在这里实现
                          },
                          tooltip: '复制到剪贴板',
                        ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  Text(
                    _humanReadableDescription,
                    style: TextStyle(
                      color: secondaryTextColor,
                      fontStyle: FontStyle.italic,
                    ),
                  ),

                  // 错误提示
                  if (!_isExpressionValid && _validationError != null)
                    Padding(
                      padding: const EdgeInsets.only(top: 8.0),
                      child: Text(
                        _validationError!,
                        style: const TextStyle(
                          color: Colors.red,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                ],
              ),
            ),

            // 下次执行时间预览
            if (widget.showNextExecutionPreview && _isExpressionValid)
              _buildNextExecutionPreview(textColor, secondaryTextColor!),

            const SizedBox(height: 24),

            _buildSectionTitle('小时', textColor),
            _buildHourSelector(primaryColor!, textColor, secondaryTextColor!, isDarkMode, chipBackgroundColor!),

            const SizedBox(height: 16),

            // 分钟选择
            _buildSectionTitle('分钟', textColor),
            _buildMinuteSelector(primaryColor!, textColor, secondaryTextColor!, isDarkMode, chipBackgroundColor!),
            // 小时选择

            const SizedBox(height: 24),

            // 按钮行
            if (widget.showResetButton || widget.showGenerateButton)
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  if (widget.showResetButton)
                    ElevatedButton(
                      onPressed: _resetToDefault,
                      child: Text('重置', style: TextStyle(color: isDarkMode ? Colors.black : Colors.white)),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: isDarkMode ? Colors.grey[400] : Colors.grey,
                      ),
                    ),
                  if (widget.showGenerateButton)
                    ElevatedButton(
                      onPressed: _notifyCronExpressionChanged,
                      child: Text('生成表达式', style: TextStyle(color: isDarkMode ? Colors.black : Colors.white)),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: primaryColor,
                      ),
                    ),
                ],
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildNextExecutionPreview(Color textColor, Color secondaryTextColor) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const SizedBox(height: 16),
        Text(
          '未来${widget.previewCount}次执行时间:',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: textColor,
          ),
        ),
        const SizedBox(height: 8),
        if (_nextExecutionTimes.isEmpty)
          Text(
            '无法预测执行时间',
            style: TextStyle(
              color: secondaryTextColor,
              fontStyle: FontStyle.italic,
            ),
          )
        else
          Column(
            children: _nextExecutionTimes.map((dateTime) {
              final dateFormat = DateFormat('yyyy-MM-dd HH:mm:ss');
              return Padding(
                padding: const EdgeInsets.symmetric(vertical: 4.0),
                child: Row(
                  children: [
                    Icon(Icons.schedule, size: 16, color: secondaryTextColor),
                    const SizedBox(width: 8),
                    Text(
                      dateFormat.format(dateTime),
                      style: TextStyle(
                        color: textColor,
                        fontFamily: 'monospace',
                      ),
                    ),
                  ],
                ),
              );
            }).toList(),
          ),
      ],
    );
  }

  Widget _buildSectionTitle(String title, Color textColor) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8.0),
      child: Text(
        title,
        style: TextStyle(
          fontSize: 18,
          fontWeight: FontWeight.bold,
          color: textColor,
        ),
      ),
    );
  }

  Widget _buildMinuteSelector(Color primaryColor, Color textColor, Color secondaryTextColor, bool isDarkMode, Color chipBackgroundColor) {
    return Column(
      children: [
        // 每分钟选项
        SwitchListTile(
          title: Text('每分钟', style: TextStyle(color: textColor)),
          value: _everyMinute,
          activeColor: primaryColor,
          onChanged: (value) {
            setState(() {
              _everyMinute = value;
              if (value) {
                _minutes = "*";
                _selectedMinutes.clear();
              } else {
                _minutes = "0";
                _selectedMinutes.add(0);
              }
              _updateCronExpression();
            });
          },
        ),

        // 如果不是每分钟，则显示选择器
        if (!_everyMinute)
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16.0),
                child: Text(
                  '选择特定分钟:',
                  style: TextStyle(color: textColor),
                ),
              ),

              // 简化选择器 - 常用值的快捷选择
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16.0),
                child: Wrap(
                  spacing: 8.0,
                  children: [0, 15, 30, 45].map((minute) {
                    final isSelected = _selectedMinutes.contains(minute);
                    return ActionChip(
                      label: Text(
                        '$minute',
                        style: TextStyle(
                          color: isSelected ? (isDarkMode ? Colors.black : Colors.white) : textColor,
                        ),
                      ),
                      backgroundColor: isSelected ? primaryColor : chipBackgroundColor,
                      onPressed: () {
                        setState(() {
                          if (isSelected) {
                            _selectedMinutes.remove(minute);
                          } else {
                            _selectedMinutes.add(minute);
                          }
                          _updateMinuteExpression();
                        });
                      },
                    );
                  }).toList(),
                ),
              ),

              // 自定义分钟输入
              Padding(
                padding: const EdgeInsets.all(16.0),
                child: Row(
                  children: [
                    Text('自定义: ', style: TextStyle(color: textColor)),
                    const SizedBox(width: 8),
                    Expanded(
                      child: TextFormField(
                        initialValue: _selectedMinutes.isEmpty ? '0' : _selectedMinutes.join(','),
                        decoration: InputDecoration(
                          hintText: '输入分钟值, 用逗号分隔 (0-59)',
                          border: const OutlineInputBorder(),
                          contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                          hintStyle: TextStyle(color: secondaryTextColor),
                          enabledBorder: OutlineInputBorder(
                            borderSide: BorderSide(color: isDarkMode ? Colors.grey[600]! : Colors.grey[300]!),
                          ),
                        ),
                        style: TextStyle(color: textColor),
                        keyboardType: TextInputType.number,
                        onChanged: (value) {
                          _selectedMinutes.clear();
                          if (value.isNotEmpty) {
                            final mins = value.split(',');
                            for (var min in mins) {
                              final parsed = int.tryParse(min.trim());
                              if (parsed != null && parsed >= 0 && parsed < 60) {
                                _selectedMinutes.add(parsed);
                              }
                            }
                          }
                          _updateMinuteExpression();
                        },
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
      ],
    );
  }

  Widget _buildHourSelector(Color primaryColor, Color textColor, Color secondaryTextColor, bool isDarkMode, Color chipBackgroundColor) {
    return Column(
      children: [
        // 每小时选项
        SwitchListTile(
          title: Text('每小时', style: TextStyle(color: textColor)),
          value: _everyHour,
          activeColor: primaryColor,
          onChanged: (value) {
            setState(() {
              _everyHour = value;
              if (value) {
                _hours = "*";
                _selectedHours.clear();
              } else {
                _hours = "0";
                _selectedHours.add(0);
              }
              _updateCronExpression();
            });
          },
        ),

        // 如果不是每小时，则显示选择器
        if (!_everyHour)
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16.0),
                child: Text(
                  '选择特定小时:',
                  style: TextStyle(color: textColor),
                ),
              ),

              // 小时选择器 - 网格布局
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16.0),
                child: GridView.builder(
                  shrinkWrap: true,
                  physics: const NeverScrollableScrollPhysics(),
                  gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                    crossAxisCount: 6,
                    childAspectRatio: 1.5,
                    crossAxisSpacing: 8,
                    mainAxisSpacing: 8,
                  ),
                  itemCount: 24,
                  itemBuilder: (context, index) {
                    final isSelected = _selectedHours.contains(index);
                    return InkWell(
                      onTap: () {
                        setState(() {
                          if (isSelected) {
                            _selectedHours.remove(index);
                          } else {
                            _selectedHours.add(index);
                          }
                          _updateHourExpression();
                        });
                      },
                      child: Container(
                        decoration: BoxDecoration(
                          color: isSelected ? primaryColor : chipBackgroundColor,
                          borderRadius: BorderRadius.circular(4),
                        ),
                        alignment: Alignment.center,
                        child: Text(
                          '$index',
                          style: TextStyle(
                            color: isSelected ? (isDarkMode ? Colors.black : Colors.white) : textColor,
                            fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                          ),
                        ),
                      ),
                    );
                  },
                ),
              ),

              // 时间段快捷选择
              Padding(
                padding: const EdgeInsets.all(16.0),
                child: Wrap(
                  spacing: 8.0,
                  runSpacing: 8.0,
                  children: [
                    _buildTimeRangeChip('工作时间 (9-17)', 9, 17, primaryColor, textColor, isDarkMode, chipBackgroundColor),
                    _buildTimeRangeChip('夜间 (18-23)', 18, 23, primaryColor, textColor, isDarkMode, chipBackgroundColor),
                    _buildTimeRangeChip('凌晨 (0-5)', 0, 5, primaryColor, textColor, isDarkMode, chipBackgroundColor),
                  ],
                ),
              ),
            ],
          ),
      ],
    );
  }

  Widget _buildTimeRangeChip(String label, int start, int end, Color primaryColor, Color textColor, bool isDarkMode, Color chipBackgroundColor) {
    final List<int> hours = List.generate(end - start + 1, (index) => start + index);
    final bool allSelected = hours.every((hour) => _selectedHours.contains(hour));

    return ActionChip(
      label: Text(
        label,
        style: TextStyle(
          color: allSelected ? (isDarkMode ? Colors.black : Colors.white) : textColor,
        ),
      ),
      backgroundColor: allSelected ? primaryColor : chipBackgroundColor,
      onPressed: () {
        setState(() {
          if (allSelected) {
            // 如果全选了，则取消选择
            for (var hour in hours) {
              _selectedHours.remove(hour);
            }
          } else {
            // 否则全选
            for (var hour in hours) {
              if (!_selectedHours.contains(hour)) {
                _selectedHours.add(hour);
              }
            }
          }
          _updateHourExpression();
        });
      },
    );
  }

  void _updateMinuteExpression() {
    if (_everyMinute) {
      _minutes = "*";
    } else if (_selectedMinutes.isEmpty) {
      _minutes = "0";
    } else {
      // 排序并去重
      _selectedMinutes.sort();
      _minutes = _selectedMinutes.join(',');
    }
    _updateCronExpression();
  }

  void _updateHourExpression() {
    if (_everyHour) {
      _hours = "*";
    } else if (_selectedHours.isEmpty) {
      _hours = "0";
    } else {
      // 排序并去重
      _selectedHours.sort();
      _hours = _selectedHours.join(',');
    }
    _updateCronExpression();
  }

  void _updateCronExpression() {
    setState(() {
      // 更新Cron表达式，保持每日粒度（* * *表示每天）
      _generatedCronExpression = "$_minutes $_hours * * * *";

      // 验证表达式
      _validateExpression();

      // 更新描述
      _updateHumanReadableDescription();

      // 生成下次执行时间
      if (_isExpressionValid) {
        _generateNextExecutionTimes();
      }

      // 通知父组件表达式已更改
      if (widget.onCronExpressionChanged != null && _isExpressionValid) {
        widget.onCronExpressionChanged!(_generatedCronExpression, _humanReadableDescription);
      }
    });
  }

  void _validateExpression() {
    // 基本验证
    _isExpressionValid = true;
    _validationError = null;

    // 检查分钟部分
    if (!_everyMinute && _selectedMinutes.isEmpty) {
      _isExpressionValid = false;
      _validationError = "请至少选择一个分钟值";
      return;
    }

    // 检查小时部分
    if (!_everyHour && _selectedHours.isEmpty) {
      _isExpressionValid = false;
      _validationError = "请至少选择一个小时值";
      return;
    }

    // 检查频率 - 避免过于频繁的执行
    if (_everyMinute && _everyHour) {
      // 每分钟执行可能需要警告但不视为错误
      // _validationError = "警告: 每分钟执行会非常频繁";
    }
  }

  void _updateHumanReadableDescription() {
    String minuteDesc;
    String hourDesc;

    // 分钟描述
    if (_minutes == "*") {
      minuteDesc = "每分钟";
    } else if (_minutes.contains(',')) {
      minuteDesc = "在第 $_minutes 分钟";
    } else {
      minuteDesc = "在第 $_minutes 分钟";
    }

    // 小时描述
    if (_hours == "*") {
      hourDesc = "每小时";
    } else if (_hours.contains(',')) {
      hourDesc = "在 $_hours 点";
    } else {
      hourDesc = "在 $_hours 点";
    }

    // 组合描述
    if (_minutes == "*" && _hours == "*") {
      _humanReadableDescription = "每天每小时每分钟执行";
    } else if (_minutes == "*") {
      _humanReadableDescription = "每天$hourDesc的每分钟执行";
    } else if (_hours == "*") {
      _humanReadableDescription = "每天每小时的$minuteDesc执行";
    } else {
      // 处理显示为更友好的格式
      if (_minutes.contains(',') || _hours.contains(',')) {
        _humanReadableDescription = "每天$hourDesc的$minuteDesc执行";
      } else {
        // 单一时间点的简化显示
        final hour = int.parse(_hours);
        final minute = int.parse(_minutes);
        final formattedHour = hour.toString().padLeft(2, '0');
        final formattedMinute = minute.toString().padLeft(2, '0');
        _humanReadableDescription = "每天 $formattedHour:$formattedMinute 执行";
      }
    }
  }

  /// 生成未来几次执行时间
  void _generateNextExecutionTimes() {
    _nextExecutionTimes = [];

    if (!_isExpressionValid) return;

    try {
      DateTime now = DateTime.now();

      // 对于每日执行的Cron，计算未来执行时间相对简单
      List<int> minutesToExecute = [];
      List<int> hoursToExecute = [];

      // 解析分钟
      if (_minutes == "*") {
        minutesToExecute = List.generate(60, (index) => index);
      } else {
        minutesToExecute = _selectedMinutes;
      }

      // 解析小时
      if (_hours == "*") {
        hoursToExecute = List.generate(24, (index) => index);
      } else {
        hoursToExecute = _selectedHours;
      }

      // 排序确保时间顺序
      minutesToExecute.sort();
      hoursToExecute.sort();

      // 生成未来的执行时间
      DateTime current = now;
      int count = 0;

      // 从当前时间开始，找到下一个执行时间点
      while (_nextExecutionTimes.length < widget.previewCount) {
        // 防止无限循环
        if (count > 1000) break;
        count++;

        for (int hour in hoursToExecute) {
          for (int minute in minutesToExecute) {
            DateTime potentialTime = DateTime(
                current.year,
                current.month,
                current.day,
                hour,
                minute
            );

            // 如果时间点在当前时间之后，加入列表
            if (potentialTime.isAfter(now)) {
              _nextExecutionTimes.add(potentialTime);

              // 如果已经收集了足够的时间点，退出循环
              if (_nextExecutionTimes.length >= widget.previewCount) {
                break;
              }
            }
          }

          // 如果已经收集了足够的时间点，退出循环
          if (_nextExecutionTimes.length >= widget.previewCount) {
            break;
          }
        }

        // 如果当天没有足够的未来时间点，移到下一天继续寻找
        if (_nextExecutionTimes.length < widget.previewCount) {
          current = DateTime(current.year, current.month, current.day + 1);
          now = DateTime(now.year, now.month, now.day, 0, 0); // 重置为下一天的开始
        }
      }

      // 确保时间点按时间顺序排序
      _nextExecutionTimes.sort();

      // 限制数量
      if (_nextExecutionTimes.length > widget.previewCount) {
        _nextExecutionTimes = _nextExecutionTimes.sublist(0, widget.previewCount);
      }
    } catch (e) {
      print('生成执行时间出错: $e');
      _nextExecutionTimes = [];
    }
  }

  void _resetToDefault() {
    setState(() {
      _minutes = "0";
      _hours = "0";
      _everyHour = false;
      _everyMinute = false;
      _selectedHours.clear();
      _selectedHours.add(0);
      _selectedMinutes.clear();
      _selectedMinutes.add(0);
      _updateCronExpression();
    });
  }

  void _notifyCronExpressionChanged() {
    if (widget.onCronExpressionChanged != null && _isExpressionValid) {
      widget.onCronExpressionChanged!(_generatedCronExpression, _humanReadableDescription);
    }
  }
}

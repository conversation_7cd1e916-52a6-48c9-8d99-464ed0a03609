import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../bloc/wy_device_blocs.dart';
import 'cloud_connection_settings_view_model.dart';

class CloudConnectionSettings extends StatelessWidget {

  const CloudConnectionSettings({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {

    final wyDeviceBloc = context.read<WyDeviceBloc>();
    return ChangeNotifierProvider(
      create: (_) => CloudConnectionViewModel(wyDeviceBloc),
      child: const _CloudConnectionSettingsContent(),
    );
  }
}

class _CloudConnectionSettingsContent extends StatelessWidget {
  const _CloudConnectionSettingsContent();

  @override
  Widget build(BuildContext context) {
    final viewModel = Provider.of<CloudConnectionViewModel>(context);

    if (viewModel.isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    return Form(
      key: GlobalKey<FormState>(),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          _buildHeader(context),
          Expanded(
            child: _buildFormContent(context, viewModel),
          ),
          if (viewModel.hasChanges)
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: ElevatedButton.icon(
                onPressed: () => viewModel.saveConfig(),
                icon: const Icon(Icons.save),
                label: const Text('保存配置'),
                style: ElevatedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(vertical: 12),
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Consumer<CloudConnectionViewModel>(
      builder: (context, viewModel, _) {
        return Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 12.0),
          child: Row(
            children: [
              const Text('云平台配置',
                style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
              ),
              const Spacer(),
              _buildStatusIndicator(viewModel),
            ],
          ),
        );
      },
    );
  }

  Widget _buildStatusIndicator(CloudConnectionViewModel viewModel) {
    if (viewModel.isSaving) {
      return const Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          SizedBox(
            width: 16,
            height: 16,
            child: CircularProgressIndicator(
              strokeWidth: 2,
            ),
          ),
          SizedBox(width: 8),
          Text('保存中...', style: TextStyle(fontSize: 14, color: Colors.grey)),
        ],
      );
    } else if (viewModel.saveError != null) {
      return const Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(Icons.error_outline, color: Colors.red, size: 16),
          SizedBox(width: 4),
          Text('保存失败', style: TextStyle(fontSize: 14, color: Colors.red)),
        ],
      );
    } else if (viewModel.hasChanges) {
      return const Text('配置已更改',
        style: TextStyle(color: Colors.orange, fontSize: 14),
      );
    } else {
      return const Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(Icons.check_circle, color: Colors.green, size: 16),
          SizedBox(width: 4),
          Text('已保存', style: TextStyle(fontSize: 14, color: Colors.green)),
        ],
      );
    }
  }

  Widget _buildFormContent(BuildContext context, CloudConnectionViewModel viewModel) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildCloudCard(context, 1),
          const SizedBox(height: 16),
          _buildCloudCard(context, 2),
          const SizedBox(height: 80), // Extra space at the bottom for save button
        ],
      ),
    );
  }

  Widget _buildCloudCard(BuildContext context, int cloudNum) {
    final viewModel = Provider.of<CloudConnectionViewModel>(context);
    bool isEnabled = cloudNum == 1 ? viewModel.isCloud1Enabled : viewModel.isCloud2Enabled;
    bool isConnected = cloudNum == 1 ? viewModel.isCloud1Connected : viewModel.isCloud2Connected;
    String cloudKey = cloudNum == 1 ? 'cloud1' : 'cloud2';

    return Card(
      elevation: 2,
      margin: EdgeInsets.zero,
      clipBehavior: Clip.antiAlias,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          // Header with toggle and status
          _buildCloudHeader(context, cloudNum, isEnabled, isConnected),

          // Connection button and details if enabled
          if (isEnabled) ...[
            // _buildConnectionButton(context, cloudKey, isConnected),

            // Cloud config if enabled
            if (viewModel.cloudConfig.containsKey(cloudKey))
              _buildCloudConfigContent(context, cloudKey),
          ],
        ],
      ),
    );
  }

  Widget _buildCloudHeader(BuildContext context, int cloudNum, bool isEnabled, bool isConnected) {
    final viewModel = Provider.of<CloudConnectionViewModel>(context);

    return Container(
      color: isEnabled ? Colors.blue.shade50 : Colors.grey.shade50,
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      child: Row(
        children: [
          Icon(
            Icons.cloud,
            size: 24,
            color: isEnabled ? Colors.blue : Colors.grey,
          ),
          const SizedBox(width: 12),
          Text(
            '云平台 $cloudNum',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w500,
              color: isEnabled ? Colors.blue.shade700 : Colors.grey.shade700,
            ),
          ),
          const Spacer(),
          // Status indicator
          if (isEnabled)
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: isConnected ? Colors.green.withOpacity(0.2) : Colors.red.withOpacity(0.2),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(
                    isConnected ? Icons.check_circle : Icons.error_outline,
                    size: 16,
                    color: isConnected ? Colors.green : Colors.red,
                  ),
                  const SizedBox(width: 4),
                  Text(
                    isConnected ? '已连接' : '未连接',
                    style: TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                      color: isConnected ? Colors.green : Colors.red,
                    ),
                  ),
                ],
              ),
            ),

          const SizedBox(width: 12),

          // Toggle switch
          Switch(
            value: isEnabled,
            onChanged: (value) => viewModel.toggleCloud(cloudNum, value),
            activeColor: Colors.blue,
          ),
        ],
      ),
    );
  }

  Widget _buildConnectionButton(BuildContext context, String cloudKey, bool isConnected) {
    final viewModel = Provider.of<CloudConnectionViewModel>(context);

    return Padding(
      padding: const EdgeInsets.fromLTRB(16, 8, 16, 8),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.end,
        children: [
          OutlinedButton.icon(
            icon: Icon(isConnected ? Icons.link_off : Icons.link),
            label: Text(isConnected ? '断开连接' : '连接'),
            style: OutlinedButton.styleFrom(
              foregroundColor: isConnected ? Colors.red : Colors.green,
              side: BorderSide(
                color: isConnected ? Colors.red : Colors.green,
              ),
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            ),
            onPressed: () {
              if (isConnected) {
                viewModel.disconnectCloud(cloudKey);
              } else {
                viewModel.connectCloud(cloudKey);
              }
            },
          ),
        ],
      ),
    );
  }

  Widget _buildCloudConfigContent(BuildContext context, String cloudKey) {
    final viewModel = Provider.of<CloudConnectionViewModel>(context);
    final cloudData = viewModel.cloudConfig[cloudKey] as Map<String, dynamic>? ??
        viewModel.getDefaultCloudConfig();
    final isConnected = cloudKey == 'cloud1' ? viewModel.isCloud1Connected : viewModel.isCloud2Connected;
    final isPlatformInteagle = cloudData['platform_connect'] == 'Inteagle';

    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Connection status indicator
          // if (isConnected)
          //   _buildConnectionStatusCard(context, cloudData, isPlatformInteagle),

          const SizedBox(height: 16),

          // Platform type selection
          _buildPlatformTypeDropdown(context, cloudKey, cloudData),

          // Platform specific content
          const SizedBox(height: 16),
          isPlatformInteagle
              ? _buildInteagleContent(context)
              : _buildStandardConfigContent(context, cloudKey, cloudData),
        ],
      ),
    );
  }

  Widget _buildConnectionStatusCard(BuildContext context, Map<String, dynamic> cloudData, bool isPlatformInteagle) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.green.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.green.withOpacity(0.3)),
      ),
      child: Row(
        children: [
          const Icon(Icons.info_outline, color: Colors.green),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              isPlatformInteagle
                  ? '当前已连接到 Inteagle 云平台'
                  : '当前已连接到 ${cloudData['host']}:${cloudData['port']}',
              style: const TextStyle(
                color: Colors.green,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPlatformTypeDropdown(BuildContext context, String cloudKey, Map<String, dynamic> cloudData) {
    final viewModel = Provider.of<CloudConnectionViewModel>(context);
    final types = viewModel.platformTypes;

    return DropdownButtonFormField<String>(
      decoration: InputDecoration(
        labelText: '连接平台',
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
        ),
        contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
        filled: true,
        fillColor: Colors.grey.shade50,
      ),
      value: cloudData['platform_connect'],
      items: types.map((type) {
        return DropdownMenuItem<String>(
          value: type,
          child: Text(
            type,
            style: const TextStyle(fontWeight: FontWeight.w500),
          ),
        );
      }).toList(),
      onChanged: (value) {
        if (value != null) {
          viewModel.updatePlatformType(cloudKey, value);
        }
      },
    );
  }

  Widget _buildInteagleContent(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 32, horizontal: 16),
      child: Center(
        child: Column(
          children: [
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.blue.shade50,
                shape: BoxShape.circle,
              ),
              child: Icon(
                Icons.cloud_done,
                size: 48,
                color: Colors.blue.shade700,
              ),
            ),
            const SizedBox(height: 24),
            Text(
              'Inteagle 云平台已预配置',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
                color: Colors.blue.shade800,
              ),
            ),
            const SizedBox(height: 12),
            Text(
              // '无需额外设置，直接点击"连接"按钮即可开始使用',
              '无需额外设置',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Colors.grey[600],
                fontSize: 16,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStandardConfigContent(BuildContext context, String cloudKey, Map<String, dynamic> cloudData) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildConnectionFields(context, cloudKey, cloudData),
        const SizedBox(height: 24),
        _AdvancedOptionsSection(cloudKey: cloudKey, cloudData: cloudData),
        const SizedBox(height: 24),
        _TopicsSection(cloudKey: cloudKey, cloudData: cloudData),
      ],
    );
  }

  Widget _buildConnectionFields(BuildContext context, String cloudKey, Map<String, dynamic> cloudData) {
    final viewModel = Provider.of<CloudConnectionViewModel>(context, listen: false);

    return StatefulBuilder(
      builder: (context, setFieldState) {
        final hostController = TextEditingController(text: cloudData['host'] ?? '');
        final portController = TextEditingController(text: (cloudData['port'] ?? 1883).toString());
        final clientIdController = TextEditingController(text: cloudData['client_id'] ?? '');
        final usernameController = TextEditingController(text: cloudData['username'] ?? '');
        final passwordController = TextEditingController(text: cloudData['password'] ?? '');

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Colors.grey.shade100,
                borderRadius: BorderRadius.circular(8),
              ),
              child: const Text(
                '基本连接配置',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                  color: Colors.black87,
                ),
              ),
            ),
            const SizedBox(height: 16),
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Expanded(
                  flex: 3,
                  child: TextField(
                    controller: hostController,
                    decoration: InputDecoration(
                      labelText: '服务器',
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                      hintText: 'localhost或mqtt.example.com',
                      prefixIcon: const Icon(Icons.dns),
                    ),
                    onEditingComplete: () {
                      viewModel.updateCloudConfig(cloudKey, 'host', hostController.text);
                      FocusScope.of(context).nextFocus();
                    },
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  flex: 1,
                  child: TextField(
                    controller: portController,
                    decoration: InputDecoration(
                      labelText: '端口',
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                      hintText: '1883',
                    ),
                    keyboardType: TextInputType.number,
                    onEditingComplete: () {
                      final port = int.tryParse(portController.text) ?? 1883;
                      viewModel.updateCloudConfig(cloudKey, 'port', port);
                      FocusScope.of(context).nextFocus();
                    },
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            TextField(
              controller: clientIdController,
              decoration: InputDecoration(
                labelText: '客户端ID',
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
                hintText: '设备唯一标识符',
                prefixIcon: const Icon(Icons.perm_identity),
              ),
              onEditingComplete: () {
                viewModel.updateCloudConfig(cloudKey, 'client_id', clientIdController.text);
                FocusScope.of(context).nextFocus();
              },
            ),
            const SizedBox(height: 16),
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Expanded(
                  child: TextField(
                    controller: usernameController,
                    decoration: InputDecoration(
                      labelText: 'username',
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                      hintText: 'username',
                      prefixIcon: const Icon(Icons.person),
                    ),
                    onEditingComplete: () {
                      viewModel.updateCloudConfig(cloudKey, 'username', usernameController.text);
                      FocusScope.of(context).nextFocus();
                    },
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: TextField(
                    controller: passwordController,
                    decoration: InputDecoration(
                      labelText: 'password',
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                      hintText: 'password',
                      prefixIcon: const Icon(Icons.lock),
                    ),
                    onEditingComplete: () {
                      viewModel.updateCloudConfig(cloudKey, 'password', passwordController.text);
                      FocusScope.of(context).unfocus();
                    },
                  ),
                ),
              ],
            ),
          ],
        );
      },
    );
  }

  String _getQosDescription(int qos) {
    switch (qos) {
      case 0:
        return '最多发送一次';
      case 1:
        return '至少发送一次';
      case 2:
        return '只发送一次';
      default:
        return '';
    }
  }
}

// 完全自定义的持久化展开卡片组件
class _PersistentExpansionCard extends StatefulWidget {
  final Widget title;
  final Widget? leading;
  final List<Widget> children;
  final bool initiallyExpanded;

  const _PersistentExpansionCard({
    required this.title,
    this.leading,
    required this.children,
    this.initiallyExpanded = false,
  });

  @override
  State<_PersistentExpansionCard> createState() => _PersistentExpansionCardState();
}

class _PersistentExpansionCardState extends State<_PersistentExpansionCard> with SingleTickerProviderStateMixin {
  late bool _isExpanded;

  // 使用在内存中持久保存的展开状态
  static final Map<String, bool> _expansionState = {};

  // 生成此组件的唯一键
  String get _stateKey => '${widget.title.toString()}_${widget.leading.toString()}';

  // 添加旋转动画控制器
  late AnimationController _controller;
  late Animation<double> _iconTurns;

  @override
  void initState() {
    super.initState();
    // 尝试从持久状态获取，如果不存在则使用初始值
    _isExpanded = _expansionState[_stateKey] ?? widget.initiallyExpanded;

    // 初始化动画控制器
    _controller = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );
    _iconTurns = Tween<double>(begin: 0.0, end: 0.25).animate(_controller);

    // 根据初始展开状态设置动画
    if (_isExpanded) {
      _controller.value = 1.0;
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 0,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: BorderSide(color: Colors.grey.shade300),
      ),
      margin: EdgeInsets.zero,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 自定义标题栏，不使用ExpansionTile
          InkWell(
            onTap: _toggleExpanded,
            borderRadius: BorderRadius.circular(12),
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Row(
                children: [
                  if (widget.leading != null) ...[
                    widget.leading!,
                    const SizedBox(width: 16),
                  ],
                  Expanded(child: widget.title),
                  RotationTransition(
                    turns: _iconTurns,
                    child: const Icon(Icons.arrow_forward_ios, size: 16),
                  ),
                ],
              ),
            ),
          ),

          // 内容部分，使用AnimatedCrossFade实现平滑过渡
          AnimatedCrossFade(
            firstChild: const SizedBox(height: 0),
            secondChild: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: widget.children,
              ),
            ),
            crossFadeState: _isExpanded ? CrossFadeState.showSecond : CrossFadeState.showFirst,
            duration: const Duration(milliseconds: 200),
          ),
        ],
      ),
    );
  }

  void _toggleExpanded() {
    setState(() {
      _isExpanded = !_isExpanded;
      // 更新动画
      if (_isExpanded) {
        _controller.forward();
      } else {
        _controller.reverse();
      }
      // 保存状态到静态Map
      _expansionState[_stateKey] = _isExpanded;
    });
  }
}

// 专门用于处理高级选项的StatefulWidget，保持输入焦点
class _AdvancedOptionsSection extends StatefulWidget {
  final String cloudKey;
  final Map<String, dynamic> cloudData;

  const _AdvancedOptionsSection({
    required this.cloudKey,
    required this.cloudData,
  });

  @override
  _AdvancedOptionsSectionState createState() => _AdvancedOptionsSectionState();
}

class _AdvancedOptionsSectionState extends State<_AdvancedOptionsSection> {
  // 持久化存储控制器，不会在重建时重新创建
  late TextEditingController _keepAliveController;
  bool _isExpanded = false;

  @override
  void initState() {
    super.initState();
    _keepAliveController = TextEditingController(
        text: widget.cloudData['keep_alive']['secs'].toString()
    );
  }

  @override
  void dispose() {
    _keepAliveController.dispose();
    super.dispose();
  }

  @override
  void didUpdateWidget(_AdvancedOptionsSection oldWidget) {
    super.didUpdateWidget(oldWidget);
    // 只有当值发生实际变化时才更新控制器
    final newValue = widget.cloudData['keep_alive']['secs'].toString();
    if (_keepAliveController.text != newValue) {
      _keepAliveController.text = newValue;
    }
  }

  @override
  Widget build(BuildContext context) {
    final viewModel = Provider.of<CloudConnectionViewModel>(context, listen: false);

    return _PersistentExpansionCard(
      title: const Text(
        '高级选项',
        style: TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.w500,
        ),
      ),
      leading: const Icon(Icons.settings),
      children: [
        Row(
          children: [
            Expanded(
              child: DropdownButtonFormField<int>(
                decoration: InputDecoration(
                  labelText: 'QoS',
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                  helperText: _getQosDescription(widget.cloudData['qos']),
                ),
                value: widget.cloudData['qos'],
                items: [0, 1, 2].map((qos) {
                  return DropdownMenuItem<int>(
                    value: qos,
                    child: Center(child: Text('$qos')),
                  );
                }).toList(),
                onChanged: (value) => viewModel.updateCloudConfig(widget.cloudKey, 'qos', value),
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: TextField(
                controller: _keepAliveController,
                decoration: InputDecoration(
                  labelText: '保活时间 (秒)',
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                  helperText: '连接保持时间',
                ),
                keyboardType: TextInputType.number,
                // 不在onChange中触发更新，而是在编辑完成时
                onEditingComplete: () {
                  final secs = int.tryParse(_keepAliveController.text) ?? 60;
                  viewModel.updateCloudConfig(widget.cloudKey, 'keep_alive', {'secs': secs, 'nanos': 0});
                  FocusScope.of(context).nextFocus();
                },
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),
        Row(
          children: [
            Expanded(
              child: DropdownButtonFormField<String>(
                decoration: InputDecoration(
                  labelText: '数据格式',
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
                value: widget.cloudData['payload_format'],
                items: ['Json', 'Protobuf'].map((format) {
                  return DropdownMenuItem<String>(
                    value: format,
                    child: Text(format),
                  );
                }).toList(),
                onChanged: (value) => viewModel.updateCloudConfig(widget.cloudKey, 'payload_format', value),
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Container(
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey.shade400),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // 添加类似其他输入框的标签
                    Padding(
                      padding: const EdgeInsets.fromLTRB(12, 8, 12, 0),
                      child: Text(
                        '清理会话',
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.grey.shade700,
                        ),
                      ),
                    ),
                    // 开关居中显示
                    Container(
                      height: 46, // 与文本输入框保持一致的高度
                      alignment: Alignment.center,
                      child: Switch(
                        value: widget.cloudData['clean_session'] ?? true,
                        onChanged: (bool value) {
                          viewModel.updateCloudConfig(widget.cloudKey, 'clean_session', value);
                        },
                      ),
                    ),
                 
                  ],
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  String _getQosDescription(int qos) {
    switch (qos) {
      case 0:
        return '最多发送一次';
      case 1:
        return '至少发送一次';
      case 2:
        return '只发送一次';
      default:
        return '';
    }
  }
}

// 专门用于处理主题配置的StatefulWidget，保持输入焦点
class _TopicsSection extends StatefulWidget {
  final String cloudKey;
  final Map<String, dynamic> cloudData;

  const _TopicsSection({
    required this.cloudKey,
    required this.cloudData,
  });

  @override
  _TopicsSectionState createState() => _TopicsSectionState();
}

class _TopicsSectionState extends State<_TopicsSection> {
  // 持久化保存文本控制器
  late TextEditingController _telemetryController;
  late TextEditingController _attributesController;
  late TextEditingController _commandsController;

  @override
  void initState() {
    super.initState();
    final topics = widget.cloudData['topics'] as Map<String, dynamic>;
    _telemetryController = TextEditingController(text: topics['telemetry']);
    _attributesController = TextEditingController(text: topics['attributes']);
    _commandsController = TextEditingController(text: topics['commands']);
  }

  @override
  void dispose() {
    _telemetryController.dispose();
    _attributesController.dispose();
    _commandsController.dispose();
    super.dispose();
  }

  @override
  void didUpdateWidget(_TopicsSection oldWidget) {
    super.didUpdateWidget(oldWidget);
    // 检查并更新所有控制器值，只在实际变化时更新
    final topics = widget.cloudData['topics'] as Map<String, dynamic>;

    if (_telemetryController.text != topics['telemetry']) {
      _telemetryController.text = topics['telemetry'];
    }

    if (_attributesController.text != topics['attributes']) {
      _attributesController.text = topics['attributes'];
    }

    if (_commandsController.text != topics['commands']) {
      _commandsController.text = topics['commands'];
    }
  }

  @override
  Widget build(BuildContext context) {
    final viewModel = Provider.of<CloudConnectionViewModel>(context, listen: false);

    return _PersistentExpansionCard(
      title: const Text(
        '主题配置',
        style: TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.w500,
        ),
      ),
      leading: const Icon(Icons.topic),
      children: [
        TextField(
          controller: _telemetryController,
          decoration: InputDecoration(
            labelText: '遥测主题',
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
            ),
            helperText: '用于发送设备状态和测量数据',
            prefixIcon: const Icon(Icons.sensors),
          ),
          // 仅在编辑完成时更新
          onEditingComplete: () {
            viewModel.updateTopicConfig(widget.cloudKey, 'telemetry', _telemetryController.text);
            FocusScope.of(context).nextFocus();
          },
        ),
        const SizedBox(height: 16),
        TextField(
          controller: _attributesController,
          decoration: InputDecoration(
            labelText: '属性主题',
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
            ),
            helperText: '用于发送设备属性',
            prefixIcon: const Icon(Icons.category),
          ),
          // 仅在编辑完成时更新
          onEditingComplete: () {
            viewModel.updateTopicConfig(widget.cloudKey, 'attributes', _attributesController.text);
            FocusScope.of(context).nextFocus();
          },
        ),
        const SizedBox(height: 16),
        TextField(
          controller: _commandsController,
          decoration: InputDecoration(
            labelText: '命令主题',
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
            ),
            helperText: '用于设备接收命令',
            prefixIcon: const Icon(Icons.terminal),
          ),
          // 仅在编辑完成时更新
          onEditingComplete: () {
            viewModel.updateTopicConfig(widget.cloudKey, 'commands', _commandsController.text);
            FocusScope.of(context).unfocus();
          },
        ),
      ],
    );
  }
}
import 'package:flutter/material.dart';
import 'package:fl_chart/fl_chart.dart';

class DataMonitorPage extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      appBar: AppBar(
        title: Text('数据监测', style: TextStyle(color: Colors.white)),
        backgroundColor: Colors.black,
        actions: [
          IconButton(
            icon: Icon(Icons.settings, color: Colors.white),
            onPressed: () {
              // TODO: 跳转设置页面
            },
          ),
        ],
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: [
            // 实时数据曲线
            Expanded(
              flex: 3,
              child: Card(
                color: Colors.grey[900],
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        '实时数据曲线',
                        style: TextStyle(color: Colors.white, fontSize: 16),
                      ),
                      Expanded(
                        child: MultiLineChart(),
                      ),
                    ],
                  ),
                ),
              ),
            ),
            SizedBox(height: 16),
            // 实时数据表格
            Expanded(
              flex: 2,
              child: Card(
                color: Colors.grey[900],
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        '实时数据表',
                        style: TextStyle(color: Colors.white, fontSize: 16),
                      ),
                      Expanded(
                        child: DataTableWidget(),
                      ),
                    ],
                  ),
                ),
              ),
            ),
            SizedBox(height: 16),
            // 操作按钮
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                ElevatedButton(
                  onPressed: () {
                    // TODO: 启动监测
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.blue,
                  ),
                  child: Text('开始监测'),
                ),
                ElevatedButton(
                  onPressed: () {
                    // TODO: 停止监测
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.red,
                  ),
                  child: Text('停止监测'),
                ),
                ElevatedButton(
                  onPressed: () {
                    // TODO: 导出数据
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.green,
                  ),
                  child: Text('导出数据'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}

// 多曲线图表
class MultiLineChart extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return LineChart(
      LineChartData(
        gridData: FlGridData(show: true),
        titlesData: FlTitlesData(
          leftTitles: AxisTitles(
            sideTitles: SideTitles(
              showTitles: true,
              reservedSize: 40,
              interval: 10,
              getTitlesWidget: (value, meta) => Text(
                '${value.toInt()}°C',
                style: TextStyle(color: Colors.blue, fontSize: 10),
              ),
            ),
          ),
          rightTitles: AxisTitles(
            sideTitles: SideTitles(
              showTitles: true,
              reservedSize: 40,
              interval: 2,
              getTitlesWidget: (value, meta) => Text(
                '${value.toInt()}mm',
                style: TextStyle(color: Colors.orange, fontSize: 10),
              ),
            ),
          ),
          bottomTitles: AxisTitles(
            sideTitles: SideTitles(
              showTitles: true,
              interval: 1,
              getTitlesWidget: (value, meta) => Text(
                '${value.toInt()}s',
                style: TextStyle(color: Colors.white, fontSize: 10),
              ),
            ),
          ),
        ),
        lineBarsData: [
          // 温度曲线
          LineChartBarData(
            spots: [
              FlSpot(0, 20),
              FlSpot(1, 25),
              FlSpot(2, 30),
              FlSpot(3, 35),
            ],
            isCurved: true,
            barWidth: 4,
          ),
          // 位移曲线
          LineChartBarData(
            spots: [
              FlSpot(0, 1),
              FlSpot(1, 1.5),
              FlSpot(2, 2),
              FlSpot(3, 2.5),
            ],
            isCurved: true,
            barWidth: 4,
          ),
        ],
      ),
    );
  }
}

// 数据表格
class DataTableWidget extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      child: DataTable(
        headingRowColor: MaterialStateColor.resolveWith((states) => Colors.grey[850]!),
        columns: const [
          DataColumn(label: Text('时间', style: TextStyle(color: Colors.white))),
          // DataColumn(label: Text('温度', style: TextStyle(color: Colors.white))),
          DataColumn(label: Text('位移', style: TextStyle(color: Colors.white))),
        ],
        rows: List.generate(
          10,
              (index) => DataRow(cells: [
            DataCell(Text('10:${index}0', style: TextStyle(color: Colors.white))),
            // DataCell(Text('${20 + index}°C', style: TextStyle(color: Colors.white))),
            DataCell(Text('${1.5 + index * 0.1}mm', style: TextStyle(color: Colors.white))),
          ]),
        ),
      ),
    );
  }
}

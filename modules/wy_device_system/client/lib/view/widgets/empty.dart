import 'package:flutter/material.dart';
import 'package:tdesign_flutter/tdesign_flutter.dart';

class Empty extends StatelessWidget {
  final IconData icon;
  final String emptyText;
  final String? desc;
  final Widget? child;
  const Empty(
      {super.key,
      this.emptyText = '数据为空',
      required this.icon,
      this.desc,
      this.child});

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            icon,
            size: 60,
            color: TDTheme.of(context).fontGyColor3,
          ),
          const SizedBox(height: 16),
          Text(
            emptyText,
            style: TextStyle(
              color: TDTheme.of(context).fontGyColor3,
              fontSize: 18,
            ),
          ),
          if (desc != null) const SizedBox(height: 8),
          if (desc != null)
            Text(
              desc!,
              style: TextStyle(
                color: TDTheme.of(context).fontGyColor2,
                fontSize: 14,
              ),
            ),
          if (child != null)
            Padding(
              padding: const EdgeInsets.all(24),
              child: child!,
            )
        ],
      ),
    );
  }
}

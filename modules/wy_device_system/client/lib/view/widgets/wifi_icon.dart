import 'package:flutter/material.dart';

class TweenWifiIcon extends StatefulWidget {
  const TweenWifiIcon({super.key});

  @override
  State<TweenWifiIcon> createState() => _TweenWifiIconState();
}

class _TweenWifiIconState extends State<TweenWifiIcon>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<int> _animation;

  final List<IconData> _wifiIcons = [
    Icons.wifi_1_bar,
    Icons.wifi_2_bar,
    Icons.wifi_outlined
  ];

  @override
  void initState() {
    super.initState();

    _controller = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 2000),
    )..repeat();

    _animation = IntTweenSequence(
      begin: 0,
      end: 2, // 对应图标索引
    ).animate(_controller);
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animation,
      builder: (context, child) {
        return Icon(
          _wifiIcons[_animation.value],
        );
      },
    );
  }
}

// 自定义整数补间序列
class IntTweenSequence extends TweenSequence<int> {
  IntTweenSequence({required int begin, required int end})
      : super(
          <TweenSequenceItem<int>>[
            TweenSequenceItem(tween: ConstantTween(begin), weight: 1),
            TweenSequenceItem(
                tween: IntTween(begin: begin, end: end), weight: 2),
            TweenSequenceItem(tween: ConstantTween(end), weight: 1),
            TweenSequenceItem(
                tween: IntTween(begin: end, end: begin), weight: 2),
          ],
        );
}

import 'package:flutter/material.dart';


class WyDeviceHomeMobile extends StatefulWidget {
  const WyDeviceHomeMobile({Key? key}) : super(key: key);

  @override
  State<WyDeviceHomeMobile> createState() => _WyDeviceHomeMobileState();

}

class _WyDeviceHomeMobileState extends State<WyDeviceHomeMobile> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Device Home'),
      ),
      body: Center(
        child: Text('Welcome to Device Home!'),
      ),
    );
  }
}
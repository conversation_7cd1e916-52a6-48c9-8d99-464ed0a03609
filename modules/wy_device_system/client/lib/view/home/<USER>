
import 'dart:async';

import 'package:client/view/player/player_page.dart';
import 'package:flutter/material.dart';
import 'package:fl_chart/fl_chart.dart';
import 'package:tolyui_rx_layout/tolyui_rx_layout.dart';
import '../../bloc/measurement_bloc.dart';
import '../../bloc/targets_blocs.dart';
import '../../bloc/web_socket_bloc.dart';
import '../../bloc/wy_device_blocs.dart';
import '../../model/wy_device.dart';
import '../../repository/target_repository.dart';
import '../cloud_connection_settings/cloud_connection_settings.dart';
import '../components/measurement/measurement.dart';
import '../components/roi_selector/multi_camera_roi_selector.dart';
import '../components/roi_selector/roi_selector.dart'; // Use the correct CanvasROISelector
import 'package:tab_container/tab_container.dart';
import 'package:provider/provider.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:tolyui/basic/basic.dart';
import 'package:tolyui/tolyui.dart';

import '../components/targets/target_list.dart';
import '../components/wy_device_setting/wy_device_setting.dart';
import '../../services/measurement_websocket_service.dart';
import 'draggable_toggle_button.dart';
class WyDeviceHomePage extends StatefulWidget {
  final String initialWebSocketUrl;
  final String deviceId;
  final String ip;

  const WyDeviceHomePage({
    Key? key,
    required this.initialWebSocketUrl,
    required this.deviceId,
    required this.ip,
  }) : super(key: key);

  @override
  State<WyDeviceHomePage> createState() => WyDeviceHomeState();
}

class WyDeviceHomeState extends State<WyDeviceHomePage>
    with SingleTickerProviderStateMixin {
  late final TabController _tabController;
  bool _showROI = true;
  bool _showSwitchableSection = true;
  late WyDeviceStatus deviceStatus;
  late final TargetBloc _targetBloc;
  late final WyDeviceBloc _wyDeviceBloc;
  late final WebSocketBloc _webSocketBloc;
  late final MeasurementChartBloc _measurementChartBloc;
  MeasurementWebSocketService? _measurementWebSocketService;

  @override
  @override
  void initState() {
    super.initState();
    deviceStatus = WyDeviceStatus.idle;
    _tabController = TabController(vsync: this, length: 4);
    _wyDeviceBloc = WyDeviceBloc(
      deviceId: widget.deviceId,
      ipAddress: widget.ip,
    );
    TargetRepository repo = TargetRepositoryImpl(wyDeviceBloc:_wyDeviceBloc);
    _targetBloc = TargetBloc(repo);
    _webSocketBloc = WebSocketBloc(
      widget.initialWebSocketUrl,
      targetBloc: _targetBloc,
      deviceBloc: _wyDeviceBloc,
    );

    // 初始化MeasurementChartBloc
    _measurementChartBloc = MeasurementChartBloc(maxDataPoints: 1000);

    // 初始化服务连接WebSocketBloc和MeasurementChartBloc
      _measurementWebSocketService = MeasurementWebSocketService(
        webSocketBloc: _webSocketBloc,
        measurementChartBloc: _measurementChartBloc,
    );




    _webSocketBloc.add(ConnectWebSocket(widget.initialWebSocketUrl));

    _targetBloc.add(const TargetsLoading());
    _targetBloc.add(const RequestTargets());
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
  }

  @override
  void dispose() {
    _tabController.dispose();
    _targetBloc.close();
    _wyDeviceBloc.close();
    _webSocketBloc.close();
    _measurementChartBloc.close();
    _measurementWebSocketService?.dispose();

    super.dispose();
  }

// 在WyDeviceHomePage中修改整体布局，让显示区域更合理
  Widget buildHome(BuildContext context) {
    return MultiBlocListener(
        listeners: [
          BlocListener<WebSocketBloc, WebSocketState>(
            listener: (context, state) {
              if (state is WebSocketConnected) {
                $message.success(
                    closeable:true,
                    position: MessagePosition.top,
                    message: 'WebSocket连接成功'
                );
              } else if (state is WebSocketError) {
                $message.error(
                    closeable:true,
                    position: MessagePosition.top,
                    message: 'WebSocket错误: ${state.error}'
                );
              } else if (state is WebSocketDisconnected) {
                $message.warning(
                    closeable:true,
                    position: MessagePosition.top,
                    message: 'WebSocket已断开连接'
                );
              }
            },
          ),
          BlocListener<WyDeviceBloc, WyDeviceState>(
            listenWhen: (previous, current) => previous != current,
            listener: (context, state) {
              if (state.errorMessage != null) {
               $message.error(
                    closeable:true,
                    duration: const Duration(seconds: 6),
                    position: MessagePosition.bottom,
                    message: '${state.errorMessage}'
                );
                context.read<WyDeviceBloc>().add(WyDeviceBlocClearErrorMessage());
              }
            },
          ),

          BlocListener<TargetBloc, TargetState>(
            listenWhen: (previous, current) => previous != current,
            listener: (context, state) {
              if (state.errorMessage != null) {
                $message.error(
                    closeable:true,
                    duration: const Duration(seconds: 6),
                    position: MessagePosition.bottom,
                    message: '${state.errorMessage}'
                );
                context.read<TargetBloc>().add(TargetBlocClearErrorMessage());
              }
            },
          ),


        ],
        child: Scaffold(
          extendBody: true,
          appBar: _buildAppBar(),
          drawer: _buildDrawer(),
          body: SafeArea(
            child: Column(
              children: [
                // 可折叠的图像区域标题栏
                InkWell(
                  onTap: () {
                    setState(() {
                      _showSwitchableSection = !_showSwitchableSection;
                    });
                  },
                  child: Container(
                    padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                    decoration: BoxDecoration(
                      color: Theme.of(context).colorScheme.surfaceVariant.withOpacity(0.3),
                      border: Border(
                        bottom: BorderSide(
                          color: Theme.of(context).dividerColor,
                          width: 1,
                        ),
                      ),
                    ),
                    child: Row(
                      children: [
                        Icon(
                          _showSwitchableSection ? Icons.visibility : Icons.visibility_off,
                          size: 20,
                          color: Theme.of(context).colorScheme.primary,
                        ),
                        const SizedBox(width: 8),
                        Text(
                          _showSwitchableSection ? '隐藏' : '显示',
                          style: Theme.of(context).textTheme.titleSmall?.copyWith(
                            color: Theme.of(context).colorScheme.primary,
                          ),
                        ),
                        const Spacer(),
                        // 在这里添加切换按钮
                      GestureDetector(
                        onTap: () {
                          setState(() {
                            _showROI = !_showROI;
                          });
                        },
                        child: Container(
                          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                          decoration: BoxDecoration(
                            color: Theme.of(context).colorScheme.surfaceVariant,
                            borderRadius: BorderRadius.circular(20),
                            border: Border.all(
                              color: _showROI ? Colors.blue : Theme.of(context).colorScheme.primary,
                              width: 1.5,
                            ),
                            boxShadow: [
                              BoxShadow(
                                color: Colors.black.withOpacity(0.1),
                                spreadRadius: 1,
                                blurRadius: 2,
                                offset: const Offset(0, 1),
                              ),
                            ],
                          ),
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Icon(
                                _showROI ? Icons.crop : Icons.videocam,
                                size: 20,
                                color: _showROI ? Colors.blue : Theme.of(context).colorScheme.primary,
                              ),
                              const SizedBox(width: 8),
                              Text(
                                _showROI ? '图片' : '视频',
                                style: TextStyle(
                                  fontSize: 14,
                                  fontWeight: FontWeight.bold,
                                  color: _showROI ? Colors.blue : Theme.of(context).colorScheme.primary,
                                ),
                              ),
                              const SizedBox(width: 8),
                              Container(
                                padding: const EdgeInsets.all(4),
                                decoration: BoxDecoration(
                                  color: _showROI ? Colors.blue.withOpacity(0.2) : Theme.of(context).colorScheme.primary.withOpacity(0.2),
                                  shape: BoxShape.circle,
                                ),
                                child: Icon(
                                  Icons.swap_horiz,
                                  size: 16,
                                  color: _showROI ? Colors.blue : Theme.of(context).colorScheme.primary,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                        const SizedBox(width: 8),
                        AnimatedRotation(
                          turns: _showSwitchableSection ? 0.5 : 0,
                          duration: const Duration(milliseconds: 300),
                          child: const Icon(Icons.expand_more),
                        ),
                      ],
                    ),
                  ),
                ),

                // 可折叠的图像内容区域
                AnimatedContainer(
                  duration: const Duration(milliseconds: 300),
                  curve: Curves.easeInOut,
                  height: _showSwitchableSection ? 320 : 0, // 固定高度
                  child: AnimatedOpacity(
                    opacity: _showSwitchableSection ? 1.0 : 0.0,
                    duration: const Duration(milliseconds: 200),
                    child: _showSwitchableSection
                        ? _buildSwitchableSection(context)
                        : null,
                  ),
                ),

                // 主内容区
                Expanded(
                  child: _buildScrollableContent(context),
                ),
              ],
            ),
          ),
        )
    );
  }
  @override
  Widget build(BuildContext context) {

    return MultiProvider(
      providers: [
        BlocProvider<TargetBloc>.value(value: _targetBloc),
        BlocProvider<WyDeviceBloc>.value(value: _wyDeviceBloc),
        BlocProvider<WebSocketBloc>.value(value: _webSocketBloc),
        BlocProvider<MeasurementChartBloc>.value(value: _measurementChartBloc),
        Provider<MeasurementWebSocketService>.value(value: _measurementWebSocketService!),
      ],
      child: BlocBuilder<TargetBloc, TargetState>(
        builder: (context, state) {
          if (state.isLoading) {
            return const Center(child: CircularProgressIndicator());
          }
          return buildHome(context);
        },
      ),
    );
  }

  Widget _buildSwitchableSection(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;

    return Container(
      height: 320, // 固定合适的高度
      width: screenWidth, // 使用全屏宽度
      child: LayoutBuilder(
        builder: (context, constraints) {
          return Container(
            width: constraints.maxWidth,
            height: constraints.maxHeight,
            child: _showROI
                ? MultiCameraROISelector(
              compactMode: true,
              targetBloc: _targetBloc,
              wyDeviceBloc: _wyDeviceBloc,
              showTargetInfoCard: false,
              canvasWidth: constraints.maxWidth,
              canvasHeight: constraints.maxHeight,
              imagePath: 'assets/images/sample_image.jpg',
              onROIsChanged: (roiData) {
                // Handle ROI changes
              },
            )
                : VideoPlayer(videoUrl: "rtsp://${widget.ip}/video1"),
          );
        },
      ),
    );
  }

  AppBar _buildAppBar() {
    return AppBar(
      // Add leading back button
      leading: IconButton(
        icon: const Icon(Icons.arrow_back),
        onPressed: () {
          Navigator.of(context).pop(); // This will navigate back to the previous screen
        },
        tooltip: '返回上一级',
      ),
      title: const Text('视觉位移计客户端'),
      actions: [
        // 添加设备状态指示器
        BlocBuilder<WyDeviceBloc, WyDeviceState>(
          builder: (context, state) {
            final deviceStatus = state.wyDeviceStatus ?? WyDeviceStatus.idle;
            IconData statusIcon;
            Color statusColor;
            String tooltip;
            switch (deviceStatus) {
              case WyDeviceStatus.idle:
                statusIcon = Icons.pause_circle;
                statusColor = Colors.grey;
                tooltip = '空闲';
                break;
              case WyDeviceStatus.initializing:
                statusIcon = Icons.sync;
                statusColor = Colors.orange;
                tooltip = '初始化中';
                break;
              case WyDeviceStatus.testing:
                statusIcon = Icons.science;
                statusColor = Colors.purple;
                tooltip = '调试中';
                break;
              case WyDeviceStatus.measuring:
                statusIcon = Icons.monitor_heart;
                statusColor = Colors.green;
                tooltip = '测量中';
                break;
              case WyDeviceStatus.accuracyCalibration:
                statusIcon = Icons.build_circle_outlined;
                statusColor = Colors.yellow;
                tooltip = '标定中';
                break;
            }

            return Tooltip(
              message: tooltip,
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 8),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(statusIcon, color: statusColor),
                    const SizedBox(width: 4),
                    Text(
                      tooltip,
                      style: TextStyle(color: statusColor, fontSize: 12),
                    ),
                  ],
                ),
              ),
            );
          },
        ),
        // WebSocket状态指示器
        BlocBuilder<WebSocketBloc, WebSocketState>(
          builder: (context, state) {
            IconData icon;
            Color color;
            String tooltip;

            if ((state is WebSocketConnected)||(state is WebSocketMessageReceived)) {
              icon = Icons.cloud_done;
              color = Colors.green;
              tooltip = '已连接';
            } else if (state is WebSocketConnecting) {
              icon = Icons.cloud_sync;
              color = Colors.amber;
              tooltip = '连接中';
            } else if (state is WebSocketDisconnected || state is WebSocketError) {
              icon = Icons.cloud_off;
              color = Colors.red;
              tooltip = '未连接';
            } else  {
              icon = Icons.cloud;
              color = Colors.grey;
              tooltip = '未知';
            }

            return IconButton(
              icon: Icon(icon, color: color),
              tooltip: tooltip,
              onPressed: () {
                if (state is WebSocketConnected) {
                  context.read<WebSocketBloc>().add(DisconnectWebSocket());
                } else if (state is WebSocketDisconnected || state is WebSocketError) {
                  context.read<WebSocketBloc>().add(
                      ConnectWebSocket("ws://*************:9999/ws")
                  );
                }
              },
            );
          },
        ),
        // IconButton(
        //   icon: const Icon(Icons.science),
        //   tooltip: '发送测试数据',
        //   onPressed: () {
        //     _toggleAutoSendTestData();
        //   },
        // ),
        // IconButton(
        //   icon: const Icon(Icons.refresh),
        //   onPressed: () {
        //     setState(() {});
        //   },
        // ),
      ],
    );
  }
  Drawer _buildDrawer() {
    return Drawer(
      child: ListView(
        padding: EdgeInsets.zero,
        children: [
          const DrawerHeader(
            decoration: BoxDecoration(color: Colors.blue),
            child:
                Text('菜单', style: TextStyle(color: Colors.white, fontSize: 24)),
          ),
          ListTile(
            leading: const Icon(Icons.home),
            title: const Text('首页'),
            onTap: () {
              Navigator.pop(context); // Close drawer
              // Add navigation logic here
            },
          ),
        ],
      ),
    );
  }

  Widget _buildScrollableContent(BuildContext context) {
    return TabContainer(
      controller: _tabController,
      tabEdge: TabEdge.top,
      tabsStart: 0.1,
      tabsEnd: 0.9,
      tabMaxLength: 100,
      borderRadius: BorderRadius.circular(16),
      tabBorderRadius: BorderRadius.circular(8),
      childPadding: const EdgeInsets.all(16.0),
      selectedTextStyle: const TextStyle(
        color: Colors.blue,
        fontSize: 16.0,
        fontWeight: FontWeight.bold,
      ),
      unselectedTextStyle: const TextStyle(
        color: Colors.grey,
        fontSize: 14.0,
      ),
      colors: List.filled(4, Colors.white),
      tabs: [
        const Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(Icons.analytics_outlined, size: 20),
            SizedBox(width: 4),
            Flexible(child: Text('测量', overflow: TextOverflow.ellipsis)),
          ],
        ),
        const Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(Icons.settings_outlined, size: 20),
            SizedBox(width: 4),
            Flexible(child: Text('标靶', overflow: TextOverflow.ellipsis)),
          ],
        ),

        const Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(Icons.code, size: 20),
            SizedBox(width: 4),
            Flexible(child: Text('设置', overflow: TextOverflow.ellipsis)),
          ],
        ),
        const Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(Icons.cloud_sync, size: 20), // 云连接图标
            SizedBox(width: 4),
            Flexible(child: Text('云连接', overflow: TextOverflow.ellipsis)),
          ],
        ),

      ],
      children: [
        // SparklineDashboard(),
        Container(
          child:
          MeasurementDashboard(
            targets: _targetBloc.state.targets,
              measurementChartBloc: _measurementChartBloc
          ),
          // MeasurementVisualizer(
          //   targets: _targetBloc.state.targets,
          //   showEnvironmental: true,
          //   showIMU: true,
          //   measurementChartBloc: _measurementChartBloc, // 传入bloc
          //
          // ),
        ),
        Container(
          // padding: const EdgeInsets.symmetric(horizontal: 16.0),
          alignment: Alignment.center,
          child: ConstrainedBox(
            constraints: const BoxConstraints(maxWidth: 800),
            child: ROITargetList(
                repository: TargetRepositoryImpl(wyDeviceBloc: _wyDeviceBloc),
                roiBloc: _targetBloc,
                wyDeviceBloc: _wyDeviceBloc,
            ),
          ),
        ),
        Container(
          alignment: Alignment.center,
          child: SingleChildScrollView(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  DeviceSettingsPanel(wyDeviceBloc: _wyDeviceBloc),
                ],
              ),
            ),
          ),
        ),
        Container(
          alignment: Alignment.center,
          child: const CloudConnectionSettings(),
        ),

      ],
    );
  }

  Widget _buildCenteredRow({required Widget child, double? maxWidth}) {
    return Row$(
      gutter: 10.rx,
      justify: RxJustify.center,
      cells: [
        Cell(
          span: 24.rx,
          child: ConstrainedBox(
            constraints: BoxConstraints(maxWidth: maxWidth ?? double.infinity),
            child: child,
          ),
        ),
      ],
    );
  }
}

// EnvironmentCard
class EnvironmentCard extends StatelessWidget {
  const EnvironmentCard({super.key});

  @override
  Widget build(BuildContext context) {
    return ConstrainedBox(
      constraints: BoxConstraints(maxWidth: MediaQuery.of(context).size.width),
      child: Row$(
        gutter: 10.rx,
        justify: RxJustify.center,
        cells: [
          Cell(span: 8.rx, child: _TemperatureStatus()),
          Cell(span: 8.rx, child: _HumidityStatus()),
          Cell(span: 8.rx, child: _PressureStatus()),
          Cell(span: 8.rx, child: _VoltageStatus()),
          Cell(span: 8.rx, child: _SignalStatus()),
          Cell(span: 8.rx, child: _WorkStatus()),
        ],
      ),
    );
  }
}

// Environment Status Widgets
class _TemperatureStatus extends StatelessWidget {
  const _TemperatureStatus();

  @override
  Widget build(BuildContext context) {
    return const EnvironmentStatus(
      icon: Icons.thermostat,
      label: '温度',
      value: '22',
      suffix: '°C',
      color: Colors.redAccent,
    );
  }
}

class _HumidityStatus extends StatelessWidget {
  const _HumidityStatus();

  @override
  Widget build(BuildContext context) {
    return const EnvironmentStatus(
      icon: Icons.water_drop,
      label: '湿度',
      value: '60',
      suffix: '%',
      color: Colors.blueAccent,
    );
  }
}

class _PressureStatus extends StatelessWidget {
  const _PressureStatus();

  @override
  Widget build(BuildContext context) {
    return const EnvironmentStatus(
      icon: Icons.compress,
      label: '压强',
      value: '1013',
      suffix: 'hPa',
      color: Colors.greenAccent,
    );
  }
}

class _VoltageStatus extends StatelessWidget {
  const _VoltageStatus();

  @override
  Widget build(BuildContext context) {
    return const EnvironmentStatus(
      icon: Icons.electric_bolt,
      label: '电压',
      value: '12',
      suffix: 'V',
      color: Colors.orangeAccent,
    );
  }
}

class _SignalStatus extends StatelessWidget {
  const _SignalStatus();

  @override
  Widget build(BuildContext context) {
    return const EnvironmentStatus(
      icon: Icons.signal_cellular_alt,
      label: '4G信号',
      value: '-72',
      suffix: 'dBm',
      color: Colors.blueAccent,
    );
  }
}

class _WorkStatus extends StatelessWidget {
  const _WorkStatus();

  @override
  Widget build(BuildContext context) {
    return const EnvironmentStatus(
      icon: Icons.run_circle,
      label: '工作状态',
      value: '测量中',
      suffix: '',
      color: Colors.blueAccent,
    );
  }
}

// EnvironmentStatus Widget
class EnvironmentStatus extends StatelessWidget {
  final IconData icon;
  final String label;
  final String value;
  final String suffix;
  final Color color;
  final bool isHorizontal;
  final bool showLabel;

  const EnvironmentStatus({
    super.key,
    required this.icon,
    required this.label,
    required this.value,
    required this.suffix,
    required this.color,
    this.isHorizontal = false,
    this.showLabel = true,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 3.0),
      child: isHorizontal
          ? Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(icon, color: color, size: 28),
                const SizedBox(width: 2.0),
                Text(
                  showLabel ? '$label: $value $suffix' : '$value $suffix',
                  style: const TextStyle(
                      fontSize: 16, fontWeight: FontWeight.bold),
                ),
              ],
            )
          : Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(icon, color: color, size: 28),
                const SizedBox(height: 1.0),
                Text(
                  showLabel ? '$label\n$value $suffix' : '$value $suffix',
                  style: const TextStyle(
                      fontSize: 16, fontWeight: FontWeight.bold),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
    );
  }
}

// MeasurementCard
class MeasurementCard extends StatelessWidget {
  const MeasurementCard({super.key});

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: SizedBox(
          height: 200,
          child: LineChart(
            LineChartData(
              gridData: FlGridData(show: true),
              titlesData: FlTitlesData(show: true),
              borderData: FlBorderData(show: false),
              lineBarsData: [
                LineChartBarData(
                  spots: const [
                    FlSpot(0, 1),
                    FlSpot(1, 1.5),
                    FlSpot(2, 1.4),
                    FlSpot(3, 3.4),
                    FlSpot(4, 2),
                    FlSpot(5, 2.2),
                    FlSpot(6, 1.8),
                  ],
                  color: Colors.blue,
                  isCurved: false,
                  barWidth: 2,
                  isStrokeCapRound: true,
                  dotData: FlDotData(show: false),
                  belowBarData: BarAreaData(show: false),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

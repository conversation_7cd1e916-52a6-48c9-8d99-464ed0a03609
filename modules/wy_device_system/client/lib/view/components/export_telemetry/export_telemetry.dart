import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';
import 'dart:io';
import 'package:path_provider/path_provider.dart';
import 'package:csv/csv.dart';

class TelemetryTable extends StatefulWidget {
  @override
  _TelemetryTableState createState() => _TelemetryTableState();
}

class _TelemetryTableState extends State<TelemetryTable> {
  List<Map<String, dynamic>> _data = [];
  bool _isLoading = false;
  int _currentPage = 1;
  final int _pageSize = 50; // 每页显示的数据量
  int _totalCount = 0; // 总数据量
  DateTime _startTime = DateTime.now().subtract(Duration(days: 1));
  DateTime _endTime = DateTime.now();

  // 输入控件控制器
  final TextEditingController _apiUrlController =
  TextEditingController(text: 'http://192.168.1.230:9999/api/telemetry');
  final TextEditingController _entityIdController =
  TextEditingController(text: 'target1');
  final TextEditingController _keyController =
  TextEditingController(text: 'sigmaX');

  // 获取数据
  Future<void> _fetchData() async {
    setState(() {
      _isLoading = true;
    });

    // 计算时间偏移量
    final duration = _endTime.difference(_startTime);
    final pageDuration = Duration(milliseconds: duration.inMilliseconds ~/ (_totalCount == 0 ? 1 : _totalCount) * _pageSize);
    final pageStartTime = _startTime.add(pageDuration * (_currentPage - 1));
    final pageEndTime = _currentPage * _pageSize >= _totalCount
        ? _endTime
        : pageStartTime.add(pageDuration);

    // 构造完整的URL
    final url = Uri.parse(
        '${_apiUrlController.text}?entity_id=${_entityIdController.text}&key=${_keyController.text}&start_ts=${pageStartTime.millisecondsSinceEpoch}&end_ts=${pageEndTime.millisecondsSinceEpoch}');

    try {
      final response = await http.get(url);
      if (response.statusCode == 200) {
        final jsonData = json.decode(response.body);
        final List<dynamic> values = jsonData['data']['values'];
        setState(() {
          _data = values.map((item) => item as Map<String, dynamic>).toList();
          _totalCount = jsonData['data']['totalCount']; // 获取总数据量
          _isLoading = false;
        });
      } else {
        throw Exception('Failed to load data');
      }
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      print('Error fetching data: $e');
    }
  }

  // 导出完整数据到CSV
  Future<void> _exportToCSV() async {
    setState(() {
      _isLoading = true;
    });

    // 获取完整数据
    final url = Uri.parse(
        '${_apiUrlController.text}?entity_id=${_entityIdController.text}&key=${_keyController.text}&start_ts=${_startTime.millisecondsSinceEpoch}&end_ts=${_endTime.millisecondsSinceEpoch}');
    try {
      final response = await http.get(url);
      if (response.statusCode == 200) {
        final jsonData = json.decode(response.body);
        final List<dynamic> values = jsonData['data']['values'];
        final fullData = values.map((item) => item as Map<String, dynamic>).toList();

        // 将数据写入CSV
        List<List<dynamic>> rows = [];
        if (fullData.isNotEmpty) {
          // 添加表头
          rows.add(['时间', _keyController.text]);
          // 添加数据
          for (var item in fullData) {
            rows.add([
              _formatTimestamp(item['ts']), // 转换时间戳
              item['value'],
            ]);
          }
        }
        String csv = const ListToCsvConverter().convert(rows);
        final directory = await getApplicationDocumentsDirectory();
        final path = '${directory.path}/${_entityIdController.text}_${_keyController.text}_telemetry_data.csv';
        final file = File(path);
        await file.writeAsString(csv);

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('数据已导出到 $path')),
        );
      } else {
        throw Exception('Failed to load data');
      }
    } catch (e) {
      print('Error exporting data: $e');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  // 将时间戳转换为时间格式
  String _formatTimestamp(int timestamp) {
    return DateTime.fromMillisecondsSinceEpoch(timestamp).toLocal().toString();
  }

  // 选择开始时间
  Future<void> _selectStartTime(BuildContext context) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _startTime,
      firstDate: DateTime(2000),
      lastDate: DateTime(2101),
    );
    if (picked != null && picked != _startTime) {
      setState(() {
        _startTime = picked;
      });
    }
  }

  // 选择结束时间
  Future<void> _selectEndTime(BuildContext context) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _endTime,
      firstDate: DateTime(2000),
      lastDate: DateTime(2101),
    );
    if (picked != null && picked != _endTime) {
      setState(() {
        _endTime = picked;
      });
    }
  }

  // 分页控件
  Widget _buildPaginationControls() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        IconButton(
          icon: Icon(Icons.arrow_back),
          onPressed: _currentPage > 1
              ? () {
            setState(() {
              _currentPage--;
            });
            _fetchData();
          }
              : null,
        ),
        Text('第 $_currentPage 页'),
        IconButton(
          icon: Icon(Icons.arrow_forward),
          onPressed: _currentPage < (_totalCount / _pageSize).ceil()
              ? () {
            setState(() {
              _currentPage++;
            });
            _fetchData();
          }
              : null,
        ),
      ],
    );
  }

  // 构建表格行
  Widget _buildTableRow(Map<String, dynamic> item) {
    return Container(
      decoration: BoxDecoration(
        border: Border(bottom: BorderSide(color: Colors.grey[300]!)),
      ),
      child: Row(
        children: [
          Expanded(
            flex: 2,
            child: Padding(
              padding: EdgeInsets.all(8),
              child: Text(_formatTimestamp(item['ts'])),
            ),
          ),
          Expanded(
            flex: 1,
            child: Padding(
              padding: EdgeInsets.all(8),
              child: Text(item['value'].toString()),
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('导出测量数据'),
        actions: [
          IconButton(
            icon: Icon(Icons.download),
            onPressed: _exportToCSV,
          ),
        ],
      ),
      body: Column(
        children: [
          Padding(
            padding: const EdgeInsets.all(8.0),
            child: Column(
              children: [
                TextField(
                  controller: _apiUrlController,
                  decoration: InputDecoration(
                    labelText: 'API URL',
                  ),
                ),
                SizedBox(height: 8),
                TextField(
                  controller: _entityIdController,
                  decoration: InputDecoration(
                    labelText: '标靶',
                  ),
                ),
                SizedBox(height: 8),
                TextField(
                  controller: _keyController,
                  decoration: InputDecoration(
                    labelText: '指标',
                  ),
                ),
                SizedBox(height: 8),
                Row(
                  children: [
                    Expanded(
                      child: ElevatedButton(
                        onPressed: () => _selectStartTime(context),
                        child: Text('选择开始时间: ${_startTime.toLocal()}'),
                      ),
                    ),
                    SizedBox(width: 8),
                    Expanded(
                      child: ElevatedButton(
                        onPressed: () => _selectEndTime(context),
                        child: Text('选择结束时间: ${_endTime.toLocal()}'),
                      ),
                    ),
                  ],
                ),
                SizedBox(height: 8),
                ElevatedButton(
                  onPressed: _fetchData,
                  child: Text('查询'),
                ),
              ],
            ),
          ),
          Expanded(
            child: Column(
              children: [
                _isLoading
                    ? Center(child: CircularProgressIndicator())
                    : _data.isEmpty
                    ? Center(child: Text('暂无数据'))
                    : Expanded(
                  child: ListView.builder(
                    itemCount: _data.length,
                    itemBuilder: (context, index) {
                      return _buildTableRow(_data[index]);
                    },
                  ),
                ),
                _buildPaginationControls(),
                Padding(
                  padding: const EdgeInsets.all(8.0),
                  child: Text('总数据量: $_totalCount'),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
import 'package:flutter/material.dart';

import '../../../model/target.dart';
import '../../../services/measurement_service.dart';
import '../charts/chart.dart';
import '../charts/model.dart';

class DataQueryPanel extends StatefulWidget {
  final List<Target> targets;
  final String deviceId;

  const DataQueryPanel({
    Key? key,
    required this.targets,
    required this.deviceId,
  }) : super(key: key);

  @override
  State<DataQueryPanel> createState() => _DataQueryPanelState();
}

class _DataQueryPanelState extends State<DataQueryPanel> with SingleTickerProviderStateMixin {
  final MeasurementDataService _dataService = MeasurementDataService();
  late TabController _tabController;
  late ScrollController _scrollController;

  // 查询参数
  DateTimeRange _selectedTimeRange = DateTimeRange(
    start: DateTime.now().subtract(const Duration(hours: 24)),
    end: DateTime.now(),
  );
  List<bool> _selectedTargets = [];
  bool _liveMode = true;
  String _quickTimeOption = '24小时';

  // 图表数据
  Map<String, List<MeasurementData>> _chartData = {};
  bool _isLoading = false;
  String _errorMessage = '';

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _scrollController = ScrollController();
    _selectedTargets = List.generate(widget.targets.length, (index) => true);

    // 初始化数据服务
    _dataService.init(widget.deviceId);

    // 默认加载最近24小时的数据
    _loadInitialData();

    // 监听实时数据流
    _setupLiveDataListener();
  }

  void _setupLiveDataListener() {
    _dataService.dataStream.listen((data) {
      if (_liveMode) {
        setState(() {
          // 更新对应标靶的实时数据
          final targetId = widget.targets.first.targetId; // 这里需要处理多标靶的情况
          _chartData['${targetId}_displacement'] = data;
        });
      }
    });
  }

  Future<void> _loadInitialData() async {
    await _queryData();
  }

  Future<void> _queryData() async {
    if (widget.targets.isEmpty) return;

    setState(() {
      _isLoading = true;
      _errorMessage = '';
    });

    try {
      final newChartData = <String, List<MeasurementData>>{};

      // 为每个选中的标靶加载数据
      for (int i = 0; i < widget.targets.length; i++) {
        if (_selectedTargets[i]) {
          final target = widget.targets[i];

          // 获取位移总量数据
          final displacementData = await _dataService.queryHistoricalData(
            deviceId: widget.deviceId,
            targetId: target.targetId,
            dataType: 'displacement',
            startTime: _selectedTimeRange.start,
            endTime: _selectedTimeRange.end,
          );

          newChartData['${target.targetId}_displacement'] = displacementData;

          // 获取X轴位移数据
          final xData = await _dataService.queryHistoricalData(
            deviceId: widget.deviceId,
            targetId: target.targetId,
            dataType: 'sigmaX',
            startTime: _selectedTimeRange.start,
            endTime: _selectedTimeRange.end,
          );

          newChartData['${target.targetId}_sigmaX'] = xData;

          // 获取Y轴位移数据
          final yData = await _dataService.queryHistoricalData(
            deviceId: widget.deviceId,
            targetId: target.targetId,
            dataType: 'sigmaY',
            startTime: _selectedTimeRange.start,
            endTime: _selectedTimeRange.end,
          );

          newChartData['${target.targetId}_sigmaY'] = yData;
        }
      }

      // 加载环境数据
      final temperatureData = await _dataService.queryHistoricalData(
        deviceId: widget.deviceId,
        targetId: '',  // 环境数据不需要标靶ID
        dataType: 'temperature',
        startTime: _selectedTimeRange.start,
        endTime: _selectedTimeRange.end,
      );

      newChartData['temperature'] = temperatureData;

      // 其他环境数据...

      setState(() {
        _chartData = newChartData;
        _isLoading = false;
      });

    } catch (e) {
      setState(() {
        _isLoading = false;
        _errorMessage = '数据查询失败: $e';
      });
    }
  }

  @override
  void dispose() {
    _tabController.dispose();
    _scrollController.dispose();
    _dataService.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        _buildQueryControls(),
        Expanded(
          child: _isLoading
              ? const Center(child: CircularProgressIndicator())
              : _errorMessage.isNotEmpty
              ? Center(child: Text(_errorMessage, style: TextStyle(color: Colors.red)))
              : _buildTabView(),
        ),
      ],
    );
  }

  Widget _buildQueryControls() {
    return Card(
      margin: const EdgeInsets.all(8),
      child: Padding(
        padding: const EdgeInsets.all(12),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Expanded(
                  child: _buildTimeRangeSelector(),
                ),
                const SizedBox(width: 8),
                ElevatedButton.icon(
                  icon: const Icon(Icons.search),
                  label: const Text('查询'),
                  onPressed: _queryData,
                ),
                const SizedBox(width: 8),
                Switch(
                  value: _liveMode,
                  onChanged: (value) {
                    setState(() {
                      _liveMode = value;
                    });
                  },
                ),
                Text(_liveMode ? '实时' : '历史'),
              ],
            ),
            const SizedBox(height: 8),
            _buildTargetSelector(),
          ],
        ),
      ),
    );
  }

  Widget _buildTimeRangeSelector() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text('时间范围', style: TextStyle(fontWeight: FontWeight.bold)),
        const SizedBox(height: 4),
        Row(
          children: [
            _buildQuickTimeButton('1小时', const Duration(hours: 1)),
            _buildQuickTimeButton('6小时', const Duration(hours: 6)),
            _buildQuickTimeButton('24小时', const Duration(hours: 24)),
            _buildQuickTimeButton('7天', const Duration(days: 7)),
            _buildQuickTimeButton('30天', const Duration(days: 30)),
            const SizedBox(width: 8),
            Expanded(
              child: OutlinedButton(
                onPressed: _showCustomDateRangePicker,
                child: Text(_selectedTimeRange.start.toString().substring(0, 16) +
                    ' 至 ' +
                    _selectedTimeRange.end.toString().substring(0, 16)),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildQuickTimeButton(String label, Duration duration) {
    final isSelected = _quickTimeOption == label;

    return Padding(
      padding: const EdgeInsets.only(right: 8),
      child: ChoiceChip(
        label: Text(label),
        selected: isSelected,
        onSelected: (selected) {
          if (selected) {
            setState(() {
              _quickTimeOption = label;
              _selectedTimeRange = DateTimeRange(
                end: DateTime.now(),
                start: DateTime.now().subtract(duration),
              );
            });
          }
        },
      ),
    );
  }

  Future<void> _showCustomDateRangePicker() async {
    final picked = await showDateRangePicker(
      context: context,
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
      initialDateRange: _selectedTimeRange,
    );

    if (picked != null) {
      setState(() {
        _selectedTimeRange = picked;
        _quickTimeOption = '自定义';
      });
    }
  }

  Widget _buildTargetSelector() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            const Text('标靶选择', style: TextStyle(fontWeight: FontWeight.bold)),
            const Spacer(),
            TextButton(
              onPressed: () {
                setState(() {
                  _selectedTargets = List.generate(widget.targets.length, (index) => true);
                });
              },
              child: const Text('全选'),
            ),
            TextButton(
              onPressed: () {
                setState(() {
                  _selectedTargets = List.generate(widget.targets.length, (index) => false);
                });
              },
              child: const Text('取消全选'),
            ),
          ],
        ),
        const SizedBox(height: 4),
        Wrap(
          spacing: 8,
          runSpacing: 4,
          children: List.generate(widget.targets.length, (index) {
            return FilterChip(
              label: Text(widget.targets[index].name),
              selected: _selectedTargets[index],
              onSelected: (selected) {
                setState(() {
                  _selectedTargets[index] = selected;
                });
              },
            );
          }),
        ),
      ],
    );
  }

  Widget _buildTabView() {
    return Column(
      children: [
        TabBar(
          controller: _tabController,
          tabs: const [
            Tab(text: '位移总量'),
            Tab(text: 'XY分量'),
            Tab(text: '环境数据'),
          ],
        ),
        Expanded(
          child: TabBarView(
            controller: _tabController,
            children: [
              _buildDisplacementTab(),
              _buildXYComponentsTab(),
              _buildEnvironmentalTab(),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildDisplacementTab() {
    if (widget.targets.isEmpty) {
      return const Center(child: Text('没有可用的标靶'));
    }

    // 创建位移图表系列
    final series = <MeasurementSeries>[];

    for (int i = 0; i < widget.targets.length; i++) {
      if (_selectedTargets[i]) {
        final target = widget.targets[i];
        final key = '${target.targetId}_displacement';

        if (_chartData.containsKey(key) && _chartData[key]!.isNotEmpty) {
          series.add(
            MeasurementSeries(
              id: key,
              name: target.name,
              data: _chartData[key]!,
              color: Colors.primaries[i % Colors.primaries.length],
            ),
          );
        }
      }
    }

    if (series.isEmpty) {
      return const Center(child: Text('没有可用的位移数据'));
    }

    return Padding(
      padding: const EdgeInsets.all(8.0),
      child: EnhancedMeasurementChart(
        series: series,
        title: '位移总量',
        timeRange: _selectedTimeRange,
        showLegend: true,
        allowZoom: true,
      ),
    );
  }

  Widget _buildXYComponentsTab() {
    if (widget.targets.isEmpty) {
      return const Center(child: Text('没有可用的标靶'));
    }

    // 为每个选中的标靶创建一个图表
    return ListView.builder(
      controller: _scrollController,
      itemCount: widget.targets.length,
      itemBuilder: (context, index) {
        final target = widget.targets[index];

        if (!_selectedTargets[index]) {
          return const SizedBox.shrink();
        }

        final xKey = '${target.targetId}_sigmaX';
        final yKey = '${target.targetId}_sigmaY';

        if (!_chartData.containsKey(xKey) || !_chartData.containsKey(yKey)) {
          return const SizedBox.shrink();
        }

        final series = [
          MeasurementSeries(
            id: xKey,
            name: 'X轴位移',
            data: _chartData[xKey]!,
            color: Colors.red,
          ),
          MeasurementSeries(
            id: yKey,
            name: 'Y轴位移',
            data: _chartData[yKey]!,
            color: Colors.green,
          ),
        ];

        return Padding(
          padding: const EdgeInsets.all(8.0),
          child: EnhancedMeasurementChart(
            series: series,
            title: '${target.name} - XY分量',
            timeRange: _selectedTimeRange,
            showLegend: true,
            allowZoom: true,
            maxHeight: 250,
          ),
        );
      },
    );
  }

  Widget _buildEnvironmentalTab() {
    final environmentalKeys = ['temperature', 'humidity', 'pressure'];
    final environmentalNames = ['温度 (°C)', '湿度 (%)', '气压 (hPa)'];
    final environmentalColors = [Colors.red, Colors.blue, Colors.green];

    final series = <MeasurementSeries>[];

    for (int i = 0; i < environmentalKeys.length; i++) {
      final key = environmentalKeys[i];

      if (_chartData.containsKey(key) && _chartData[key]!.isNotEmpty) {
        series.add(
          MeasurementSeries(
            id: key,
            name: environmentalNames[i],
            data: _chartData[key]!,
            color: environmentalColors[i],
          ),
        );
      }
    }

    if (series.isEmpty) {
      return const Center(child: Text('没有可用的环境数据'));
    }

    return Padding(
      padding: const EdgeInsets.all(8.0),
      child: EnhancedMeasurementChart(
        series: series,
        title: '环境数据',
        timeRange: _selectedTimeRange,
        showLegend: true,
        allowZoom: true,
      ),
    );
  }
}


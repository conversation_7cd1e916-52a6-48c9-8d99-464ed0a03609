
import 'package:fl_chart/fl_chart.dart';
import 'package:flutter/material.dart';

class MultiLineChart extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return LineChart(
      LineChartData(
        gridData: FlGridData(show: true),
        titlesData: FlTitlesData(
          leftTitles: AxisTitles(
            sideTitles: SideTitles(
              showTitles: true,
              reservedSize: 40,
              interval: 10,
              getTitlesWidget: (value, meta) => Text(
                '${value.toInt()}°C',
                style: TextStyle(color: Colors.blue, fontSize: 10),
              ),
            ),
          ),
          rightTitles: AxisTitles(
            sideTitles: SideTitles(
              showTitles: true,
              reservedSize: 40,
              interval: 2,
              getTitlesWidget: (value, meta) => Text(
                '${value.toInt()}mm',
                style: TextStyle(color: Colors.orange, fontSize: 10),
              ),
            ),
          ),
          bottomTitles: AxisTitles(
            sideTitles: SideTitles(
              showTitles: true,
              interval: 1,
              getTitlesWidget: (value, meta) => Text(
                '${value.toInt()}s',
                style: TextStyle(color: Colors.white, fontSize: 10),
              ),
            ),
          ),
        ),
        lineBarsData: [
          // 温度曲线
          LineChartBarData(
            spots: [
              FlSpot(0, 20),
              FlSpot(1, 25),
              FlSpot(2, 30),
              FlSpot(3, 35),
            ],
            isCurved: true,
            barWidth: 4,
          ),
          // 位移曲线
          LineChartBarData(
            spots: [
              FlSpot(0, 1),
              FlSpot(1, 1.5),
              FlSpot(2, 2),
              FlSpot(3, 2.5),
            ],
            isCurved: true,
            barWidth: 4,
          ),
        ],
      ),
    );
  }
}

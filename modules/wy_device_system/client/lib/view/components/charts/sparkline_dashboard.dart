import 'dart:async';
import 'package:client/view/components/charts/sparkline_chart.dart';
import 'package:flutter/material.dart';

import '../../../model/data_point.dart';
import '../../../model/data_source.dart';
import 'fl_sparkline_chart.dart';

class SparklineDashboard extends StatefulWidget {
  const SparklineDashboard({super.key});

  @override
  State<SparklineDashboard> createState() => _SparklineDashboardState();
}

class _SparklineDashboardState extends State<SparklineDashboard> {
  late DataSource cpuData;
  late DataSource diskData;
  late DataSource memoryData;
  late DataSource ethernetData;

  @override
  void initState() {
    super.initState();
    cpuData = DataSource();
    diskData = DataSource();
    memoryData = DataSource();
    ethernetData = DataSource();

    // 生成初始数据
    cpuData.generateData();
    diskData.generateData();
    memoryData.generateData();
    ethernetData.generateData();

    // 模拟实时更新，每秒刷新一次
    Timer.periodic(const Duration(seconds: 1), (timer) {
      setState(() {
        cpuData.updateData();
        diskData.updateData();
        memoryData.updateData();
        ethernetData.updateData();
      });
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Sparkline Charts')),
      body: Padding(
        padding: const EdgeInsets.all(8.0),
        child: GridView.count(
          crossAxisCount: 2, // 2列布局
          childAspectRatio: 1.2, // 调整卡片宽高比
          crossAxisSpacing: 8.0, // 水平间距
          mainAxisSpacing: 8.0, // 垂直间距
          children: [
            _buildSparklineCard('X', cpuData.data, Colors.blue, Colors.blue),
            _buildSparklineCard('Y', diskData.data, Colors.purple, Colors.purple),
            _buildSparklineCard('温度', memoryData.data, Colors.green, Colors.green),
            _buildSparklineCard('湿度', ethernetData.data, Colors.brown, Colors.brown),
            _buildSparklineCard('压强', ethernetData.data, Colors.brown, Colors.brown),
            _buildSparklineCard('4G', ethernetData.data, Colors.brown, Colors.brown),
          ],
        ),
      ),
    );
  }
  Widget _buildSparklineCard(String title, List<DataPoint> data, Color lineColor, Color fillColor) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(8.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              title,
              style: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            Expanded(
              child: Container(
                width: double.infinity,
                padding: const EdgeInsets.only(bottom: 4.0),
                child: SparklineChart(
                  data: data,
                  lineColor: lineColor,
                  fillColor: fillColor,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }}
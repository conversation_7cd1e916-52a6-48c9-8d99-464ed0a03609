import 'dart:math';

import 'package:flutter/material.dart';
import 'package:multi_split_view/multi_split_view.dart';

import '../../../bloc/measurement_bloc.dart';
import '../../../bloc/targets_blocs.dart';
import '../../../bloc/wy_device_blocs.dart';
import '../../../model/target.dart';
import 'package:intl/intl.dart';
import 'package:fl_chart/fl_chart.dart';
import 'package:dropdown_search/dropdown_search.dart';
import 'package:progressive_time_picker/progressive_time_picker.dart';
import '../data_query_panel/data_query_panel.dart';
import '../targets/target_selector.dart';
import 'chart.dart';
import 'model.dart';
import 'package:provider/provider.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
class MeasurementVisualizer extends StatefulWidget {
  final List<Target> targets;
  final bool showEnvironmental;
  final bool showIMU;
  final MeasurementChartBloc? measurementChartBloc;

  const MeasurementVisualizer({
    Key? key,
    required this.targets,
    this.showEnvironmental = true,
    this.showIMU = false,
    this.measurementChartBloc,

  }) : super(key: key);

  @override
  State<MeasurementVisualizer> createState() =>
      _MultiTargetDisplacementVisualizerState();
}

class _MultiTargetDisplacementVisualizerState
    extends State<MeasurementVisualizer> {
  String? selectedTargetId;
  DateTimeRange timeRange = DateTimeRange(
    start: DateTime.now().subtract(const Duration(hours: 24)),
    end: DateTime.now(),
  );
  String viewMode = 'overview'; // 'overview', 'detailed'
  String _activeView = 'chart'; // 'chart' 或 'query' - 新增

  TextEditingController searchController = TextEditingController();
  String searchQuery = '';
  bool isSearchVisible = false;

  @override
  void initState() {
    super.initState();
    if (widget.targets.isNotEmpty) {
      selectedTargetId = widget.targets.first.targetId;
    }

    _fetchHistoricalData();

  }

  void _fetchHistoricalData() {
    if (widget.measurementChartBloc != null) {
      String deviceId = context.read<WyDeviceBloc>().state.deviceId;
      final types = ['x', 'y', 'z', 'displacement',
        'temperature', 'humidity', 'pressure',
        'accel_x', 'accel_y', 'accel_z','sigmaX','sigmaY'];
      widget.measurementChartBloc!.add(FetchHistoricalData(
        deviceId: deviceId,
        start: timeRange.start,
        end: timeRange.end,
        types: types,
      ));
    }
  }

  @override
  void dispose() {
    searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (widget.measurementChartBloc != null) {
      debugPrint('MeasurementChartPanel: measurementChartBloc is not null');
      return BlocBuilder<MeasurementChartBloc, MeasurementChartState>(
        bloc: widget.measurementChartBloc,
        builder: (context, state) {
          return _buildContent();
        },
      );
    } else {
      debugPrint('MeasurementChartPanel: measurementChartBloc is null');
      return _buildContent();
    }
  }

  Widget _buildContent() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 控制栏（包含查询/图表切换）
        _buildControlBar(),

        // 根据活动视图选择显示内容
        Expanded(
          child: _activeView == 'chart'
              ? _buildChartView()
              : _buildQueryView(),
        ),
      ],
    );
  }
  Widget _buildControlBar() {
    return Row(
      children: [
        // 标靶选择器
        if (widget.targets.isNotEmpty)
          Expanded(child: _buildCompactControls()),

        // 查询/图表切换按钮
        IconButton(
          icon: Icon(_activeView == 'chart' ? Icons.search : Icons.bar_chart),
          tooltip: _activeView == 'chart' ? '切换到查询模式' : '切换到图表模式',
          onPressed: () {
            setState(() {
              _activeView = _activeView == 'chart' ? 'query' : 'chart';
            });
          },
        ),
      ],
    );
  }


  Widget _buildChartView() {
    // 即使没有标靶也显示环境数据
    return LayoutBuilder(
      builder: (context, constraints) {
        final isNarrowLayout = constraints.maxWidth < 600;

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: 2),
            Expanded(
              child: widget.targets.isEmpty
                  ? _buildNoTargetsButEnvironmentView()
                  : isNarrowLayout
                  ? _buildMobileLayout()
                  : _buildDesktopLayout(),
            ),
          ],
        );
      },
    );
  }

  Widget _buildQueryView() {
    return DataQueryPanel(
      targets: widget.targets,
      deviceId:  "wy_device",
    );
  }
  Widget _buildNoTargetsButEnvironmentView() {
    return SingleChildScrollView(
      child: Column(
        children: [
          // 顶部提示信息
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      Icons.track_changes_outlined,
                      size: 48,
                      color: Colors.grey,
                    ),
                    SizedBox(height: 12),
                    Text(
                      '没有可用的标靶数据',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        color: Colors.grey[700],
                      ),
                    ),
                    SizedBox(height: 8),
                    Text(
                      '当前正在显示环境数据，请先添加标靶以查看位移数据',
                      style: TextStyle(
                        color: Colors.grey[600],
                      ),
                      textAlign: TextAlign.center,
                    ),
                    SizedBox(height: 16),
                    ElevatedButton.icon(
                      icon: Icon(Icons.refresh),
                      label: Text('刷新数据'),
                      onPressed: () {
                        setState(() {
                          // 重新获取数据
                          _fetchHistoricalData();
                        });
                      },
                    ),
                  ],
                ),
              ),
            ),
          ),

          // 环境状态卡片
          if (widget.showEnvironmental) ...[
            const SizedBox(height: 8),
            _buildEnvOverview(),
          ],

        ],
      ),
    );
  }

// 新增方法：提供IMU数据概览
  Widget _buildIMUOverview() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(12.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'IMU传感器数据',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 12),
            LayoutBuilder(
              builder: (context, constraints) {
                // 检查是否需要使用垂直布局
                final isNarrow = constraints.maxWidth < 450;

                if (isNarrow) {
                  // 窄屏幕的垂直布局
                  return Column(
                    children: [
                      _buildSparklineCard('加速度X', _getDeviceIMUData('accel_x'), Colors.red.shade300),
                      const SizedBox(height: 8),
                      _buildSparklineCard('加速度Y', _getDeviceIMUData('accel_y'), Colors.green.shade300),
                      const SizedBox(height: 8),
                      _buildSparklineCard('加速度Z', _getDeviceIMUData('accel_z'), Colors.blue.shade300),
                    ],
                  );
                } else {
                  // 宽屏幕的水平布局
                  return Row(
                    children: [
                      Expanded(
                          child: _buildSparklineCard(
                              '加速度X', _getDeviceIMUData('accel_x'), Colors.red.shade300)
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                          child: _buildSparklineCard(
                              '加速度Y', _getDeviceIMUData('accel_y'), Colors.green.shade300)
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                          child: _buildSparklineCard(
                              '加速度Z', _getDeviceIMUData('accel_z'), Colors.blue.shade300)
                      ),
                    ],
                  );
                }
              },
            ),
          ],
        ),
      ),
    );
  }

// 为设备级别的IMU数据添加一个获取方法
  List<MeasurementData> _getDeviceIMUData(String type) {
    if (widget.measurementChartBloc != null) {
      final data = widget.measurementChartBloc!.state.dataMap[type] ?? [];

      if (data.isNotEmpty) {
        return data.map((item) => item.toUiModel()).toList();
      }
    }

    // 如果没有bloc或没有数据，返回模拟数据
    return List.generate(100, (i) {
      final time = timeRange.start.add(Duration(
        minutes: ((timeRange.duration.inMinutes / 100) * i).round(),
      ));
      double value;
      switch (type) {
        case 'accel_x':
          value = sin(i * 0.2) * 0.8;
          break;
        case 'accel_y':
          value = cos(i * 0.2) * 0.6;
          break;
        default: // accel_z
          value = 9.8 + sin(i * 0.1) * 0.2;
      }
      return MeasurementData(time, value);
    });
  }

  Widget _buildCompactControls() {
    return Row(
      children: [
        Expanded(
          child: Card(
              elevation: 0,
              margin: EdgeInsets.zero,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(4),
                side: BorderSide(color: Theme.of(context).dividerColor),
              ),
              child: Column(
                  children:[
                    _buildTargetSelector(),
                  ])),
        ),
      ],
    );
  }

  Widget _buildTargetSelector() {
    return TargetDropDownSearch(
      onChanged: (Target target) {
        setState(() {
          selectedTargetId = target.targetId;
        });
        context.read<TargetBloc>().add(TargetSelected(target.targetId));
      },

      selectedTargetId: selectedTargetId,
      hintText: '请选择标靶',
      dropdownWidth: 200,
    );
  }
  Widget _buildMobileLayout() {
    final Target? selectedTarget = widget.targets.isEmpty
        ? null
        : widget.targets.firstWhere(
          (t) => t.targetId == selectedTargetId,
      orElse: () => widget.targets.first,
    );

    return SingleChildScrollView(
      child: Column(
        children: [
          if (widget.targets.length > 1 && selectedTarget != null) ...[
            const SizedBox(height: 16),
            _buildXYZChart(selectedTarget),
          ],
          if (widget.showEnvironmental) ...[
            const SizedBox(height: 16),
            _buildEnvOverview(),
          ],


        ],
      ),
    );
  }

  Widget _buildEnvOverview() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(12.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '环境状态',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 12),
            LayoutBuilder(
              builder: (context, constraints) {
                // Check if we need to use a vertical layout
                final isNarrow = constraints.maxWidth < 450;

                if (isNarrow) {
                  // Vertical layout for narrow screens
                  return Column(
                    children: [
                      _buildSparklineCard('温度', _getEnvironmentalData('temperature'), Colors.red),
                      const SizedBox(height: 8),
                      _buildSparklineCard('湿度', _getEnvironmentalData('humidity'), Colors.green),
                      const SizedBox(height: 8),
                      _buildSparklineCard('压强', _getEnvironmentalData('pressure'), Colors.blue),
                    ],
                  );
                } else {
                  // Horizontal layout for wider screens
                  return Row(
                    children: [
                      Expanded(
                          child: _buildSparklineCard(
                              '温度', _getEnvironmentalData('temperature'), Colors.red)
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                          child: _buildSparklineCard(
                              '湿度', _getEnvironmentalData('humidity'), Colors.green)
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                          child: _buildSparklineCard(
                              '压强', _getEnvironmentalData('pressure'), Colors.blue)
                      ),
                    ],
                  );
                }
              },
            ),
          ],
        ),
      ),
    );
  }


  Widget _buildSparklineCard(
      String title, List<MeasurementData> data, Color color) {
    // Extract latest value and calculate trend percentage
    final latestValue = data.isNotEmpty ? data.last.value : 0;
    final changePercent = data.length > 1
        ? ((data.last.value - data[data.length - 2].value) /
            data[data.length - 2].value *
            100)
        : 0.0;

    return Container(
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surfaceVariant.withOpacity(0.3),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(title, style: const TextStyle(fontWeight: FontWeight.bold)),
          const SizedBox(height: 4),
          SizedBox(
            height: 30,
            child: _buildSparkline(data, color),
          ),
          const SizedBox(height: 4),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(latestValue.toStringAsFixed(2),
                  style: const TextStyle(fontWeight: FontWeight.bold)),
              Row(
                children: [
                  Icon(
                    changePercent >= 0
                        ? Icons.arrow_upward
                        : Icons.arrow_downward,
                    size: 12,
                    color: changePercent >= 0 ? Colors.green : Colors.red,
                  ),
                  Text('${changePercent.abs().toStringAsFixed(1)}%',
                      style: TextStyle(
                        fontSize: 10,
                        color: changePercent >= 0 ? Colors.green : Colors.red,
                      )),
                ],
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSparkline(List<MeasurementData> data, Color color) {
    if (data.isEmpty) return const SizedBox();

    final points = data
        .map((point) => FlSpot(
              point.timestamp.millisecondsSinceEpoch.toDouble(),
              point.value,
            ))
        .toList();

    return LineChart(
      LineChartData(
        gridData: FlGridData(show: false),
        titlesData: FlTitlesData(show: false),
        borderData: FlBorderData(show: false),
        lineTouchData: LineTouchData(enabled: false),
        minX: points.first.x,
        maxX: points.last.x,
        minY: data.map((e) => e.value).reduce(min) * 0.95,
        maxY: data.map((e) => e.value).reduce(max) * 1.05,
        lineBarsData: [
          LineChartBarData(
            spots: points,
            isCurved: true,
            color: color,
            barWidth: 2,
            isStrokeCapRound: true,
            dotData: FlDotData(show: false),
            belowBarData: BarAreaData(
              show: true,
              color: color.withOpacity(0.2),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDesktopLayout() {
    // 检查标靶列表是否为空
    if (widget.targets.isEmpty) {
      // 如果标靶为空但需要显示环境数据
      if (widget.showEnvironmental || widget.showIMU) {
        return Column(
          children: [
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: Card(
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Row(
                    children: [
                      Icon(
                        Icons.track_changes_outlined,
                        size: 36,
                        color: Colors.grey,
                      ),
                      SizedBox(width: 16),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              '没有可用的标靶数据',
                              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                                color: Colors.grey[700],
                              ),
                            ),
                            SizedBox(height: 4),
                            Text(
                              '当前正在显示环境数据，请添加标靶以查看位移数据',
                              style: TextStyle(
                                color: Colors.grey[600],
                              ),
                            ),
                          ],
                        ),
                      ),
                      ElevatedButton.icon(
                        icon: Icon(Icons.refresh),
                        label: Text('刷新数据'),
                        onPressed: () {
                          setState(() {
                            // 重新获取数据
                            _fetchHistoricalData();
                          });
                        },
                      ),
                    ],
                  ),
                ),
              ),
            ),
            // 环境和IMU数据部分
            Expanded(
              child: MultiSplitView(
                controller: MultiSplitViewController(),
                initialAreas: [
                  if (widget.showEnvironmental)
                    Area(
                      builder: (ctx, area) => _buildEnvironmentalChart(null),
                    ),
                  if (widget.showIMU)
                    Area(
                      builder: (ctx, area) => _buildIMUOnlyChart(),
                    ),
                ],
              ),
            ),
          ],
        );
      } else {
        // 如果既没有标靶也不需要显示环境数据，则显示空视图
        return _buildEmptyTargetsView();
      }
    }

    final selectedTarget = widget.targets.firstWhere(
          (t) => t.targetId == selectedTargetId,
      orElse: () => widget.targets.first,
    );

    if (viewMode == 'overview') {
      // Overview mode - show all targets with sparklines for quick comparison
      return Column(
        children: [
          // Top section: SparklineCharts for quick overview of all targets
          SizedBox(
            height: 140,
            child: ListView.separated(
              scrollDirection: Axis.horizontal,
              itemCount: widget.targets.length,
              separatorBuilder: (_, __) => const SizedBox(width: 12),
              itemBuilder: (context, index) {
                final target = widget.targets[index];
                return SizedBox(
                  width: 200,
                  child: _buildTargetSummaryCard(target),
                );
              },
            ),
          ),
          const SizedBox(height: 16),
          // Bottom section: Grid of detailed charts
          Expanded(
            child: GridView.builder(
              gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 3,
                childAspectRatio: 1.3,
                crossAxisSpacing: 8,
                mainAxisSpacing: 8,
              ),
              itemCount: widget.targets.length,
              itemBuilder: (context, index) {
                final target = widget.targets[index];
                return _buildTotalDisplacementChart(target);
              },
            ),
          ),
        ],
      );
    } else {
      // Detailed mode - Multi split view
      return Column(
        children: [
          // Target summary header
          _buildTargetSummaryCard(selectedTarget),
          const SizedBox(height: 12),
          // Multi split view for resizable panels
          Expanded(
            child: MultiSplitView(
              controller: MultiSplitViewController(),
              initialAreas: [
                Area(
                    builder: (ctx, area) =>
                        _buildTotalDisplacementChart(selectedTarget)),
                Area(builder: (ctx, area) => _buildXYZChart(selectedTarget)),
                if (widget.showEnvironmental)
                  Area(
                      builder: (ctx, area) =>
                          _buildEnvironmentalChart(selectedTarget)),
                if (widget.showIMU)
                  Area(builder: (ctx, area) => _buildIMUChart(selectedTarget)),
              ],
            ),
          ),
        ],
      );
    }
  }

// 仅显示IMU数据的图表
  Widget _buildIMUOnlyChart() {
    final series = [
      MeasurementSeries(
        id: 'accel_x',
        name: '加速度X',
        data: _getDeviceIMUData('accel_x'),
        color: Colors.red.shade300,
      ),
      MeasurementSeries(
        id: 'accel_y',
        name: '加速度Y',
        data: _getDeviceIMUData('accel_y'),
        color: Colors.green.shade300,
      ),
      MeasurementSeries(
        id: 'accel_z',
        name: '加速度Z',
        data: _getDeviceIMUData('accel_z'),
        color: Colors.blue.shade300,
      ),
    ];

    return EnhancedMeasurementChart(
      series: series,
      title: 'IMU数据',
      timeRange: timeRange,
    );
  }
// 添加一个新方法来显示空标靶提示
  Widget _buildEmptyTargetsView() {
    return Center(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            Icons.track_changes_outlined,
            size: 64,
            color: Colors.grey,
          ),
          SizedBox(height: 16),
          Text(
            '没有可用的标靶数据',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              color: Colors.grey[700],
            ),
          ),
          SizedBox(height: 8),
          Text(
            '请先添加标靶或检查数据连接',
            style: TextStyle(
              color: Colors.grey[600],
            ),
          ),
          SizedBox(height: 24),
          ElevatedButton.icon(
            icon: Icon(Icons.refresh),
            label: Text('重试'),
            onPressed: () {
              setState(() {
                // 重新获取数据
                _fetchHistoricalData();
              });
            },
          ),
        ],
      ),
    );
  }
  Widget _buildTargetSummaryCard(Target target) {
    final displacementData = _getDisplacementData(target);
    final latestValue =
        displacementData.isNotEmpty ? displacementData.last.value : 0;

    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(12.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  target.name,
                  style: Theme.of(context).textTheme.titleMedium,
                ),
                Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: _getStatusColor(1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    _getStatusText(1),
                    style: const TextStyle(color: Colors.white, fontSize: 12),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                Expanded(
                  flex: 2,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text('当前位移: ${latestValue.toStringAsFixed(2)}mm',
                          style: const TextStyle(fontWeight: FontWeight.bold)),
                      Text(
                          '更新时间: ${DateFormat('MM-dd HH:mm').format(displacementData.isNotEmpty ? displacementData.last.timestamp : DateTime.now())}',
                          style: const TextStyle(
                              fontSize: 12, color: Colors.grey)),
                    ],
                  ),
                ),
                //指标
                Expanded(
                  flex: 3,
                  child: SizedBox(
                    height: 40,
                    child: _buildSparkline(displacementData, Colors.blue),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Color _getStatusColor(double value) {
    if (value > 3) return Colors.red;
    if (value > 2) return Colors.orange;
    return Colors.green;
  }

  String _getStatusText(double value) {
    if (value > 3) return '警告';
    if (value > 2) return '注意';
    return '正常';
  } // 位移总量图表

  Widget _buildTotalDisplacementChart(Target target) {
    final series = [
      MeasurementSeries(
        id: '${target.targetId}_displacement',
        name: '位移',
        data: _getDisplacementData(target),
        color: Colors.blue,
      ),
    ];

    return EnhancedMeasurementChart(
      series: series,
      title: '${target.name} - 位移总量',
      timeRange: timeRange,
      showLegend: false,
    );
  }

  // XYZ分量图表
  Widget _buildXYZChart(Target target) {
    final series = [
      MeasurementSeries(
        id: '${target.targetId}_sigmaX',
        name: 'X',
        data: _getAxisData(target, 'sigmaX'),
        color: Colors.red,
      ),
      MeasurementSeries(
        id: '${target.targetId}_sigmaY',
        name: 'Y',
        data: _getAxisData(target, 'sigmaY'),
        color: Colors.green,
      ),
      // MeasurementSeries(
      //   id: '${target.targetId}_z',
      //   name: 'Z',
      //   data: _getAxisData(target, 'z'),
      //   color: Colors.blue,
      // ),
    ];

    return EnhancedMeasurementChart(
      series: series,
      title: '${target.name} - XYZ位移变化',
      timeRange: timeRange,
    );
  }

  // 环境数据图表
  Widget _buildEnvironmentalChart(Target? target) {
    final series = [
      MeasurementSeries(
        id: 'temp',
        name: '温度 (°C)',
        data: _getEnvironmentalData('temperature'),
        color: Colors.red,
      ),
      MeasurementSeries(
        id: 'humidity',
        name: '湿度 (%)',
        data: _getEnvironmentalData('humidity'),
        color: Colors.blue,
      ),
      MeasurementSeries(
        id: 'pressure',
        name: '压强 (hPa)',
        data: _getEnvironmentalData('pressure'),
        color: Colors.green,
      ),
    ];

    return EnhancedMeasurementChart(
      series: series,
      title: '环境数据',
      timeRange: timeRange,
    );
  }
  // IMU数据图表
  Widget _buildIMUChart(Target target) {
    final series = [
      MeasurementSeries(
        id: '${target.targetId}_accel_x',
        name: '加速度X',
        data: _getIMUData(target, 'accel_x'),
        color: Colors.red.shade300,
      ),
      MeasurementSeries(
        id: '${target.targetId}_accel_y',
        name: '加速度Y',
        data: _getIMUData(target, 'accel_y'),
        color: Colors.green.shade300,
      ),
      MeasurementSeries(
        id: '${target.targetId}_accel_z',
        name: '加速度Z',
        data: _getIMUData(target, 'accel_z'),
        color: Colors.blue.shade300,
      ),
    ];

    return EnhancedMeasurementChart(
      series: series,
      title: 'IMU数据',
      timeRange: timeRange,
    );
  }
  // 添加多标靶比较图表

  Widget _buildMultiTargetChart() {
    final metrics = ['x', 'y', 'z'];

    return Column(
      children: [
        for (String metric in metrics) _buildMetricComparisonChart(metric),
      ],
    );
  }

  Widget _buildMetricComparisonChart(String metric) {
    List<MeasurementSeries> series = [];
    String metricName = metric == 'x'
        ? 'X轴'
        : metric == 'y'
            ? 'Y轴'
            : 'Z轴';
    Color metricColor = metric == 'x'
        ? Colors.red
        : metric == 'y'
            ? Colors.green
            : Colors.blue;

    // 为每个标靶创建同一指标的系列数据
    for (var target in widget.targets) {
      series.add(
        MeasurementSeries(
          id: '${target.targetId}_$metric',
          name: target.name,
          data: _getAxisData(target, metric),
          color: Colors.primaries[Random().nextInt(Colors.primaries.length)]
              .withOpacity(0.8),
        ),
      );
    }

    return Padding(
      padding: const EdgeInsets.only(bottom: 16.0),
      child: EnhancedMeasurementChart(
        series: series,
        title: '$metricName 对比',
        timeRange: timeRange,
        showLegend: true,
        maxHeight: 200, // 控制图表高度使多个图表能够在一个屏幕上显示
        allowZoom: true,
      ),
    );
  }





  // 以下方法为模拟数据生成，实际应用中应从真实数据源获取
  List<MeasurementData> _getDisplacementData(Target target) {
    if (widget.measurementChartBloc != null) {
      debugPrint('获取位移数据: ${target.targetId}');
      final key = '${target.targetId}_displacement';
      debugPrint('key: $key');
      final data = widget.measurementChartBloc!.state.dataMap[key] ?? [];

      if (data.isNotEmpty) {
        return data.map((item) => item.toUiModel()).toList();
      }
    }

    // 如果没有bloc或没有数据，返回模拟数据
    return List.generate(100, (i) {
      final time = timeRange.start.add(Duration(
        minutes: ((timeRange.duration.inMinutes / 100) * i).round(),
      ));
      return MeasurementData(
        time,
        2 + sin(i * 0.1) * 0.5 + Random().nextDouble() * 0.2,
      );
    });
  }


  List<MeasurementData> _getAxisData(Target target, String axis) {
    if (widget.measurementChartBloc != null) {
      // final key = '${target.targetId}_$axis';
      final key = '${target.targetId}_$axis';

      debugPrint('获取轴数据key: $key');
      final data = widget.measurementChartBloc!.state.dataMap[key] ?? [];
      if (data.isNotEmpty) {
        return data.map((item) => item.toUiModel()).toList();
      }
    }

    // 如果没有bloc或没有数据，返回模拟数据
    return List.generate(100, (i) {
      final time = timeRange.start.add(Duration(
        minutes: ((timeRange.duration.inMinutes / 100) * i).round(),
      ));
      double value;
      switch (axis) {
        case 'sigmaX':
          value = sin(i * 0.1) + Random().nextDouble() * 0.2;
          break;
        case 'sigmaY':
          value = cos(i * 0.1) + Random().nextDouble() * 0.3;
          break;
        default: // z
          value = sin(i * 0.05) * 0.5 + Random().nextDouble() * 0.1;
      }
      return MeasurementData(time, value);
    });
  }


  List<MeasurementData> _getEnvironmentalData(String type) {
    if (widget.measurementChartBloc != null) {
      final data = widget.measurementChartBloc!.state.dataMap[type] ?? [];

      if (data.isNotEmpty) {
        return data.map((item) => item.toUiModel()).toList();
      }
    }

    // 如果没有bloc或没有数据，返回模拟数据
    return List.generate(100, (i) {
      final time = timeRange.start.add(Duration(
        minutes: ((timeRange.duration.inMinutes / 100) * i).round(),
      ));
      double value;
      switch (type) {
        case 'temperature':
          value = 22 + sin(i * 0.05) * 2;
          break;
        case 'humidity':
          value = 50 + cos(i * 0.05) * 10;
          break;
        case 'pressure': // pressure
          value = 1013 + sin(i * 0.02) * 2;
          break;
        default:
          value = 0;
      }
      return MeasurementData(time, value);
    });
  }



  List<MeasurementData> _getIMUData(Target target, String type) {
    if (widget.measurementChartBloc != null) {
      final key = '${target.targetId}_$type';
      final data = widget.measurementChartBloc!.state.dataMap[key] ?? [];

      if (data.isNotEmpty) {
        return data.map((item) => item.toUiModel()).toList();
      }
    }

    // 如果没有bloc或没有数据，返回模拟数据
    return List.generate(100, (i) {
      final time = timeRange.start.add(Duration(
        minutes: ((timeRange.duration.inMinutes / 100) * i).round(),
      ));
      double value;
      switch (type) {
        case 'accel_x':
          value = sin(i * 0.2) * 0.8;
          break;
        case 'accel_y':
          value = cos(i * 0.2) * 0.6;
          break;
        default: // accel_z
          value = 9.8 + sin(i * 0.1) * 0.2;
      }
      return MeasurementData(time, value);
    });
  }

}

import 'package:flutter/material.dart';
import 'package:fl_chart/fl_chart.dart';
import 'package:intl/intl.dart';
import 'dart:math';

import 'model.dart';

class EnhancedMeasurementChart extends StatefulWidget {
  final List<MeasurementSeries> series;
  final String title;
  final DateTimeRange timeRange;
  final bool showLegend;
  final bool showGrid;
  final bool allowZoom;
  final double? maxHeight;
  final int legendMaxItems;

  final List<String>? visibleMetrics;
  final bool showThresholds;

  const EnhancedMeasurementChart({
    Key? key,
    required this.series,
    required this.title,
    required this.timeRange,
    this.showLegend = true,
    this.showGrid = true,
    this.allowZoom = true,
    this.legendMaxItems = 5,
    this.visibleMetrics,
    this.showThresholds = false,
    this.maxHeight,
  }) : super(key: key);

  @override
  State<EnhancedMeasurementChart> createState() => _EnhancedMeasurementChartState();
}

class _EnhancedMeasurementChartState extends State<EnhancedMeasurementChart> {
  double minY = 0;
  double maxY = 1000;
  double minX = 0;
  double maxX = 1;
  bool isZooming = false;
  double zoomLevel = 1.0;
  double scrollPosition = 0.0;
  Map<String, bool> seriesVisibility = {};
  List<MeasurementSeries> filteredSeries = [];

  @override
  void initState() {
    super.initState();
    _initSeriesVisibility();
    _updateFilteredSeries();
    _calculateAxisLimits();
  }
  @override
  void didUpdateWidget(EnhancedMeasurementChart oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.series != widget.series ||
        oldWidget.timeRange != widget.timeRange ||
        oldWidget.visibleMetrics != widget.visibleMetrics) {
      _initSeriesVisibility();
      _updateFilteredSeries();
      _calculateAxisLimits();
    }
  }
  void _initSeriesVisibility() {
    // Initialize all series as visible by default
    for (var series in widget.series) {
      seriesVisibility[series.id] = seriesVisibility[series.id] ?? true;
    }
  }

  void _updateFilteredSeries() {
    filteredSeries = widget.series.where((series) {
      // Filter by visibility and metrics if provided
      bool isVisible = seriesVisibility[series.id] ?? true;

      if (widget.visibleMetrics != null && widget.visibleMetrics!.isNotEmpty) {
        // Check if the series matches any of the visible metrics
        bool matchesMetric = false;
        for (var metric in widget.visibleMetrics!) {
          if (series.id.contains(metric)) {
            matchesMetric = true;
            break;
          }
        }
        return isVisible && matchesMetric;
      }

      return isVisible;
    }).toList();
  }



  void _calculateAxisLimits() {
    if (widget.series.isEmpty || widget.series.any((s) => s.data.isEmpty)) {
      return;
    }

    double min = double.infinity;
    double max = double.negativeInfinity;

    for (var series in widget.series) {
      for (var point in series.data) {
        if (point.value < min) min = point.value;
        if (point.value > max) max = point.value;
      }
    }

    // Add padding to the range
    double padding = (max - min) * 0.1;
    setState(() {
      minY = min - padding;
      maxY = max + padding;
      // X axis is time range in milliseconds since epoch
      minX = widget.timeRange.start.millisecondsSinceEpoch.toDouble();
      maxX = widget.timeRange.end.millisecondsSinceEpoch.toDouble();
    });
  }

  @override
  Widget build(BuildContext context) {
    final isSmallScreen = MediaQuery.of(context).size.width < 600;

    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(12.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                  child: Text(
                    widget.title,
                    style: Theme.of(context).textTheme.titleMedium,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
                // Row(
                //   children: [
                //     if (widget.allowZoom)
                //       IconButton(
                //         icon: Icon(
                //           isZooming ? Icons.zoom_out : Icons.zoom_in,
                //           size: 20,
                //         ),
                //         onPressed: () => setState(() => isZooming = !isZooming),
                //         tooltip: isZooming ? 'Disable Zoom' : 'Enable Zoom',
                //       ),
                //     IconButton(
                //       icon: const Icon(Icons.filter_list, size: 20),
                //       onPressed: _showSeriesFilterDialog,
                //       tooltip: 'Filter Series',
                //     ),
                //   ],
                // ),
              ],
            ),
            if (widget.showLegend && !isSmallScreen) ...[
              const SizedBox(height: 8),
              _buildLegend(),
            ],
            const SizedBox(height: 8),
            LimitedBox(
              maxHeight: widget.maxHeight ?? 300,
              child: widget.series.isEmpty || widget.series.any((s) => s.data.isEmpty)
                  ? const Center(child: Text('No data available'))
                  : _buildChart(),
            ),
            if (widget.showLegend && isSmallScreen) ...[
              const SizedBox(height: 12),
              _buildLegend(compact: true),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildChart() {
    return Padding(
      padding: const EdgeInsets.only(right: 16, top: 16),
      child: LineChart(
        LineChartData(
          // minX: minX,
          // maxX: maxX,
          // minY: minY,
          // maxY: maxY,
          lineTouchData: LineTouchData(
            touchTooltipData: LineTouchTooltipData(

              getTooltipItems: (touchedSpots) {
                if (touchedSpots.isEmpty) return [];
                // Format date from the first spot
                final date = DateTime.fromMillisecondsSinceEpoch(touchedSpots[0].x.toInt());
                final formattedDate = DateFormat('MM-dd HH:mm').format(date);
                // Create one tooltip per touched spot
                return touchedSpots.map((spot) {
                  final seriesIndex = spot.barIndex;
                  if (seriesIndex >= 0 && seriesIndex < widget.series.length) {
                    final series = widget.series[seriesIndex];
                    return LineTooltipItem(
                      '${series.name}: ${spot.y.toStringAsFixed(2)}',
                      TextStyle(color: series.color),
                      children: [
                        TextSpan(
                          text: '\n$formattedDate',
                          style: const TextStyle(
                            color: Colors.grey,
                            fontWeight: FontWeight.bold,
                            fontSize: 12,
                          ),
                        ),
                      ],
                    );
                  } else {
                    return null;
                  }
                }).toList();
              },
            ),
            handleBuiltInTouches: true,
          ),
          gridData: FlGridData(
            show: widget.showGrid,
            drawVerticalLine: widget.showGrid,
            drawHorizontalLine: widget.showGrid,
          ),
          titlesData: FlTitlesData(
            bottomTitles: AxisTitles(
              sideTitles: SideTitles(
                showTitles: true,
                getTitlesWidget: (value, meta) {
                  final date = DateTime.fromMillisecondsSinceEpoch(value.toInt());
                  return Padding(
                    padding: const EdgeInsets.only(top: 8.0),
                    child: Text(
                      DateFormat('MM-dd\nHH:mm').format(date),
                      style: const TextStyle(fontSize: 10),
                    ),
                  );
                },
                interval: (maxX - minX) / 5,
              ),
            ),
            leftTitles: AxisTitles(
              sideTitles: SideTitles(
                showTitles: true,
                getTitlesWidget: (value, meta) {
                  return Text(
                    value.toStringAsFixed(1),
                    style: const TextStyle(fontSize: 10),
                  );
                },
                reservedSize: 40,
              ),
            ),
            rightTitles: const AxisTitles(
              sideTitles: SideTitles(showTitles: false),
            ),
            topTitles: const AxisTitles(
              sideTitles: SideTitles(showTitles: false),
            ),
          ),
          borderData: FlBorderData(
            show: true,
            border: Border.all(
              color: const Color(0xff37434d),
              width: 1,
            ),
          ),
          lineBarsData: _createLineBarsData(),
          extraLinesData: ExtraLinesData(
            horizontalLines: [
              HorizontalLine(
                y: 25.0,
                color: Colors.red,
                strokeWidth: 2,
                dashArray: [5, 5],
              ),
            ],
          ),
        ),
      ),
    );
  }

  List<LineChartBarData> _createLineBarsData() {
    return widget.series.map((series) {
      return LineChartBarData(
        spots: series.data.map((point) {
          return FlSpot(
            point.timestamp.millisecondsSinceEpoch.toDouble(),
            point.value,
          );
        }).toList(),
        isCurved: false,
        color: series.color,
        barWidth: 2,
        isStrokeCapRound: true,
        dotData: const FlDotData(show: true),
        belowBarData: BarAreaData(show: false),
      );
    }).toList();
  }

  Widget _buildLegend({bool compact = false}) {
    return Wrap(
      spacing: 16,
      runSpacing: 8,
      children: widget.series.map((series) {
        final isVisible = seriesVisibility[series.id] ?? true;
        return GestureDetector(
          onTap: () {
            setState(() {
              seriesVisibility[series.id] = !isVisible;
              _updateFilteredSeries();
              _calculateAxisLimits();
            });
          },
          child: Opacity(
            opacity: isVisible ? 1.0 : 0.5,
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Container(
                  width: 12,
                  height: 12,
                  color: series.color,
                ),
                const SizedBox(width: 4),
                Text(
                  series.name,
                  style: TextStyle(
                    fontSize: compact ? 10 : 12,
                    decoration: isVisible ? null : TextDecoration.lineThrough,
                  ),
                ),
              ],
            ),
          ),
        );
      }).toList(),
    );
  }

void _showSeriesFilterDialog() {
  showDialog(
    context: context,
    builder: (context) {
      return AlertDialog(
        title: const Text('Filter Series'),
        content: SizedBox(
          width: double.maxFinite,
          child: ListView(
            shrinkWrap: true,
            children: widget.series.map((series) {
              return CheckboxListTile(
                title: Text(series.name),
                value: seriesVisibility[series.id] ?? true,
                onChanged: (bool? value) {
                  setState(() {
                    seriesVisibility[series.id] = value ?? true;
                    _updateFilteredSeries();
                    _calculateAxisLimits();
                  });
                },
                secondary: Container(
                  width: 16,
                  height: 16,
                  color: series.color,
                ),
                dense: true,
              );
            }).toList(),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              setState(() {
                for (var series in widget.series) {
                  seriesVisibility[series.id] = true;
                }
                _updateFilteredSeries();
                _calculateAxisLimits();
              });
              Navigator.pop(context);
            },
            child: const Text('Reset All'),
          ),
        ],
      );
    },
  );
}
}
import 'package:flutter/material.dart';
import 'package:fl_chart/fl_chart.dart';
import '../../../model/data_point.dart';

class FLSparklineChart extends StatelessWidget {
  final List<DataPoint> data;
  final Color lineColor;
  final Color fillColor;

  const FLSparklineChart({
    required this.data,
    required this.lineColor,
    required this.fillColor,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    if (data.isEmpty) {
      return const Center(child: Text('暂无数据'));
    }

    final spots = data.asMap().entries.map((entry) {
      return FlSpot(entry.key.toDouble(), entry.value.value);
    }).toList();

    final avgValue = data.map((p) => p.value).reduce((a, b) => a + b) / data.length;

    return LineChart(
      LineChartData(
        gridData: FlGridData(
          show: false,
          drawVerticalLine: false,
          horizontalInterval: 1,
          drawHorizontalLine: true,
          getDrawingHorizontalLine: (value) => FlLine(
            color: Colors.grey.withOpacity(0.2),
            strokeWidth: 1,
          ),
        ),
        titlesData: const FlTitlesData(
          show: true,
        ),
        borderData: FlBorderData(show: true),
        lineBarsData: [
          LineChartBarData(
            spots: spots,
            isCurved: false,
            color: lineColor,
            barWidth: 2,
            isStrokeCapRound: true,
            dotData: FlDotData(
              show: true,
              getDotPainter: (spot, percent, bar, index) =>
              index == spots.length - 1
                  ? FlDotCirclePainter(
                radius: 4,
                color: lineColor,
                strokeWidth: 2,
                strokeColor: Colors.white,
              )
                  : FlDotCirclePainter(radius: 0),
            ),
            belowBarData: BarAreaData(
              show: true,
              color: fillColor.withOpacity(0.2),
            ),
          ),
        ],
        extraLinesData: ExtraLinesData(
          horizontalLines: [
            HorizontalLine(
              y: avgValue,
              color: Colors.green,
              strokeWidth: 1,
              dashArray: [5, 5],
              label: HorizontalLineLabel(
                show: true,
                labelResolver: (line) => 'Avg: ${avgValue.toStringAsFixed(1)}',
                style: const TextStyle(color: Colors.green, fontSize: 10),
                alignment: Alignment.topRight,
              ),
            ),
          ],
        ),
        lineTouchData: LineTouchData(enabled: false),
        minY: spots.map((s) => s.y).reduce((a, b) => a < b ? a : b) * 0.9,
        maxY: spots.map((s) => s.y).reduce((a, b) => a > b ? a : b) * 1.1,
      ),
    );
  }}
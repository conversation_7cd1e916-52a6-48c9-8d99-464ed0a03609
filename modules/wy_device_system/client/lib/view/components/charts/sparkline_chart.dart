import 'package:chart_sparkline/chart_sparkline.dart';
import 'package:flutter/material.dart';

import '../../../model/data_point.dart';

class SparklineChart extends StatelessWidget {
  final List<DataPoint> data;
  final Color lineColor;
  final Color fillColor;

  const SparklineChart({
    required this.data,
    required this.lineColor,
    required this.fillColor,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    if (data.isEmpty) {
      return const Center(child: Text('暂无数据'));
    }

    // Convert DataPoint list to a list of doubles for Sparkline
    final values = data.map((point) => point.value).toList();

    return Sparkline(
      data: values,
      lineColor: lineColor,
      fillMode: FillMode.below,

      lineWidth: 2.0,
      pointsMode: PointsMode.last,
      averageLine: true,
      averageLineColor: Colors.green,
      // averageLabel: true,
      gridLinesEnable: true,
      useCubicSmoothing: false,
      // cubicSmoothingFactor: 0.2,
      enableThreshold: true,
      thresholdSize: 0.5,
      kLine: ['last'],
      maxLabel: true,
    );
  }
}
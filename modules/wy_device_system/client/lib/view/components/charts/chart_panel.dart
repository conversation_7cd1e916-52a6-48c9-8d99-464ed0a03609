import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

import '../../../bloc/targets_blocs.dart';
import '../../../bloc/wy_device_blocs.dart';
import '../../../model/measurement_query.dart';
import '../../../model/target.dart';
import '../targets/target_selector.dart';
import '../charts/flexible-line-chart.dart';
import 'package:provider/provider.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class IntegratedChartPanel extends StatefulWidget {
  final Function(MeasurementQuery query)? onQueryChanged;
  final Widget? chartWidget; // 可选的图表组件

  const IntegratedChartPanel({
    super.key,
    this.onQueryChanged,
    this.chartWidget,
  });

  @override
  State<IntegratedChartPanel> createState() => _IntegratedChartPanelState();
}

class _IntegratedChartPanelState extends State<IntegratedChartPanel>
    with TickerProviderStateMixin {

  late AnimationController _filterAnimationController;
  late Animation<double> _filterAnimation;
  bool _isFilterExpanded = false;

  // 查询状态
  MeasurementType _selectedChartType = MeasurementType.displacement;
  DateTime _startDate = DateTime.now().subtract(const Duration(hours: 24));
  DateTime _endDate = DateTime.now();
  bool _isRealtime = true;
  Duration _selectedTimeWindow = const Duration(minutes: 10);

  bool get _showTargetSelector => _selectedChartType == MeasurementType.displacement;
  final _dateFormat = DateFormat('MM-dd HH:mm');

  @override
  void initState() {
    super.initState();
    _filterAnimationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _filterAnimation = CurvedAnimation(
      parent: _filterAnimationController,
      curve: Curves.easeInOut,
    );
  }

  @override
  void dispose() {
    _filterAnimationController.dispose();
    super.dispose();
  }

  void _toggleFilter() {
    setState(() {
      _isFilterExpanded = !_isFilterExpanded;
      if (_isFilterExpanded) {
        _filterAnimationController.forward();
      } else {
        _filterAnimationController.reverse();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.06),
            blurRadius: 12,
            spreadRadius: 0,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildHeader(),
          _buildQuickFilters(),
          AnimatedBuilder(
            animation: _filterAnimation,
            builder: (context, child) {
              return SizeTransition(
                sizeFactor: _filterAnimation,
                child: _buildExpandedFilters(),
              );
            },
          ),
          const Divider(height: 1),
          _buildChartArea(),
        ],
      ),
    );
  }

  Widget _buildHeader() {
    final theme = Theme.of(context);

    return Container(
      padding: const EdgeInsets.all(16),
      child: Row(
        children: [
          // 图表类型指示器
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            decoration: BoxDecoration(
              color: theme.primaryColor.withOpacity(0.1),
              borderRadius: BorderRadius.circular(20),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  _selectedChartType == MeasurementType.displacement
                      ? Icons.show_chart
                      : Icons.thermostat,
                  size: 16,
                  color: theme.primaryColor,
                ),
                const SizedBox(width: 6),
                Text(
                  _selectedChartType.displayName,
                  style: TextStyle(
                    fontSize: 13,
                    fontWeight: FontWeight.w600,
                    color: theme.primaryColor,
                  ),
                ),
              ],
            ),
          ),

          const SizedBox(width: 12),

          // 时间范围显示
          Expanded(
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 4),
              decoration: BoxDecoration(
                color: Colors.grey.shade50,
                borderRadius: BorderRadius.circular(16),
                border: Border.all(color: Colors.grey.shade200),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(
                    _isRealtime ? Icons.access_time : Icons.date_range,
                    size: 14,
                    color: Colors.grey.shade600,
                  ),
                  const SizedBox(width: 6),
                  Flexible(
                    child: Text(
                      _isRealtime
                          ? '实时 (${_formatDuration(_selectedTimeWindow)})'
                          : '${_dateFormat.format(_startDate)} ~ ${_dateFormat.format(_endDate)}',
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.grey.shade700,
                        fontWeight: FontWeight.w500,
                      ),
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ],
              ),
            ),
          ),

          const SizedBox(width: 8),

          // 筛选器展开/收起按钮
          IconButton(
            onPressed: _toggleFilter,
            icon: AnimatedRotation(
              turns: _isFilterExpanded ? 0.5 : 0,
              duration: const Duration(milliseconds: 300),
              child: const Icon(Icons.expand_more),
            ),
            style: IconButton.styleFrom(
              backgroundColor: Colors.grey.shade100,
              foregroundColor: Colors.grey.shade600,
              minimumSize: const Size(36, 36),
              padding: EdgeInsets.zero,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildQuickFilters() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: SingleChildScrollView(
        scrollDirection: Axis.horizontal,
        child: Row(
          children: [
            // 图表类型切换
            _buildQuickFilterChip(
              label: '位移',
              isSelected: _selectedChartType == MeasurementType.displacement,
              onTap: () => _updateChartType(MeasurementType.displacement),
            ),
            const SizedBox(width: 8),
            _buildQuickFilterChip(
              label: '环境',
              isSelected: _selectedChartType == MeasurementType.envStatus,
              onTap: () => _updateChartType(MeasurementType.envStatus),
            ),

            const SizedBox(width: 16),
            Container(width: 1, height: 20, color: Colors.grey.shade300),
            const SizedBox(width: 16),

            // 实时/历史切换
            _buildQuickFilterChip(
              label: '实时',
              isSelected: _isRealtime,
              onTap: () => _updateRealtimeMode(true),
            ),
            const SizedBox(width: 8),
            _buildQuickFilterChip(
              label: '历史',
              isSelected: !_isRealtime,
              onTap: () => _updateRealtimeMode(false),
            ),

            // 快速时间选择（仅实时模式）
            if (_isRealtime) ...[
              const SizedBox(width: 16),
              Container(width: 1, height: 20, color: Colors.grey.shade300),
              const SizedBox(width: 16),
              ..._getQuickTimeOptions().map((option) => Padding(
                padding: const EdgeInsets.only(right: 8),
                child: _buildQuickFilterChip(
                  label: option['label'] as String,
                  isSelected: _selectedTimeWindow == option['value'],
                  onTap: () => _updateTimeWindow(option['value'] as Duration),
                ),
              )),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildQuickFilterChip({
    required String label,
    required bool isSelected,
    required VoidCallback onTap,
  }) {
    final theme = Theme.of(context);

    return GestureDetector(
      onTap: onTap,
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
        decoration: BoxDecoration(
          color: isSelected ? theme.primaryColor : Colors.transparent,
          borderRadius: BorderRadius.circular(16),
          border: Border.all(
            color: isSelected ? theme.primaryColor : Colors.grey.shade300,
          ),
        ),
        child: Text(
          label,
          style: TextStyle(
            fontSize: 12,
            fontWeight: FontWeight.w500,
            color: isSelected ? Colors.white : Colors.grey.shade700,
          ),
        ),
      ),
    );
  }

  Widget _buildExpandedFilters() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        border: Border(
          top: BorderSide(color: Colors.grey.shade200),
          bottom: BorderSide(color: Colors.grey.shade200),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 标靶选择（仅位移图表显示）
          if (_showTargetSelector) ...[
            _buildTargetSelector(),
            const SizedBox(height: 16),
          ],

          // 时间范围详细设置
          if (!_isRealtime) _buildHistoricalTimeSelector(),
          if (_isRealtime) _buildCustomTimeWindowInput(),
        ],
      ),
    );
  }

  Widget _buildTargetSelector() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          '标靶选择',
          style: TextStyle(fontSize: 14, fontWeight: FontWeight.w600),
        ),
        const SizedBox(height: 8),
        Container(
          height: 48,
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: Colors.grey.shade300),
          ),
          child: BlocBuilder<TargetBloc, TargetState>(
            builder: (context, state) {
              return TargetDropDownSearch(
                onChanged: (Target target) {
                  context.read<TargetBloc>().add(TargetSelected(target.targetId));
                  _triggerQueryUpdate();
                },
                selectedTargetId: state.selectedTargetId,
                hintText: '请选择标靶',
                dropdownWidth: double.infinity,
              );
            },
          ),
        ),
      ],
    );
  }

  Widget _buildHistoricalTimeSelector() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          '历史时间范围',
          style: TextStyle(fontSize: 14, fontWeight: FontWeight.w600),
        ),
        const SizedBox(height: 8),
        Row(
          children: [
            Expanded(
              child: _buildDateTimeField(true, '开始时间', _startDate),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildDateTimeField(false, '结束时间', _endDate),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildCustomTimeWindowInput() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          '自定义时间窗口',
          style: TextStyle(fontSize: 14, fontWeight: FontWeight.w600),
        ),
        const SizedBox(height: 8),
        Row(
          children: [
            Expanded(
              child: TextButton.icon(
                onPressed: _showCustomDurationDialog,
                icon: const Icon(Icons.schedule, size: 16),
                label: Text('当前: ${_formatDuration(_selectedTimeWindow)}'),
                style: TextButton.styleFrom(
                  backgroundColor: Colors.white,
                  foregroundColor: Colors.grey.shade700,
                  side: BorderSide(color: Colors.grey.shade300),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                  padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildChartArea() {
    return Container(
      height: 300, // 固定图表高度
      padding: const EdgeInsets.all(16),
      child: widget.chartWidget ?? _buildDefaultChart(),
    );
  }

  Widget _buildDefaultChart() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.shade200),
      ),
      child: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.show_chart, size: 48, color: Colors.grey),
            SizedBox(height: 8),
            Text(
              '图表数据加载中...',
              style: TextStyle(color: Colors.grey, fontSize: 14),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDateTimeField(bool isStart, String label, DateTime value) {
    return GestureDetector(
      onTap: () => _selectDate(isStart),
      child: Container(
        height: 48,
        padding: const EdgeInsets.symmetric(horizontal: 12),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: Colors.grey.shade300),
        ),
        child: Row(
          children: [
            Icon(Icons.calendar_today, size: 16, color: Colors.grey.shade600),
            const SizedBox(width: 8),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    label,
                    style: TextStyle(
                      fontSize: 11,
                      color: Colors.grey.shade600,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  Text(
                    _dateFormat.format(value),
                    style: const TextStyle(
                      fontSize: 13,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  // 辅助方法
  List<Map<String, dynamic>> _getQuickTimeOptions() {
    return [
      {'label': '1分钟', 'value': const Duration(minutes: 1)},
      {'label': '5分钟', 'value': const Duration(minutes: 5)},
      {'label': '10分钟', 'value': const Duration(minutes: 10)},
      {'label': '30分钟', 'value': const Duration(minutes: 30)},
      {'label': '1小时', 'value': const Duration(hours: 1)},
    ];
  }

  String _formatDuration(Duration duration) {
    if (duration.inDays > 0) {
      return '${duration.inDays}天';
    } else if (duration.inHours > 0) {
      return '${duration.inHours}小时';
    } else {
      return '${duration.inMinutes}分钟';
    }
  }

  String _getSelectedTargetName() {
    try {
      final targetBloc = context.read<TargetBloc>();
      final selectedTargetId = targetBloc.state.selectedTargetId;
      if (selectedTargetId == null) return '未选择标靶';

      final targets = targetBloc.state.targets;
      final target = targets.firstWhere((t) => t.targetId == selectedTargetId);
      return target.name;
    } catch (e) {
      return '未选择标靶';
    }
  }

  void _updateChartType(MeasurementType type) {
    setState(() {
      _selectedChartType = type;
    });
    _triggerQueryUpdate();
  }

  void _updateRealtimeMode(bool isRealtime) {
    setState(() {
      _isRealtime = isRealtime;
    });
    _triggerQueryUpdate();
  }

  void _updateTimeWindow(Duration duration) {
    setState(() {
      _selectedTimeWindow = duration;
    });
    _triggerQueryUpdate();
  }

  void _triggerQueryUpdate() {
    if (widget.onQueryChanged != null) {
      final query = MeasurementQuery(
        chartType: _selectedChartType,
        startTime: _isRealtime
            ? DateTime.now().subtract(_selectedTimeWindow)
            : _startDate,
        endTime: _isRealtime ? DateTime.now() : _endDate,
        isRealtime: _isRealtime,
        selectedTargetId: _showTargetSelector
            ? context.read<TargetBloc>().state.selectedTargetId
            : null,
      );
      widget.onQueryChanged!(query);
    }
  }

  Future<void> _selectDate(bool isStart) async {
    final DateTime initialDate = isStart ? _startDate : _endDate;

    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: initialDate,
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
    );

    if (picked != null) {
      final TimeOfDay? pickedTime = await showTimePicker(
        context: context,
        initialTime: TimeOfDay.fromDateTime(initialDate),
      );

      if (pickedTime != null) {
        setState(() {
          if (isStart) {
            _startDate = DateTime(
              picked.year, picked.month, picked.day,
              pickedTime.hour, pickedTime.minute,
            );
          } else {
            _endDate = DateTime(
              picked.year, picked.month, picked.day,
              pickedTime.hour, pickedTime.minute,
            );
          }
        });

        if (_startDate.isAfter(_endDate)) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('开始时间不能晚于结束时间')),
          );
          setState(() {
            if (isStart) {
              _startDate = _endDate.subtract(const Duration(hours: 1));
            } else {
              _endDate = _startDate.add(const Duration(hours: 1));
            }
          });
        }

        _triggerQueryUpdate();
      }
    }
  }

  void _showCustomDurationDialog() {
    final TextEditingController valueController = TextEditingController();
    String selectedUnit = '分钟';

    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
          title: const Text('自定义时间范围'),
          content: Row(
            children: [
              Expanded(
                child: TextField(
                  controller: valueController,
                  keyboardType: TextInputType.number,
                  decoration: const InputDecoration(
                    labelText: '数值',
                    border: OutlineInputBorder(),
                  ),
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: StatefulBuilder(
                  builder: (context, setState) {
                    return DropdownButtonFormField<String>(
                      value: selectedUnit,
                      decoration: const InputDecoration(
                        labelText: '单位',
                        border: OutlineInputBorder(),
                      ),
                      items: const [
                        DropdownMenuItem(value: '分钟', child: Text('分钟')),
                        DropdownMenuItem(value: '小时', child: Text('小时')),
                        DropdownMenuItem(value: '天', child: Text('天')),
                      ],
                      onChanged: (value) {
                        if (value != null) {
                          setState(() => selectedUnit = value);
                        }
                      },
                    );
                  },
                ),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('取消'),
            ),
            ElevatedButton(
              onPressed: () {
                int? value = int.tryParse(valueController.text);
                if (value != null && value > 0) {
                  Duration customDuration;
                  switch (selectedUnit) {
                    case '分钟':
                      customDuration = Duration(minutes: value);
                      break;
                    case '小时':
                      customDuration = Duration(hours: value);
                      break;
                    case '天':
                      customDuration = Duration(days: value);
                      break;
                    default:
                      customDuration = Duration(minutes: value);
                  }

                  setState(() => _selectedTimeWindow = customDuration);
                  _triggerQueryUpdate();
                  Navigator.of(context).pop();
                } else {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(content: Text('请输入有效的正数值')),
                  );
                }
              },
              child: const Text('确定'),
            ),
          ],
        );
      },
    );
  }
}

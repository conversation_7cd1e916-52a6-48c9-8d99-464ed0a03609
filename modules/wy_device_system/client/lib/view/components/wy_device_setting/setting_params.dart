import 'package:client/model/wy_device.dart';
import 'package:flutter/material.dart';
import 'package:tdesign_flutter/tdesign_flutter.dart';

class SettingParams extends StatefulWidget {
  final WyDeviceAttribute? deviceAttribute;
  final onConfirm;
  const SettingParams(
      {super.key, this.deviceAttribute, required this.onConfirm});

  @override
  State<SettingParams> createState() => _SettingParamsState();
}

class _SettingParamsState extends State<SettingParams>
    with SingleTickerProviderStateMixin {
  late List<String> _tabs;
  late TabController _tabController;
  final cameraExposureList = ['High', 'Middle', 'Low'];
  // 稳定采样
  late bool stableSample;
  // 采样频率
  late double wyMaxSampleFreq;
  // 滤波设置
  late String type;
  // 小窗口
  late int minor;
  // 大窗口
  late int major;

  // 数据正负方向
  bool showDirection = true;
  late bool sigmaXDirection;
  late bool sigmaYDirection;
  // 曝光设置
  late String wyCameraExposure;
  // 算法阈值
  late int wyBrightnessThr;

  // 启用加热器
  late bool wyEnableHeater;
  // 环境状态报告间隔 (秒)
  late int reportEnvStatusInterval;
  // 加热器温度范围 (°C)
  late List<int> wyHeaterTemperatureRange;
  @override
  void initState() {
    super.initState();
    _tabs = ['测量设置', '相机设置', '环境控制'];
    _tabController = TabController(length: _tabs.length, vsync: this);
    final deviceAttribute = widget.deviceAttribute!;
    // 稳定采样
    stableSample = deviceAttribute.stableSample;
    wyMaxSampleFreq = deviceAttribute.wyMaxSampleFreq;
    // 滤波设置
    type = deviceAttribute.filter.type;
    minor = deviceAttribute.filter.args.minor;
    major = deviceAttribute.filter.args.major;
    // 数据正负方向
    sigmaXDirection = deviceAttribute.sigmaXDirection;
    sigmaYDirection = deviceAttribute.sigmaYDirection;
    // 相机设置
    wyCameraExposure = deviceAttribute.wyCameraExposure;
    wyBrightnessThr = deviceAttribute.wyBrightnessThr;

    // 环境控制
    wyEnableHeater = deviceAttribute.wyEnableHeater;
    reportEnvStatusInterval = deviceAttribute.reportEnvStatusInterval;
    wyHeaterTemperatureRange = deviceAttribute.wyHeaterTemperatureRange;
  }

  @override
  Widget build(BuildContext context) {
    final screenSize = MediaQuery.of(context).size;
    const boxHeight = 550.0;

    return Container(
      width: screenSize.width * 0.8,
      height: boxHeight,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.2),
            blurRadius: 16,
            offset: const Offset(0, 8),
          ),
        ],
      ),
      child: Column(
        children: [
          TDTabBar(
            height: 60,
            labelColor: TDTheme.of(context).brandNormalColor,
            backgroundColor: Colors.white,
            showIndicator: true,
            controller: _tabController,
            tabs: _tabs
                .map((t) => TDTab(
                      text: t,
                      size: TDTabSize.large,
                    ))
                .toList(),
          ),
          Container(
            height: boxHeight - 60 - 80,
            color: Colors.white,
            child: TDTabBarView(
              controller: _tabController,
              children: [
                SingleChildScrollView(
                  child: Column(
                    children: [
                      TDCell(
                        title: '稳定采样',
                        noteWidget: TDSwitch(
                          isOn: stableSample,
                          onChanged: (value) {
                            setState(() {
                              stableSample = value;
                            });
                            return value;
                          },
                        ),
                      ),
                      TDCell(
                        title: '采样频率',
                        hover: false,
                        style: TDCellStyle(
                            padding: const EdgeInsets.fromLTRB(18, 0, 0, 0)),
                        noteWidget: Container(
                          alignment: Alignment.centerRight,
                          width: 180,
                          child: TDSlider(
                            sliderThemeData: TDSliderThemeData(
                              context: context,
                              showThumbValue: true,
                              min: 0,
                              max: 60.0,
                            ),
                            rightLabel: 'Hz',
                            value: wyMaxSampleFreq,
                            onChanged: (value) {
                              setState(() {
                                wyMaxSampleFreq = value;
                              });
                            },
                          ),
                        ),
                      ),
                      const TDDivider(),
                      TDCell(
                        title: '滤波设置',
                        description: '启用平均滤波处理数据',
                        noteWidget: TDSwitch(
                          isOn: type == 'mean',
                          onChanged: (value) {
                            setState(() {
                              type = value ? 'mean' : 'none';
                            });
                            return value;
                          },
                        ),
                      ),
                      if (type == 'mean')
                        Column(
                          children: [
                            TDCell(
                              hover: false,
                              title: '小窗口',
                              noteWidget: TDStepper(
                                size: TDStepperSize.large,
                                theme: TDStepperTheme.outline,
                                value: minor,
                                defaultValue: minor,
                                min: 1,
                                max: 1000,
                                onChange: (value) {
                                  setState(() {
                                    minor = value;
                                    if (minor > major) {
                                      major = value;
                                    }
                                  });
                                },
                              ),
                            ),
                            TDCell(
                              hover: false,
                              title: '大窗口',
                              noteWidget: TDStepper(
                                size: TDStepperSize.large,
                                theme: TDStepperTheme.outline,
                                value: major,
                                defaultValue: major,
                                min: 1,
                                max: 1000,
                                onChange: (value) {
                                  setState(() {
                                    major = value;
                                    if (major < minor) {
                                      minor = value;
                                    }
                                  });
                                },
                              ),
                            ),
                          ],
                        ),
                      const TDDivider(),
                      TDCell(
                        title: '数据正负方向设置',
                        description: '以下方向均以摄像头正对标靶的视角作为参考',
                        rightIconWidget: Icon(showDirection
                            ? TDIcons.chevron_down
                            : TDIcons.chevron_right),
                        onClick: (cell) {
                          setState(() {
                            showDirection = !showDirection;
                          });
                        },
                      ),
                      if (showDirection)
                        Column(
                          children: [
                            TDCell(
                              title: '左右正负方向',
                              note: sigmaXDirection ? '左正右负' : '左负右正',
                              rightIconWidget: TDSwitch(
                                isOn: sigmaXDirection,
                                onChanged: (value) {
                                  setState(() {
                                    sigmaXDirection = value;
                                  });
                                  return value;
                                },
                              ),
                            ),
                            TDCell(
                                title: '上下正负方向',
                                note: sigmaYDirection ? '上正下负' : '上负下正',
                                rightIconWidget: TDSwitch(
                                  isOn: sigmaYDirection,
                                  onChanged: (value) {
                                    setState(() {
                                      sigmaYDirection = value;
                                    });
                                    return value;
                                  },
                                )),
                          ],
                        ),
                    ],
                  ),
                ),
                SingleChildScrollView(
                  child: Column(
                    children: [
                      TDCell(
                          hover: false,
                          title: '曝光设置',
                          noteWidget: Row(
                            mainAxisSize: MainAxisSize.min,
                            spacing: 8,
                            children: cameraExposureList
                                .map((item) => TDSelectTag(
                                      item,
                                      theme: TDTagTheme.primary,
                                      isOutline: true,
                                      isLight: true,
                                      isSelected: wyCameraExposure == item,
                                      onSelectChanged: (value) {
                                        setState(() {
                                          if (value) wyCameraExposure = item;
                                        });
                                      },
                                    ))
                                .toList(),
                          )),
                      TDCell(
                        hover: false,
                        title: '算法阈值',
                        noteWidget: TDStepper(
                          size: TDStepperSize.large,
                          theme: TDStepperTheme.outline,
                          value: wyBrightnessThr,
                          min: 0,
                          max: 255,
                          onChange: (value) {
                            setState(() {
                              wyBrightnessThr = value;
                            });
                          },
                        ),
                      ),
                    ],
                  ),
                ),
                SingleChildScrollView(
                  child: Column(
                    children: [
                      TDCell(
                        title: '启用加热器',
                        noteWidget: TDSwitch(
                          isOn: wyEnableHeater,
                          onChanged: (value) {
                            setState(() {
                              wyEnableHeater = value;
                            });
                            return value;
                          },
                        ),
                      ),
                      TDCell(
                        hover: false,
                        title: '环境状态报告间隔(秒)',
                        description: '范围1~3600',
                        noteWidget: TDStepper(
                          size: TDStepperSize.large,
                          theme: TDStepperTheme.outline,
                          value: reportEnvStatusInterval,
                          min: 1,
                          max: 3600,
                          onChange: (value) {
                            setState(() {
                              reportEnvStatusInterval = value;
                            });
                          },
                        ),
                      ),
                      TDCell(
                        hover: false,
                        style: TDCellStyle(
                            padding: const EdgeInsets.fromLTRB(18, 0, 0, 0)),
                        title: '加热器温度范围(°C)',
                        noteWidget: Container(
                          alignment: Alignment.centerRight,
                          width: 180,
                          child: TDRangeSlider(
                            sliderThemeData: TDSliderThemeData(
                              context: context,
                              showThumbValue: true,
                              min: -40,
                              max: 85.0,
                              scaleFormatter: (value) =>
                                  value.round().toString(),
                            ),
                            value: RangeValues(
                                wyHeaterTemperatureRange[0].toDouble(),
                                wyHeaterTemperatureRange[1].toDouble()),
                            onChanged: wyEnableHeater
                                ? (value) {
                                    setState(() {
                                      wyHeaterTemperatureRange = [
                                        value.start.toInt(),
                                        value.end.toInt()
                                      ];
                                    });
                                  }
                                : null,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
          Container(
              alignment: Alignment.center,
              padding: const EdgeInsets.all(16),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  TDButton(
                    text: '取消',
                    theme: TDButtonTheme.light,
                    isBlock: true,
                    onTap: () {
                      Navigator.pop(context);
                    },
                  ),
                  TDButton(
                    text: '确定',
                    theme: TDButtonTheme.primary,
                    isBlock: true,
                    onTap: () {
                      widget.onConfirm({});
                    },
                  )
                ],
              ))
        ],
      ),
    );
  }
}

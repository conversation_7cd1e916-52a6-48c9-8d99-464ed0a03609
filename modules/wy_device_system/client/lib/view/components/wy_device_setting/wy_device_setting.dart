import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../bloc/wy_device_blocs.dart';
import '../../../model/wy_device.dart';
import 'package:input_slider/input_slider.dart';
import 'package:tolyui/basic/basic.dart';
import 'package:tolyui/tolyui.dart';

import '../../cron/cron-component.dart';

class DeviceSettingsPanel extends StatelessWidget {
  final WyDeviceBloc wyDeviceBloc;

  const DeviceSettingsPanel({Key? key, required this.wyDeviceBloc})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return BlocProvider<WyDeviceBloc>.value(
      value: wyDeviceBloc,
      child: const DeviceSettingsContent(),
    );
  }
}

class DeviceSettingsContent extends StatefulWidget {
  const DeviceSettingsContent({Key? key}) : super(key: key);

  @override
  State<DeviceSettingsContent> createState() => _DeviceSettingsContentState();
}

class _DeviceSettingsContentState extends State<DeviceSettingsContent> {
  int _currentIndex = 0;
  final _pageController = PageController();
  bool _isSaving = false;
  bool _isFactoryCalibrationMode = false;

  // 使用防抖计时器来降低保存频率
  bool _debounceActive = false;

  final List<_SettingsCategory> _categories = [
    _SettingsCategory(Icons.info_outline, '基本信息'),
    _SettingsCategory(Icons.monitor_outlined, '测量设置'),
    _SettingsCategory(Icons.light_mode_outlined, '补光灯设置'),
    _SettingsCategory(Icons.camera_alt_outlined, '相机设置'),
    _SettingsCategory(Icons.thermostat_outlined, '环境控制'),
  ];

  @override
  void initState() {
    super.initState();
    final bloc = context.read<WyDeviceBloc>();
    bloc.add(LoadDeviceAttributes());
    setState(() {
      _isFactoryCalibrationMode = bloc.state.isFactoryCalibrationMode ?? false;
    });
  }

  @override
  void dispose() {
    _pageController.dispose();
    if (_isFactoryCalibrationMode) {
      final bloc = context.read<WyDeviceBloc>();
      bloc.add(UpdateFactoryCalibrationMode(false));
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocConsumer<WyDeviceBloc, WyDeviceState>(
      listener: (context, state) {
        if (!state.isLoading && _isSaving) {
          setState(() => _isSaving = false);
          _showSaveStatus(context);
        }
      },
      buildWhen: (previous, current) {
        // 只在关键状态变更时重建UI
        return previous.isLoading != current.isLoading ||
            previous.deviceAttribute != current.deviceAttribute ||
            previous.factoryCalibration != current.factoryCalibration ||
            previous.selectedCameraId != current.selectedCameraId;
      },
      builder: (context, state) {
        if (state.isLoading) {
          return const SizedBox(
            height: 200,
            child: Center(child: CircularProgressIndicator()),
          );
        }

        if (state.deviceAttribute == null) {
          return SizedBox(
            height: 200,
            child: Center(
              child: ElevatedButton.icon(
                onPressed: () =>
                    context.read<WyDeviceBloc>().add(LoadDeviceAttributes()),
                icon: const Icon(Icons.refresh),
                label: const Text('加载设备数据'),
              ),
            ),
          );
        }

        return Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildCategorySelector(),
            const SizedBox(height: 8),
            if (_isSaving) const LinearProgressIndicator(minHeight: 2),
            SizedBox(
              height: 350,
              child: PageView(
                controller: _pageController,
                physics: const BouncingScrollPhysics(),
                onPageChanged: (index) {
                  setState(() => _currentIndex = index);
                },
                children: [
                  _buildBasicInfoSettings(state.deviceAttribute!),
                  _buildMeasurementSettings(state),
                  _buildPWMSettings(state.deviceAttribute!),
                  _buildCameraSettings(state.deviceAttribute!),
                  _buildEnvironmentSettings(state.deviceAttribute!),
                ],
              ),
            ),
          ],
        );
      },
    );
  }

  // 优化的类别选择器，使用SliverAnimatedList以获得更好的滚动性能
  Widget _buildCategorySelector() {
    return Container(
      height: 46,
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surfaceVariant.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
      ),
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: _categories.length,
        padding: const EdgeInsets.symmetric(horizontal: 8),
        itemBuilder: (context, index) {
          final isSelected = _currentIndex == index;
          return Padding(
            padding: const EdgeInsets.symmetric(horizontal: 4),
            child: ChoiceChip(
              label: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(
                    _categories[index].icon,
                    size: 16,
                    color: isSelected
                        ? Theme.of(context).colorScheme.onPrimary
                        : Theme.of(context)
                            .colorScheme
                            .onSurface
                            .withOpacity(0.75),
                  ),
                  const SizedBox(width: 4),
                  Text(_categories[index].title),
                ],
              ),
              selected: isSelected,
              selectedColor: Theme.of(context).colorScheme.primary,
              backgroundColor: Colors.transparent,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(20),
                side: isSelected
                    ? BorderSide.none
                    : BorderSide(
                        color: Theme.of(context)
                            .colorScheme
                            .outline
                            .withOpacity(0.5),
                        width: 1,
                      ),
              ),
              onSelected: (selected) {
                if (selected) {
                  setState(() => _currentIndex = index);
                  _pageController.animateToPage(
                    index,
                    duration: const Duration(milliseconds: 300),
                    curve: Curves.easeInOut,
                  );
                }
              },
            ),
          );
        },
      ),
    );
  }

  // Individual settings pages with similar layout as before
  Widget _buildBasicInfoSettings(WyDeviceAttribute attr) {
    return _SettingsPage(
      title: _categories[0].title,
      icon: _categories[0].icon,
      fields: [
        _buildTextField(
            '设备型号', attr.model, (v) => _updateAttribute('model', v)),
        _buildReadOnlyField('设备ID', attr.wyID),
        _buildReadOnlyField('SIM卡号', attr.ccid),
        _buildReadOnlyField('固件标题', attr.currentFwTitle),
        _buildReadOnlyField('固件版本', attr.currentFwVersion),
        _buildReadOnlyField('MCU固件版本', attr.wyMCUFirmwareV),
        _buildSwitch('指示灯', attr.indicatorLight,
            (v) => _updateAttribute('indicatorLight', v)),
      ],
    );
  }

  Widget _buildCameraSettings(WyDeviceAttribute attr) {
    return _SettingsPage(
      title: _categories[3].title,
      icon: _categories[3].icon,
      fields: [
        _buildRadioGroup(
            '曝光设置',
            attr.wyCameraExposure,
            ['High', 'Middle', 'Low'],
            (v) => _updateAttribute('wyCameraExposure', v)),
        _buildNumericField('算法阈值', attr.wyBrightnessThr.toString(),
            (v) => _updateAttribute('wyBrightnessThr', int.parse(v)),
            min: 0, max: 225, hint: '范围: 0-225', isInteger: true),
      ],
    );
  }

  Widget _buildRadioGroup(String label, String value, List<String> options,
      Function(String) onChanged,
      {bool enabled = true}) {
    return _FormField(
      label: label,
      child: Theme(
        data: ThemeData(
          visualDensity: VisualDensity.compact,
          materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
        ),
        child: Wrap(
          spacing: 16.0,
          runSpacing: 8.0,
          children: options.map((option) {
            return InkWell(
              onTap: enabled
                  ? () {
                      if (option != value) onChanged(option);
                    }
                  : null,
              borderRadius: BorderRadius.circular(8),
              child: Container(
                padding:
                    const EdgeInsets.symmetric(horizontal: 4.0, vertical: 2.0),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Radio<String>(
                      value: option,
                      groupValue: value,
                      onChanged: enabled
                          ? (val) {
                              if (val != null && val != value) onChanged(val);
                            }
                          : null,
                    ),
                    Text(option,
                        style: TextStyle(
                          color: enabled ? null : Colors.grey[600],
                          fontWeight: option == value
                              ? FontWeight.bold
                              : FontWeight.normal,
                        )),
                  ],
                ),
              ),
            );
          }).toList(),
        ),
      ),
    );
  }

  Widget _buildPWMSettings(WyDeviceAttribute attr) {
    return _SettingsPage(
      title: _categories[2].title,
      icon: _categories[2].icon,
      fields: [
        _buildSlider(
          '补光灯亮度',
          attr.pwm.toDouble(),
          0,
          10000,
          (val) => _updateAttribute('pwm', val.round()),
        ),
        _buildSwitch('PWM自适应', attr.pwSelfAdaptive,
            (v) => _updateAttribute('pwSelfAdaptive', v)),
        _buildNumericField(
            '自适应上报间隔 (秒)',
            attr.wyReportSelfAdaptiveInterval.toString(),
            (v) =>
                _updateAttribute('wyReportSelfAdaptiveInterval', int.parse(v))),
        _buildNumericField('最小自适应PWM', attr.minimumAdaptivePwm.toString(),
            (v) => _updateAttribute('minimumAdaptivePwm', int.parse(v)),
            enabled: attr.pwSelfAdaptive, hint: '范围: 0-10000'),
        _buildNumericField('最大自适应PWM', attr.maximumAdaptivePwm.toString(),
            (v) => _updateAttribute('maximumAdaptivePwm', int.parse(v)),
            enabled: attr.pwSelfAdaptive, hint: '范围: 0-10000'),
      ],
    );
  }

  Widget _buildSlider(String label, double value, double min, double max,
      Function(double) onChanged,
      {bool enabled = true}) {
    return _FormField(
      label: label,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          InputSlider(
            onChange: (value) => onChanged(value),
            min: min,
            max: max,
            decimalPlaces: 0,
            defaultValue: value.clamp(min, max),
          ),
        ],
      ),
      fullWidth: true,
    );
  }

  Widget _buildMeasurementSettings(WyDeviceState state) {
    final attr = state.deviceAttribute!;
    final factoryCalibration = state.factoryCalibration;
    final selectedCameraId = state.selectedCameraId;

    return _SettingsPage(
      title: _categories[1].title,
      icon: _categories[1].icon,
      useCompactLayout: true,
      fields: [
        // 采样设置卡片
        _buildSettingsCard(
          title: '采样设置',
          icon: Icons.sensors,
          tooltip: '设置采样频率和稳定性',
          initiallyExpanded: true,
          child: LayoutBuilder(
            builder: (context, constraints) {
              final isNarrow = constraints.maxWidth < 450;
              return isNarrow
                  ? Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        _FormField(
                          label: '采样频率',
                          child: _FrequencyInputField(
                            frequency: attr.wyMaxSampleFreq,
                            onChanged: (value) =>
                                _updateAttribute('wyMaxSampleFreq', value),
                          ),
                          fullWidth: true,
                        ),
                        const SizedBox(height: 12),
                        _buildSwitch('稳定采样', attr.stableSample,
                            (v) => _updateAttribute('stableSample', v)),
                        _buildCronSelectorButton(),
                      ],
                    )
                  : Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Expanded(
                          flex: 3,
                          child: _FormField(
                            label: '采样频率',
                            child: _FrequencyInputField(
                              frequency: attr.wyMaxSampleFreq,
                              onChanged: (value) =>
                                  _updateAttribute('wyMaxSampleFreq', value),
                            ),
                          ),
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          flex: 2,
                          child: _buildSwitch('稳定采样', attr.stableSample,
                              (v) => _updateAttribute('stableSample', v)),
                        ),
                        Expanded(
                          flex: 2,
                          child: _buildCronSelectorButton(),
                        ),
                      ],
                    );
            },
          ),
        ),

        // 滤波设置卡片
        _buildSettingsCard(
          title: '滤波设置',
          icon: Icons.filter_list,
          tooltip: '启用平均滤波处理数据',
          initiallyExpanded: attr.filter.type == 'mean',
          trailing: Switch(
            value: attr.filter.type == 'mean',
            onChanged: (v) => _updatefilter('type', v ? 'mean' : 'none'),
          ),
          child: attr.filter.type == 'mean'
              ? Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Padding(
                      padding: EdgeInsets.only(top: 8, bottom: 8),
                      child: Text('窗口大小设置',
                          style: TextStyle(
                              fontSize: 13, fontWeight: FontWeight.w500)),
                    ),
                    Row(
                      children: [
                        Expanded(
                          child: TextFormField(
                            initialValue: attr.filter.args.minor.toString(),
                            onChanged: (val) {
                              if (val.isNotEmpty) {
                                try {
                                  int value = int.parse(val);
                                  if (value <= attr.filter.args.major) {
                                    _updateFilterArgs('minor', value);
                                  }
                                } catch (_) {}
                              }
                            },
                            keyboardType: TextInputType.number,
                            decoration: InputDecoration(
                              isDense: true,
                              contentPadding: const EdgeInsets.all(12),
                              labelText: '小窗口',
                              helperText: '必须 ≤ 大窗口',
                              helperStyle: TextStyle(
                                  fontSize: 10,
                                  color:
                                      Theme.of(context).colorScheme.secondary),
                              prefixIcon:
                                  const Icon(Icons.filter_1_outlined, size: 18),
                              border: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(8)),
                            ),
                          ),
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: TextFormField(
                            initialValue: attr.filter.args.major.toString(),
                            onChanged: (val) {
                              if (val.isNotEmpty) {
                                try {
                                  int value = int.parse(val);
                                  if (value >= attr.filter.args.minor) {
                                    _updateFilterArgs('major', value);
                                  }
                                } catch (_) {}
                              }
                            },
                            keyboardType: TextInputType.number,
                            decoration: InputDecoration(
                              isDense: true,
                              contentPadding: const EdgeInsets.all(12),
                              labelText: '大窗口',
                              helperText: '必须 ≥ 小窗口',
                              helperStyle: TextStyle(
                                  fontSize: 10,
                                  color:
                                      Theme.of(context).colorScheme.secondary),
                              prefixIcon:
                                  const Icon(Icons.filter_2_outlined, size: 18),
                              border: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(8)),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                )
              : const SizedBox.shrink(),
        ),

        // 数据正负方向设置卡片
        _buildSettingsCard(
          title: '数据正负方向设置',
          icon: Icons.compass_calibration,
          tooltip: '方向设定基于摄像头正对标靶的视角',
          initiallyExpanded: true,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                '以下方向均以摄像头正对标靶的视角作为参考',
                style: TextStyle(fontSize: 12, color: Colors.grey),
              ),
              const SizedBox(height: 12),
              _buildDirectionalSwitch('∑X数据正负方向', attr.sigmaXDirection, '左正右负',
                  '右正左负', (v) => _updateAttribute('sigmaXDirection', v)),
              const SizedBox(height: 8),
              _buildDirectionalSwitch('∑Y数据正负方向', attr.sigmaYDirection, '上正下负',
                  '下正上负', (v) => _updateAttribute('sigmaYDirection', v)),
            ],
          ),
        ),

        // 出厂精度标定卡片
        _buildSettingsCard(
          title: '出厂精度标定',
          icon: Icons.scale,
          tooltip: '设置设备初始参考点和测量距离',
          initiallyExpanded: true,
          titleBuilder: (context, constraints) {
            final isNarrow = constraints.maxWidth < 400;
            return isNarrow
                ? Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Icon(Icons.scale,
                              size: 20,
                              color: Theme.of(context).colorScheme.primary),
                          const SizedBox(width: 8),
                          Expanded(
                            child: Text('出厂精度标定',
                                style: TextStyle(
                                    fontWeight: FontWeight.w500,
                                    color:
                                        Theme.of(context).colorScheme.primary),
                                overflow: TextOverflow.ellipsis),
                          ),
                          Tooltip(
                            message: '设置设备镜头焦距和标定距离',
                            child: const Icon(Icons.help_outline, size: 16),
                          ),
                        ],
                      ),
                      const SizedBox(height: 6),
                      TextButton.icon(
                        onPressed: () {
                          final bloc = context.read<WyDeviceBloc>();
                          final newMode = !_isFactoryCalibrationMode;
                          bloc.add(UpdateFactoryCalibrationMode(newMode));
                          setState(() {
                            _isFactoryCalibrationMode = newMode;
                          });
                        },
                        icon: Icon(
                          _isFactoryCalibrationMode
                              ? Icons.lock_open
                              : Icons.lock_outline,
                          size: 16,
                        ),
                        label: Text(
                          _isFactoryCalibrationMode ? '退出标定模式' : '进入标定模式',
                          style: const TextStyle(fontSize: 12),
                        ),
                        style: TextButton.styleFrom(
                            padding: const EdgeInsets.symmetric(horizontal: 8)),
                      ),
                    ],
                  )
                : Row(
                    children: [
                      Icon(Icons.scale,
                          size: 20,
                          color: Theme.of(context).colorScheme.primary),
                      const SizedBox(width: 8),
                      Text('出厂精度标定',
                          style: TextStyle(
                              fontWeight: FontWeight.w500,
                              color: Theme.of(context).colorScheme.primary)),
                      const SizedBox(width: 8),
                      Tooltip(
                        message: '设置设备初始参考点和测量距离',
                        child: const Icon(Icons.help_outline, size: 16),
                      ),
                      const Spacer(),
                      TextButton.icon(
                        onPressed: () {
                          final bloc = context.read<WyDeviceBloc>();
                          final newMode = !_isFactoryCalibrationMode;
                          bloc.add(UpdateFactoryCalibrationMode(newMode));
                          setState(() {
                            _isFactoryCalibrationMode = newMode;
                          });
                        },
                        icon: Icon(
                          _isFactoryCalibrationMode
                              ? Icons.lock_open
                              : Icons.lock_outline,
                          size: 16,
                        ),
                        label: Text(
                          _isFactoryCalibrationMode ? '退出标定模式' : '进入标定模式',
                          style: const TextStyle(fontSize: 12),
                        ),
                      ),
                    ],
                  );
          },
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                '设备出厂精度标定，请勿随意更改',
                style: TextStyle(fontSize: 12, color: Colors.red),
              ),
              Builder(
                builder: (context) {
                  final factoryCalibration = context.select(
                      (WyDeviceBloc bloc) => bloc.state.factoryCalibration);
                  final calibration = factoryCalibration;
                  return Card(
                    margin: EdgeInsets.zero,
                    elevation: 0,
                    color: Theme.of(context)
                        .colorScheme
                        .surfaceVariant
                        .withOpacity(0.2),
                    shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8)),
                    child: Padding(
                      padding: const EdgeInsets.all(12.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              Icon(
                                calibration.isCalibrated == true
                                    ? Icons.check_circle_outline
                                    : Icons.error_outline,
                                size: 16,
                                color: calibration.isCalibrated == true
                                    ? Colors.green
                                    : Colors.orange,
                              ),
                              const SizedBox(width: 8),
                              Text(
                                '标定状态：${calibration.isCalibrated == true ? "已标定" : "未标定"}',
                                style: const TextStyle(
                                    fontWeight: FontWeight.w500),
                              ),
                            ],
                          ),
                          if (calibration.isCalibrated == true) ...[
                            const SizedBox(height: 4),
                            Text(
                              '标定时间：${DateTime.fromMillisecondsSinceEpoch(calibration.ts).toString().substring(0, 19)}',
                              style: const TextStyle(
                                  fontSize: 12, color: Colors.grey),
                            ),
                          ],
                        ],
                      ),
                    ),
                  );
                },
              ),
              _buildRadioGroup(
                '摄像头',
                selectedCameraId.toString(),
                ['0', '1'],
                (v) => {
                  context
                      .read<WyDeviceBloc>()
                      .add(SelectedCameraId(int.parse(v))),
                },
                enabled: _isFactoryCalibrationMode,
              ),
              const SizedBox(height: 8),
              Builder(
                builder: (context) {
                  final wyDeviceBloc = context.watch<WyDeviceBloc>();
                  final factoryCalibration =
                      wyDeviceBloc.state.factoryCalibration;
                  // 找到当前标靶型号对应的索引
                  int currentIndex = 0;
                  final targetModel = factoryCalibration.targetModel;
                  final targetModels = [
                    'T1-DB3D300S7J',
                    'T1-DB3D400S7J',
                    'T1-DB3D500S7J'
                  ];
                  if (targetModel != null) {
                    for (int i = 0; i < targetModels.length; i++) {
                      if (targetModels[i] == targetModel) {
                        currentIndex = i;
                        break;
                      }
                    }
                  }
                  return _FormField(
                    label: '标靶型号',
                    child: TolySelect(
                      data: targetModels,
                      selectIndex: currentIndex,
                      onSelected: (int value) {
                        if (!_isFactoryCalibrationMode) return;
                        final String targetModel = targetModels[value];
                        var newFactoryCalibration = factoryCalibration.copyWith(
                          targetModel: targetModel,
                        );
                        context.read<WyDeviceBloc>().add(
                              UpdateFactoryCalibration(newFactoryCalibration),
                            );
                        // 强制重建UI
                        setState(() {});
                      },
                    ),
                  );
                },
              ),
              const SizedBox(height: 8),
              _buildNumericField(
                  '标定距离',
                  factoryCalibration.actualMeasureDistance.toString(),
                  (v) => {
                        context.read<WyDeviceBloc>().add(
                              UpdateFactoryCalibration(
                                factoryCalibration.copyWith(
                                    actualMeasureDistance: double.parse(v)),
                              ),
                            ),
                      },
                  min: 0.01,
                  max: 1500,
                  hint: '设备到标定标靶的距离',
                  isInteger: false,
                  enabled: _isFactoryCalibrationMode,
                  suffix: 'm'),
              const SizedBox(height: 8),
              _buildNumericField(
                  '镜头焦距',
                  factoryCalibration.focalLength.toString(),
                  (v) => {
                        context.read<WyDeviceBloc>().add(
                              UpdateFactoryCalibration(
                                factoryCalibration.copyWith(
                                    focalLength: int.parse(v)),
                              ),
                            ),
                      },
                  isInteger: true,
                  enabled: _isFactoryCalibrationMode,
                  min: 1,
                  max: 1000,
                  suffix: 'mm'),
              const SizedBox(height: 16),
              if (_isFactoryCalibrationMode)
                Align(
                  alignment: Alignment.center,
                  child: ElevatedButton.icon(
                    onPressed: () {
                      showDialog(
                        context: context,
                        builder: (dialogContext) => AlertDialog(
                          title: const Text('开始标定'),
                          content:
                              const Text('确定要开始设备标定吗？此过程会影响测量精度，标定过程中请勿移动设备。'),
                          actions: [
                            TextButton(
                              onPressed: () => Navigator.pop(dialogContext),
                              child: const Text('取消'),
                            ),
                            FilledButton(
                              onPressed: () {
                                Navigator.pop(dialogContext);
                                try {
                                  final bloc = context.read<WyDeviceBloc>();
                                  bloc.add(StartFactoryCalibration());

                                  ScaffoldMessenger.of(context).showSnackBar(
                                    const SnackBar(
                                      content: Row(
                                        children: [
                                          SizedBox(
                                            width: 16,
                                            height: 16,
                                            child: CircularProgressIndicator(
                                                strokeWidth: 2),
                                          ),
                                          SizedBox(width: 12),
                                          Text('设备标定中，请稍候...'),
                                        ],
                                      ),
                                      duration: Duration(seconds: 10),
                                    ),
                                  );
                                } catch (e) {
                                  // 显示错误信息
                                  ScaffoldMessenger.of(context).showSnackBar(
                                    SnackBar(
                                      content: Text('标定失败: ${e.toString()}'),
                                      backgroundColor: Colors.red,
                                    ),
                                  );
                                }
                              },
                              child: const Text('标定'),
                            ),
                          ],
                        ),
                      );
                    },
                    icon: const Icon(Icons.play_arrow),
                    label: const Text('开始标定'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Theme.of(context).colorScheme.primary,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildCronSelectorButton() {
    return ElevatedButton.icon(
      icon: const Icon(Icons.schedule, size: 18),
      label: const Text('定时测量'),
      style: ElevatedButton.styleFrom(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 0),
        minimumSize: const Size(0, 36),
        tapTargetSize: MaterialTapTargetSize.shrinkWrap,
      ),
      onPressed: () {
        showDialog(
          context: context,
          builder: (BuildContext context) {
            // 获取屏幕尺寸
            final size = MediaQuery.of(context).size;
            final isSmallScreen = size.width < 600;

            return Dialog(
              insetPadding: EdgeInsets.symmetric(
                horizontal: isSmallScreen ? 16 : 32,
                vertical: 24,
              ),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(16),
              ),
              child: ConstrainedBox(
                constraints: BoxConstraints(
                  maxWidth: 500,
                  maxHeight: isSmallScreen ? size.height * 0.8 : 500,
                ),
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    crossAxisAlignment: CrossAxisAlignment.stretch,
                    children: [
                      Row(
                        children: [
                          const Text(
                            '设置定时测量',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const Spacer(),
                          if (isSmallScreen)
                            IconButton(
                              icon: const Icon(Icons.close),
                              onPressed: () => Navigator.pop(context),
                              padding: EdgeInsets.zero,
                              constraints: const BoxConstraints(),
                            ),
                        ],
                      ),
                      const SizedBox(height: 16),
                      Expanded(
                        child: ClipRRect(
                          borderRadius: BorderRadius.circular(8),
                          child: SingleChildScrollView(
                            child: DailyCronBuilder(
                              primaryColor:
                                  Theme.of(context).colorScheme.primary,
                              backgroundColor: Theme.of(context)
                                  .colorScheme
                                  .surfaceVariant
                                  .withOpacity(0.2),
                              showCopyButton: false,
                              showResetButton: true,
                              showGenerateButton: false,
                              onCronExpressionChanged:
                                  (cronExpression, description) {
                                debugPrint('Cron expression: $cronExpression');
                              },
                            ),
                          ),
                        ),
                      ),
                      const SizedBox(height: 16),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.end,
                        children: [
                          TextButton(
                            onPressed: () => Navigator.pop(context),
                            child: const Text('取消'),
                          ),
                          const SizedBox(width: 8),
                          FilledButton(
                            onPressed: () {
                              Navigator.pop(context);
                            },
                            child: const Text('确定'),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
            );
          },
        );
      },
    );
  }

  Widget _buildDirectionalSwitch(String label, bool value, String leftLabel,
      String rightLabel, Function(bool) onChanged,
      {bool enabled = true}) {
    return _FormField(
      label: label,
      child: LayoutBuilder(
        builder: (context, constraints) {
          final isNarrow = constraints.maxWidth < 180;
          return isNarrow
              ? Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(value ? leftLabel : rightLabel,
                            style: TextStyle(
                              fontSize: 12,
                              color: Theme.of(context).primaryColor,
                              fontWeight: FontWeight.bold,
                            )),
                        Switch(
                          value: value,
                          onChanged: enabled
                              ? (val) {
                                  if (val != value) onChanged(val);
                                }
                              : null,
                        ),
                      ],
                    ),
                    Text('当前: ${value ? leftLabel : rightLabel}',
                        style:
                            const TextStyle(fontSize: 10, color: Colors.grey)),
                  ],
                )
              : Row(
                  children: [
                    Text(leftLabel,
                        style: TextStyle(
                          fontSize: 12,
                          color: value
                              ? Theme.of(context).primaryColor
                              : Colors.grey,
                          fontWeight:
                              value ? FontWeight.bold : FontWeight.normal,
                        )),
                    Switch(
                      value: value,
                      onChanged: enabled
                          ? (val) {
                              if (val != value) onChanged(val);
                            }
                          : null,
                    ),
                    Flexible(
                      child: Text(
                        rightLabel,
                        style: TextStyle(
                          fontSize: 12,
                          color: !value
                              ? Theme.of(context).primaryColor
                              : Colors.grey,
                          fontWeight:
                              !value ? FontWeight.bold : FontWeight.normal,
                        ),
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ],
                );
        },
      ),
    );
  }

  Widget _buildEnvironmentSettings(WyDeviceAttribute attr) {
    return _SettingsPage(
      title: _categories[4].title,
      icon: _categories[4].icon,
      fields: [
        _buildSwitch('启用加热器', attr.wyEnableHeater,
            (v) => _updateAttribute('wyEnableHeater', v)),
        _buildNumericField(
            '环境状态报告间隔 (秒)',
            attr.reportEnvStatusInterval.toString(),
            (v) => _updateAttribute('reportEnvStatusInterval', int.parse(v)),
            hint: '范围: 1-3600'),
        _buildRangeSlider(
          '加热器温度范围 (°C)',
          attr.wyHeaterTemperatureRange[0].toDouble(),
          attr.wyHeaterTemperatureRange[1].toDouble(),
          -40,
          85,
          (min, max) => _updateAttribute(
              'wyHeaterTemperatureRange', [min.round(), max.round()]),
          enabled: attr.wyEnableHeater,
        ),
      ],
    );
  }

  // 创建可复用的设置卡片组件
  Widget _buildSettingsCard({
    required String title,
    required IconData icon,
    required Widget child,
    String? tooltip,
    bool initiallyExpanded = false,
    Widget? trailing,
    Widget Function(BuildContext, BoxConstraints)? titleBuilder,
  }) {
    return Card(
      margin: const EdgeInsets.symmetric(vertical: 6),
      elevation: 1,
      clipBehavior: Clip.antiAlias,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: ExpansionTile(
        initiallyExpanded: initiallyExpanded,
        tilePadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 0),
        childrenPadding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
        title: titleBuilder != null
            ? LayoutBuilder(builder: titleBuilder)
            : Row(
                children: [
                  Icon(icon,
                      size: 20, color: Theme.of(context).colorScheme.primary),
                  const SizedBox(width: 8),
                  Text(title,
                      style: TextStyle(
                          fontWeight: FontWeight.w500,
                          color: Theme.of(context).colorScheme.primary)),
                  if (tooltip != null) ...[
                    const SizedBox(width: 8),
                    Tooltip(
                      message: tooltip,
                      child: const Icon(Icons.help_outline, size: 16),
                    ),
                  ],
                  if (trailing != null) ...[
                    const Spacer(),
                    trailing,
                  ],
                ],
              ),
        children: [child],
      ),
    );
  }

  // Helper form widgets with improved layout
  Widget _buildTextField(String label, String value, Function(String) onChanged,
      {bool enabled = true, bool fullWidth = false}) {
    return _FormField(
      label: label,
      child: TextFormField(
        initialValue: value,
        onChanged: (val) {
          if (val != value) onChanged(val);
        },
        enabled: enabled,
        decoration: InputDecoration(
          isDense: true,
          contentPadding:
              const EdgeInsets.symmetric(horizontal: 12, vertical: 12),
          border: OutlineInputBorder(borderRadius: BorderRadius.circular(8)),
        ),
      ),
      fullWidth: fullWidth,
    );
  }

  Widget _buildReadOnlyField(String label, String value) {
    return _FormField(
      label: label,
      child: TextFormField(
        initialValue: value,
        enabled: false,
        decoration: InputDecoration(
          isDense: true,
          contentPadding:
              const EdgeInsets.symmetric(horizontal: 12, vertical: 12),
          border: OutlineInputBorder(borderRadius: BorderRadius.circular(8)),
          filled: true,
          fillColor: Colors.grey[100],
        ),
      ),
    );
  }

  Widget _buildNumericField(
      String label, String value, Function(String) onChanged,
      {bool enabled = true,
      String? hint,
      String? Function(String?)? validators,
      double? min,
      double? max,
      bool isInteger = true,
      String? suffix}) {
    return _FormField(
      label: label,
      child: TextFormField(
        initialValue: value,
        onChanged: (val) {
          if (val.isNotEmpty && val != value) {
            try {
              final parsedValue =
                  isInteger ? int.parse(val) : double.parse(val);

              if ((min != null && parsedValue < min) ||
                  (max != null && parsedValue > max)) {
                return;
              }

              if (validators != null) {
                String? error = validators(val);
                if (error == null) {
                  onChanged(val);
                }
              } else {
                onChanged(val);
              }
            } catch (e) {
              // 无效数字格式 - 静默忽略
            }
          }
        },
        validator: (val) {
          if (val == null || val.isEmpty) {
            return '此项不能为空';
          }

          try {
            final parsedValue = isInteger ? int.parse(val) : double.parse(val);

            if (min != null && parsedValue < min) {
              return '不能小于 $min';
            }
            if (max != null && parsedValue > max) {
              return '不能大于 $max';
            }

            // 应用自定义验证（如果提供）
            if (validators != null) {
              return validators(val);
            }
          } catch (e) {
            return '请输入有效的${isInteger ? '整数' : '数字'}';
          }

          return null;
        },
        enabled: enabled,
        keyboardType: TextInputType.numberWithOptions(decimal: !isInteger),
        autovalidateMode: AutovalidateMode.onUserInteraction,
        decoration: InputDecoration(
          isDense: true,
          contentPadding:
              const EdgeInsets.symmetric(horizontal: 12, vertical: 12),
          border: OutlineInputBorder(borderRadius: BorderRadius.circular(8)),
          helperText: hint,
          helperStyle: const TextStyle(fontSize: 10),
          errorMaxLines: 2,
          suffixText: suffix,
        ),
      ),
    );
  }

  Widget _buildSwitch(String label, bool value, Function(bool) onChanged,
      {bool enabled = true}) {
    return _FormField(
      label: label,
      child: Switch(
        value: value,
        onChanged: enabled
            ? (val) {
                if (val != value) onChanged(val);
              }
            : null,
      ),
    );
  }

  Widget _buildRangeSlider(String label, double start, double end,
      double min,
      double max, Function(double, double) onChanged,
      {bool enabled = true}) {
    return _FormField(
      label: label,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          RangeSlider(
            values: RangeValues(start, end),
            min: min,
            max: max,
            divisions: (max - min).round(),
            labels:
                RangeLabels(start.round().toString(), end.round().toString()),
            onChanged: enabled
                ? (values) {
                    if (values.start != start || values.end != end) {
                      onChanged(values.start, values.end);
                    }
                  }
                : null,
            activeColor: Theme.of(context).colorScheme.primary,
            inactiveColor:
                Theme.of(context).colorScheme.primary.withOpacity(0.2),
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text('$min',
                  style: const TextStyle(fontSize: 12, color: Colors.grey)),
              Text('$max',
                  style: const TextStyle(fontSize: 12, color: Colors.grey)),
            ],
          ),
        ],
      ),
      fullWidth: true,
    );
  }

  // 更新与保存方法 - 使用防抖优化
  void _updateAttribute(String name, dynamic value) {
    final bloc = context.read<WyDeviceBloc>();
    bloc.add(UpdateSingleAttribute(name, value));
    _autoSave();
  }

  void _updatefilter(String field, String value) {
    final state = context.read<WyDeviceBloc>().state;
    if (state.deviceAttribute == null) return;

    final type = state.deviceAttribute!.filter;
    final newType = Filter(
      type: field == 'type' ? value : type.type,
      args: type.args,
    );

    context
        .read<WyDeviceBloc>()
        .add(UpdateSingleAttribute('filter', newType));
    _autoSave();
  }

  void _updateFilterArgs(String field, int value) {
    final state = context.read<WyDeviceBloc>().state;
    if (state.deviceAttribute == null) return;

    final type = state.deviceAttribute!.filter;
    final newArgs = Args(
      major: field == 'major' ? value : type.args.major,
      minor: field == 'minor' ? value : type.args.minor,
    );

    final newType = Filter(type: type.type, args: newArgs);
    context
        .read<WyDeviceBloc>()
        .add(UpdateSingleAttribute('filter', newType));
    _autoSave();
  }

  // 优化的自动保存实现，使用防抖
  void _autoSave() {
    if (!_isSaving && !_debounceActive) {
      setState(() {
        _debounceActive = true; // 开始防抖
      });

      // 添加防抖延迟，避免频繁保存
      Future.delayed(const Duration(milliseconds: 500), () {
        if (mounted) {
          setState(() {
            _isSaving = true;
            _debounceActive = false; // 结束防抖
          });

          final deviceAttribute =
              context.read<WyDeviceBloc>().state.deviceAttribute;
          if (deviceAttribute != null) {
            context
                .read<WyDeviceBloc>()
                .add(UpdateDeviceAttributes(deviceAttribute));
          }
        }
      });
    }
  }

  void _showSaveStatus(BuildContext context) {
    ScaffoldMessenger.of(context).removeCurrentSnackBar();
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(Icons.check_circle_outline,
                color: Theme.of(context).colorScheme.onInverseSurface),
            const SizedBox(width: 8),
            const Text('设置已自动保存'),
          ],
        ),
        duration: const Duration(seconds: 1),
        behavior: SnackBarBehavior.floating,
        width: 200,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(24),
        ),
      ),
    );
  }
}

// 辅助类 - 改进的响应式布局
class _SettingsCategory {
  final IconData icon;
  final String title;

  _SettingsCategory(this.icon, this.title);
}

class _SettingsPage extends StatelessWidget {
  final String title;
  final IconData icon;
  final List<Widget> fields;
  final List<Widget>? actions;
  final bool useCompactLayout;

  const _SettingsPage({
    required this.title,
    required this.icon,
    required this.fields,
    this.actions,
    this.useCompactLayout = false,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (!useCompactLayout) ...[
          Row(
            children: [
              Icon(icon, color: Theme.of(context).primaryColor),
              const SizedBox(width: 8),
              Text(title,
                  style: const TextStyle(
                      fontSize: 16, fontWeight: FontWeight.bold)),
            ],
          ),
          const Divider(),
        ],
        Expanded(
          child: SingleChildScrollView(
            child: Padding(
              padding: EdgeInsets.only(
                bottom: 16,
                top: useCompactLayout ? 0 : 8,
              ),
              child: Column(
                children: fields,
              ),
            ),
          ),
        ),
        if (actions != null) ...[
          const Divider(),
          Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: actions!,
          ),
        ],
      ],
    );
  }
}

class _FormField extends StatelessWidget {
  final String label;
  final Widget child;
  final bool fullWidth;

  const _FormField({
    required this.label,
    required this.child,
    this.fullWidth = false,
  });

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final isWide = screenWidth > 600;
    final isVeryNarrow = screenWidth < 400;

    // 非常窄的屏幕使用列布局
    if (isVeryNarrow) {
      return Container(
        margin: const EdgeInsets.symmetric(vertical: 8.0),
        width: double.infinity,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(label,
                style:
                    const TextStyle(fontSize: 14, fontWeight: FontWeight.w500)),
            const SizedBox(height: 6),
            child,
          ],
        ),
      );
    }

    // 较宽的屏幕使用行布局
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 6.0),
      width: fullWidth ? double.infinity : (isWide ? null : double.infinity),
      constraints: BoxConstraints(
        maxWidth: fullWidth ? double.infinity : (isWide ? 500 : 300),
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          SizedBox(
            width: isWide ? 130 : 110,
            child: Text(
              label,
              style: const TextStyle(fontSize: 14, fontWeight: FontWeight.w500),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(child: child),
        ],
      ),
    );
  }
}

class _FrequencyInputField extends StatefulWidget {
  final double frequency;
  final Function(double) onChanged;

  const _FrequencyInputField({
    required this.frequency,
    required this.onChanged,
  });

  @override
  State<_FrequencyInputField> createState() => _FrequencyInputFieldState();
}

class _FrequencyInputFieldState extends State<_FrequencyInputField> {
  bool _isHzMode = true;
  late TextEditingController _controller;

  @override
  void initState() {
    super.initState();
    _updateControllerValue();
  }

  @override
  void didUpdateWidget(covariant _FrequencyInputField oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.frequency != widget.frequency) {
      _updateControllerValue();
    }
  }

  void _updateControllerValue() {
    final value = _isHzMode
        ? widget.frequency.toString()
        : widget.frequency > 0
            ? (1 / widget.frequency).toStringAsFixed(2)
            : '';
    _controller = TextEditingController(text: value);
  }

  void _updateFrequencyValue(String val) {
    if (val.isNotEmpty) {
      try {
        final inputValue = double.parse(val);
        if (_isHzMode) {
          if (inputValue >= 0.000001 && inputValue <= 60.0) {
            widget.onChanged(inputValue);
          }
        } else {
          if (inputValue >= 1 / 60.0 && inputValue <= 86400.0) {
            final hzValue = 1 / inputValue;
            widget.onChanged(hzValue);
          }
        }
      } catch (_) {}
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Expanded(
          child: TextFormField(
            controller: _controller,
            keyboardType: const TextInputType.numberWithOptions(decimal: true),
            // 仅在提交或失去焦点时更新，而不是在每次按键时
            onChanged: null,
            onEditingComplete: () {
              _updateFrequencyValue(_controller.text);
              FocusScope.of(context).unfocus();
            },
            onFieldSubmitted: _updateFrequencyValue,
            onTapOutside: (_) {
              _updateFrequencyValue(_controller.text);
              FocusScope.of(context).unfocus();
            },
            decoration: InputDecoration(
              isDense: true,
              contentPadding: const EdgeInsets.all(12),
              suffixText: _isHzMode ? 'Hz' : '秒',
              helperText: _isHzMode ? '范围: 0.000001-60.0' : '范围: 0.017-86400.0',
              helperStyle: TextStyle(
                  fontSize: 10, color: Theme.of(context).colorScheme.secondary),
              border:
                  OutlineInputBorder(borderRadius: BorderRadius.circular(8)),
            ),
          ),
        ),
        IconButton(
          icon: const Icon(Icons.swap_horiz),
          tooltip: '切换单位',
          onPressed: () {
            setState(() {
              _isHzMode = !_isHzMode;
              _updateControllerValue();
            });
          },
        ),
      ],
    );
  }
}

import 'package:client/view/components/targets/target_list_view_model.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../bloc/targets_blocs.dart';
import '../../../bloc/wy_device_blocs.dart';
import '../../../model/target.dart';
import '../../../repository/target_repository.dart';
import 'package:provider/provider.dart';
import 'dart:math' as math;
class ROITargetList extends StatefulWidget {
  final TargetRepository repository;
  final TargetBloc? roiBloc; // Make it nullable to support both scenarios
  final WyDeviceBloc? wyDeviceBloc; // 添加WyDeviceBloc参数

  const ROITargetList({
    Key? key,
    required this.repository,
    required this.roiBloc,
    required this.wyDeviceBloc,

  }) : super(key: key);

  @override
  State<ROITargetList> createState() => _ROITargetListState();
}

class _ROITargetListState extends State<ROITargetList> {
  late final TargetBloc _targetBloc;
  late final TargetListViewModel _viewModel;

  @override
  void initState() {
    super.initState();

    _targetBloc = widget.roiBloc ?? TargetBloc(widget.repository);

    // _roiBloc = TargetBloc(widget.repository);
    _viewModel = TargetListViewModel(
      bloc: _targetBloc,
      repository: widget.repository,
      wyDeviceBloc: widget.wyDeviceBloc,
    );
    // _viewModel.loadTargets();
  }

  @override
  void dispose() {
    if (widget.roiBloc == null) {
      _targetBloc.close();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        BlocProvider<TargetBloc>.value(value: _targetBloc),
        Provider<TargetListViewModel>.value(value: _viewModel),
      ],
      child: BlocBuilder<TargetBloc, TargetState>(
        builder: (context, state) {
          if (state.isLoading) {
            return const Center(child: CircularProgressIndicator());
          }
          return _ROITargetListView(viewModel: _viewModel);
        },
      ),
    );
  }
}
class _ROITargetListView extends StatefulWidget {
  final TargetListViewModel viewModel;
  const _ROITargetListView({required this.viewModel});

  @override
  State<_ROITargetListView> createState() => _ROITargetListViewState();
}

class _ROITargetListViewState extends State<_ROITargetListView> {
  final ScrollController _scrollController = ScrollController();

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<TargetBloc, TargetState>(
      builder: (context, state) {
        return Container(
          constraints: const BoxConstraints(maxHeight: 500),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(16),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.08),
                blurRadius: 16,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            mainAxisSize: MainAxisSize.min,
            children: [
              _buildHeader(state),
              if (state.isLoading)
                const LinearProgressIndicator()
              else
                Flexible(
                  child: ConstrainedBox(
                    constraints: BoxConstraints(
                      maxHeight: MediaQuery.of(context).size.height * 0.6,
                      minHeight: 100,
                    ),
                    child: _buildList(context, state),
                  ),
                ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildHeader(TargetState state) {
    return Container(
      height: 56,
      padding: const EdgeInsets.symmetric(horizontal: 16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: const BorderRadius.vertical(top: Radius.circular(16)),
        border: Border(
          bottom: BorderSide(color: Colors.grey.shade100),
        ),
      ),
      child: Row(
        children: [
          Text(
            '标靶列表',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: Colors.grey.shade900,
            ),
          ),
          const SizedBox(width: 8),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
            decoration: BoxDecoration(
              color: Colors.grey.shade100,
              borderRadius: BorderRadius.circular(12),
            ),
            child: Text(
              '${state.targets.length}',
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey.shade700,
              ),
            ),
          ),
          const Spacer(),
          IconButton(
            icon: const Icon(Icons.psychology, size: 20),
            onPressed: () => showBatchInitializeDialog(context,
                state.targets,
                widget.viewModel.initializeTargets),
            tooltip: '批量初始化标靶',
            constraints: const BoxConstraints(
              minWidth: 40,
              minHeight: 40,
            ),
          ),
          const Spacer(),
          IconButton(
            icon: const Icon(Icons.bug_report),
            tooltip: '批量调试',
            onPressed: () => showBatchDebugDialog(
              context,
              state.targets,
              widget.viewModel.debugTargets,
            ),
          ),
          const Spacer(),
          IconButton(
            icon: const Icon(Icons.add_rounded, size: 20),
            onPressed: () {
              widget.viewModel.createTarget();
              // Wait for the widget to rebuild before scrolling
              WidgetsBinding.instance.addPostFrameCallback((_) {
                if (_scrollController.hasClients) {
                  _scrollController.animateTo(
                    _scrollController.position.maxScrollExtent,
                    duration: const Duration(milliseconds: 300),
                    curve: Curves.easeOut,
                  );
                }
              });
            },
            tooltip: '新增标靶',
            constraints: const BoxConstraints(
              minWidth: 40,
              minHeight: 40,
            ),
          ),
          IconButton(
            icon: const Icon(Icons.refresh_rounded, size: 20),
            onPressed: () => widget.viewModel.loadTargets(),
            tooltip: '刷新列表',
            constraints: const BoxConstraints(
              minWidth: 40,
              minHeight: 40,
            ),
          ),
        ],
      ),
    );
  }

  void showBatchInitializeDialog(
      BuildContext context,
      List<Target> targets,
      Function(List<String>) onInitialize,
      ) {
    showDialog(
      context: context,
      builder: (dialogContext) => BlocProvider<TargetBloc>.value(
        value: BlocProvider.of<TargetBloc>(context), // 从父级上下文获取TargetBloc
        child: _BatchInitializeDialog(
          targets: targets,
          onInitialize: onInitialize,
        ),
      ),
    );
  }

  void showBatchDebugDialog(
      BuildContext context,
      List<Target> targets,
      Function(List<String>) onDebug,
      ) {
    showDialog(
      context: context,
      builder: (_) => _BatchDebugDialog(
        targets: targets,
        onDebug: onDebug,
      ),
    );
  }

  Widget _buildList(BuildContext context, TargetState state) {
    if (state.targets.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.radar, size: 48, color: Colors.grey.shade200),
            const SizedBox(height: 16),
            Text(
              '暂无标靶数据',
              style: TextStyle(color: Colors.grey.shade500),
            ),
          ],
        ),
      );
    }


    return ListView.separated(
    controller: _scrollController,

    padding: const EdgeInsets.symmetric(vertical: 8),
      itemCount: state.targets.length,
      separatorBuilder: (_, __) => const SizedBox(height: 8),
      itemBuilder: (context, index) {
        final target = state.targets[index];
        final targetViewModel = widget.viewModel.convertToViewModel(
          target,
          target.targetId == state.selectedTargetId,
        );
        return TargetListItem(
          viewModel: targetViewModel,
          onTap: () => widget.viewModel.selectTarget(target.targetId),
          onNameChanged: (name) => widget.viewModel.updateTarget(
            target.copyWith(name: name),
          ),
          onDistanceChanged: (distance) => widget.viewModel.updateTarget(
            target.copyWith(actualMeasureDistance: distance),
          ),
          onHeightChanged: (height) => widget.viewModel.updateTarget(
            target.copyWith(heightDiff: height),
          ),
          onBasePointChanged: (isBase) => widget.viewModel.updateTarget(
            target.copyWith(basePoint: isBase),
          ),
          onSkipMeasurementChanged: (skip) => widget.viewModel.updateTarget(
            target.copyWith(skipMeasurement: skip),
          ),
          onModelChanged: (model) => widget.viewModel.updateTarget(
            target.copyWith(targetModel: model),
          ),
          onSelfAdaptionChanged: (selfAdaption) => widget.viewModel.updateTarget(
            target.copyWith(selfAdaption: selfAdaption),
          ),
        );
      },
    );
  }
}

class TargetListItem extends StatelessWidget {
  final TargetViewModel viewModel;
  final VoidCallback onTap;
  final ValueChanged<String> onNameChanged;
  final ValueChanged<double?> onDistanceChanged;
  final ValueChanged<double?> onHeightChanged;
  final ValueChanged<bool> onBasePointChanged;
  final ValueChanged<bool> onSkipMeasurementChanged;
  final ValueChanged<String> onModelChanged;
  final ValueChanged<bool> onSelfAdaptionChanged;

  const TargetListItem({
    Key? key,
    required this.viewModel,
    required this.onTap,
    required this.onNameChanged,
    required this.onDistanceChanged,
    required this.onHeightChanged,
    required this.onBasePointChanged,
    required this.onSkipMeasurementChanged,
    required this.onModelChanged,
    required this.onSelfAdaptionChanged,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {

    final targetBloc = context.watch<TargetBloc>();
    final bool isInitializing = targetBloc.state.initializingTargets[viewModel.targetId] ?? false;
    final bool isInitialized = targetBloc.state.targets
        .firstWhere((t) => t.targetId == viewModel.targetId, orElse: () => Target(targetId: '', name: '', rect: Rect.zero))
        .isInitialized;

    debugPrint('TargetListItem: ${viewModel.targetId}, isInitializing: $isInitializing, isInitialized: $isInitialized');

    return AnimatedContainer(
      duration: const Duration(milliseconds: 200),
      margin: const EdgeInsets.symmetric(horizontal: 12),
      decoration: BoxDecoration(
        color: viewModel.isSelected ? Colors.blue.shade50 : Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: viewModel.isSelected
              ? Colors.blue.shade200
              : Colors.grey.shade200,
          width: 1.5,
        ),
      ),
      child: Material(
        color: Colors.transparent,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Row(
              children: [
                if (viewModel.model?.isNotEmpty ?? false)
                  Padding(
                    padding: const EdgeInsets.only(left: 16, top: 8),
                    child: _buildMetricChip(
                      icon: Icons.model_training,
                      label: viewModel.model!,
                      color: Colors.purple,
                    ),
                  ),
                if (viewModel.selfAdaption ?? false)
                  Padding(
                    padding: const EdgeInsets.only(left: 8, top: 8),
                    child: _buildMetricChip(
                      icon: Icons.auto_fix_high,
                      label: '自适应',
                      color: Colors.teal,
                    ),
                  ),
                Container(
                  padding: const EdgeInsets.symmetric(vertical: 4, horizontal: 8),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      if (isInitializing)
                        const Row(
                          children: [
                            SizedBox(
                              width: 16,
                              height: 16,
                              child: CircularProgressIndicator(strokeWidth: 2),
                            ),
                            SizedBox(width: 8),
                            Text('正在初始化...'),
                          ],
                        )
                      else if (isInitialized)
                        const Row(
                          children: [
                            Icon(Icons.check_circle, color: Colors.green, size: 16),
                            SizedBox(width: 8),
                            Text('已初始化'),
                          ],
                        )
                      else
                        TextButton.icon(
                          icon: const Icon(Icons.psychology, size: 16),
                          label: const Text('初始化标靶'),
                          onPressed: () => context.read<TargetListViewModel>().initializeTarget(viewModel.targetId),
                        )
                    ],
                  ),
                ),
              ],
            ),
            _buildInfoSection(),
            _buildActionSection(context),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoSection() {
    return InkWell(
      onTap: onTap,
      borderRadius: const BorderRadius.vertical(top: Radius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            _buildTargetStatus(),
            const SizedBox(width: 16),
            Expanded(child: _buildTargetInfo()),
          ],
        ),
      ),
    );
  }

  Widget _buildTargetStatus() {
    return Column(
      children: [
        _buildBasePointIndicator(),
        if (viewModel.showMeasurements) ...[
          const SizedBox(height: 8),
          _buildMeasurementIndicator(),
        ],
      ],
    );
  }

  Widget _buildBasePointIndicator() {
    return InkWell(
      onTap: () => onBasePointChanged(!viewModel.isBasePoint),
      borderRadius: BorderRadius.circular(8),
      child: Container(
        padding: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          color:
              viewModel.isBasePoint ? Colors.blue.shade50 : Colors.grey.shade50,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: viewModel.isBasePoint
                ? Colors.blue.shade200
                : Colors.grey.shade200,
          ),
        ),
        child: Icon(
          viewModel.isBasePoint
              ? Icons.radio_button_checked
              : Icons.radio_button_unchecked,
          color: viewModel.isBasePoint ? Colors.blue : Colors.grey.shade600,
          size: 20,
        ),
      ),
    );
  }

  Widget _buildMeasurementIndicator() {
    return InkWell(
      onTap: () => onSkipMeasurementChanged(!viewModel.skipMeasurement),
      borderRadius: BorderRadius.circular(8),
      child: Container(
        padding: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: viewModel.isMeasurable
              ? Colors.green.shade50
              : Colors.grey.shade50,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: viewModel.isMeasurable
                ? Colors.green.shade200
                : Colors.grey.shade200,
          ),
        ),
        child: Icon(
          viewModel.isMeasurable ? Icons.visibility : Icons.visibility_off,
          color: viewModel.isMeasurable
              ? Colors.green.shade600
              : Colors.grey.shade600,
          size: 20,
        ),
      ),
    );
  }

  Widget _buildTargetInfo() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          viewModel.displayName,
          style: TextStyle(
            fontSize: 16,
            fontWeight:
                viewModel.isSelected ? FontWeight.w600 : FontWeight.normal,
            letterSpacing: 0.3,
            color: Colors.grey.shade900,
          ),
        ),
        if (viewModel.showMeasurements) ...[
          const SizedBox(height: 8),
          _buildMeasurements(),
        ],
      ],
    );
  }

  Widget _buildMeasurements() {
    return Wrap(
      spacing: 12,
      runSpacing: 8,
      children: [
        if (viewModel.distanceText.isNotEmpty)
          _buildMetricChip(
            icon: Icons.straighten,
            label: viewModel.distanceText,
            color: viewModel.isBasePoint ? Colors.blue : Colors.grey.shade700,
          ),
        if (viewModel.heightDiff != null)
          _buildMetricChip(
            icon: Icons.height,
            label: viewModel.heightText,
            color:
                viewModel.skipMeasurement ? Colors.grey.shade400 : Colors.green,
          ),
      ],
    );
  }

  Widget _buildMetricChip({
    required IconData icon,
    required String label,
    required Color color,
  }) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(6),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 14, color: color),
          const SizedBox(width: 4),
          Text(
            label,
            style: TextStyle(
              fontSize: 12,
              color: color,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionSection(BuildContext context) {
    if (!viewModel.isSelected) return const SizedBox.shrink();

    return Container(
      decoration: BoxDecoration(
        border: Border(
          top: BorderSide(color: Colors.grey.shade200),
        ),
      ),
      child: Row(
        children: [
          _buildActionButton(
            icon: Icons.delete_outline,
            label: '删除',
            onPressed: () => _showDeleteConfirmation(context),
            color: Colors.red.shade700,
          ),

          _buildActionButton(
            icon: Icons.edit_attributes,
            label: '设置',
            onPressed: () => _showEditPanel(context),
          ),

        ],
      ),
    );
  }


  void _showDeleteConfirmation(BuildContext context) {
    final vm = context.read<TargetListViewModel>();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('删除标靶'),
        content: Text('确定要删除标靶 ${viewModel.displayName} 吗？'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('取消'),
          ),
          TextButton(
            style: TextButton.styleFrom(
              foregroundColor: Colors.red,
            ),
            onPressed: () {
              vm.deleteTarget(viewModel.targetId);
              Navigator.pop(context);
            },
            child: const Text('删除'),
          ),
        ],
      ),
    );
  }
  void _showEditPanel(BuildContext context) {
    final viewModel = context.read<TargetListViewModel>();
    viewModel.startEdit(this.viewModel.targetId);

    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => Provider<TargetListViewModel>.value(
        value: viewModel,
        child: Padding(
          padding: EdgeInsets.only(
            bottom: MediaQuery.of(context).viewInsets.bottom,
          ),
          child: _EditPanel(viewModel: this.viewModel),
        ),
      ),
    );
  }


  Widget _buildActionButton({
    required IconData icon,
    required String label,
    required VoidCallback onPressed,
    Color? color,
  }) {
    return Expanded(
      child: InkWell(
        onTap: onPressed,
        child: Padding(
          padding: const EdgeInsets.symmetric(vertical: 8),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(icon, size: 16, color: color ?? Colors.grey.shade700),
              const SizedBox(width: 4),
              Text(
                label,
                style: TextStyle(
                  fontSize: 13,
                  color: color ?? Colors.grey.shade700,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class _EditPanel extends StatefulWidget {
  final TargetViewModel viewModel;

  const _EditPanel({
    required this.viewModel,
  });

  @override
  State<_EditPanel> createState() => _EditPanelState();
}

class _EditPanelState extends State<_EditPanel> {
  late TextEditingController nameController;
  late TextEditingController distanceController;
  late TextEditingController heightController;
  late TextEditingController modelController;

  late bool isBasePoint;
  late bool isMeasurable;
  late bool isSelfAdaption;

  @override
  void initState() {
    super.initState();
    nameController = TextEditingController(text: widget.viewModel.name);
    distanceController = TextEditingController(
      text: widget.viewModel.distance?.toString() ?? '',
    );
    heightController = TextEditingController(
      text: widget.viewModel.heightDiff?.toString() ?? '',
    );
    modelController = TextEditingController(
      text: widget.viewModel.model?.toString() ?? '',
    );
    isBasePoint = widget.viewModel.isBasePoint;
    isMeasurable = widget.viewModel.isMeasurable;
    isSelfAdaption = widget.viewModel.selfAdaption ?? false;
  }

  @override
  void dispose() {
    nameController.dispose();
    distanceController.dispose();
    heightController.dispose();
    modelController.dispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 10,
          ),
        ],
      ),
      constraints: BoxConstraints(
        maxHeight: MediaQuery.of(context).size.height * 0.8,
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          _buildHeader(context),
          const Divider(height: 1),
          Flexible(
            child: SingleChildScrollView(
              child: _buildContent(),
            ),
          ),
          _buildActions(context),
        ],
      ),
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Colors.blue.shade50,
              borderRadius: BorderRadius.circular(8),
            ),
            child: const Icon(Icons.edit_rounded, color: Colors.blue, size: 20),
          ),
          const SizedBox(width: 12),
          const Text(
            '设置标靶',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
            ),
          ),
          const Spacer(),
          IconButton(
            icon: const Icon(Icons.close),
            onPressed: () => Navigator.pop(context),
          ),
        ],
      ),
    );
  }

  Widget _buildContent() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          _buildInputField(
            label: '标靶名称',
            controller: nameController,
            icon: Icons.label_outline,
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: _buildInputField(
                  label: '距离 (m)',
                  controller: distanceController,
                  icon: Icons.straighten,
                  keyboardType: TextInputType.number,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildInputField(
                  label: '高度差 (m)',
                  controller: heightController,
                  icon: Icons.height,
                  keyboardType: TextInputType.number,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          _buildInputField(
            label: '型号',
            controller: modelController,
            icon: Icons.model_training,
          ),
          const SizedBox(height: 24),
          _buildSwitches(),
        ],
      ),
    );
  }

  Widget _buildInputField({
    required String label,
    required TextEditingController controller,
    required IconData icon,
    TextInputType? keyboardType,
  }) {
    return TextField(
      controller: controller,
      decoration: InputDecoration(
        labelText: label,
        prefixIcon: Icon(icon, size: 20),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
        ),
      ),
      keyboardType: keyboardType,
    );
  }

  Widget _buildSwitches() {
    return Wrap(
      spacing: 12,
      runSpacing: 12,
      children: [
        _buildSwitch(
          label: '基准点',
          value: isBasePoint,
          onChanged: (v) => setState(() => isBasePoint = v),
          activeColor: Colors.blue,
        ),
        _buildSwitch(
          label: '测量',
          value: isMeasurable,
          onChanged: (v) => setState(() => isMeasurable = v),
          activeColor: Colors.green,
        ),
        _buildSwitch(
          label: '自适应标靶',
          value: isSelfAdaption,
          onChanged: (v) => setState(() => isSelfAdaption = v),
          activeColor: Colors.green,
        ),
      ],
    );
  }

  Widget _buildSwitch({
    required String label,
    required bool value,
    required ValueChanged<bool> onChanged,
    required Color activeColor,
  }) {
    return ConstrainedBox(
      constraints: const BoxConstraints(minWidth: 140),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        decoration: BoxDecoration(
          color: value ? activeColor.withOpacity(0.1) : Colors.grey.shade50,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: value ? activeColor.withOpacity(0.5) : Colors.grey.shade300,
          ),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              label,
              style: TextStyle(
                color: value ? activeColor : Colors.grey.shade700,
                fontWeight: FontWeight.w500,
                fontSize: 14,
              ),
            ),
            const SizedBox(width: 8),
            Switch.adaptive(
              value: value,
              onChanged: onChanged,
              activeColor: activeColor,
              materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActions(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Row(
        children: [
          Expanded(
            child: TextButton(
              onPressed: () {
                final viewModel = context.read<TargetListViewModel>();
                viewModel.cancelEdit(widget.viewModel.targetId);
                Navigator.pop(context);
              },
              style: TextButton.styleFrom(
                padding: const EdgeInsets.symmetric(vertical: 12),
              ),
              child: const Text('取消'),
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: FilledButton(
              onPressed: () {

                final target = Target(
                  targetId: widget.viewModel.targetId,
                  name: nameController.text,
                  actualMeasureDistance: double.tryParse(distanceController.text),
                  heightDiff: double.tryParse(heightController.text),
                  basePoint: isBasePoint,
                  skipMeasurement: !isMeasurable,
                  selfAdaption: isSelfAdaption,
                  targetModel: modelController.text,
                  rect: Rect.zero,
                );

                final viewModel = context.read<TargetListViewModel>();
                viewModel.updateEditingTarget(target);
                viewModel.confirmEdit(widget.viewModel.targetId);

                Navigator.pop(context);
              },
              style: FilledButton.styleFrom(
                padding: const EdgeInsets.symmetric(vertical: 12),
              ),
              child: const Text('确定'),
            ),
          ),
        ],
      ),
    );
  }
}


// 通用的批量操作对话框基类
class _BatchOperationDialog extends StatefulWidget {
  final String title;
  final String subtitle;
  final List<Target> targets;
  final Function(List<String>) onAction;
  final String actionButtonText;
  final Widget Function(Target target, bool isSelected, ValueChanged<bool?> onChanged) itemBuilder;

  const _BatchOperationDialog({
    required this.title,
    required this.subtitle,
    required this.targets,
    required this.onAction,
    required this.actionButtonText,
    required this.itemBuilder,
  });

  @override
  State<_BatchOperationDialog> createState() => _BatchOperationDialogState();
}

class _BatchOperationDialogState extends State<_BatchOperationDialog> {
  final Set<String> selectedTargetIds = {};
  bool selectAll = false;

  @override
  Widget build(BuildContext context) {
    // 获取屏幕尺寸，实现响应式布局
    final screenSize = MediaQuery.of(context).size;
    final dialogWidth = math.min(520.0, screenSize.width * 0.9);
    final dialogHeight = math.min(420.0, screenSize.height * 0.8);

    return Dialog(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Container(
        width: dialogWidth,
        height: dialogHeight,
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // 1. 标题区域 - 固定背景色和阴影
            Container(
              padding: const EdgeInsets.fromLTRB(24, 24, 24, 16),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(12),
                  topRight: Radius.circular(12),
                ),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.05),
                    offset: const Offset(0, 1),
                    blurRadius: 1,
                  ),
                ],
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    widget.title,
                    style: const TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  if (widget.subtitle.isNotEmpty)
                    Padding(
                      padding: const EdgeInsets.only(top: 8),
                      child: Text(
                        widget.subtitle,
                        style: TextStyle(color: Colors.grey.shade700, fontSize: 14),
                      ),
                    ),
                ],
              ),
            ),

            // 2. 选择头部区域
            _buildSelectionHeader(),

            // 3. 列表区域 - 明确的背景色和滚动约束
            Expanded(
              child: Container(
                color: Colors.white,
                child: ScrollConfiguration(
                  // 禁用滚动穿透
                  behavior: ScrollConfiguration.of(context).copyWith(
                    physics: const ClampingScrollPhysics(),
                    overscroll: false,
                  ),
                  child: ListView.separated(
                    padding: const EdgeInsets.only(bottom: 8),
                    itemCount: widget.targets.length,
                    separatorBuilder: (context, index) => const Divider(
                      height: 1,
                      thickness: 0.5,
                      indent: 16,
                      endIndent: 16,
                    ),
                    itemBuilder: (context, index) {
                      final target = widget.targets[index];
                      return Container(
                        color: Colors.white, // 确保每个项都有背景色
                        child: widget.itemBuilder(
                          target,
                          selectedTargetIds.contains(target.targetId),
                              (selected) {
                            setState(() {
                              if (selected == true) {
                                selectedTargetIds.add(target.targetId);
                              } else {
                                selectedTargetIds.remove(target.targetId);
                              }
                              selectAll = selectedTargetIds.length == widget.targets.length;
                            });
                          },
                        ),
                      );
                    },
                  ),
                ),
              ),
            ),

            // 4. 按钮区域 - 固定背景色和上边框
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: const BorderRadius.only(
                  bottomLeft: Radius.circular(12),
                  bottomRight: Radius.circular(12),
                ),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.05),
                    offset: const Offset(0, -1),
                    blurRadius: 1,
                  ),
                ],
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  TextButton(
                    onPressed: () => Navigator.of(context).pop(),
                    child: const Text('取消'),
                  ),
                  const SizedBox(width: 16),
                  FilledButton(
                    onPressed: selectedTargetIds.isEmpty
                        ? null
                        : () {
                      widget.onAction(selectedTargetIds.toList());
                      Navigator.of(context).pop();
                    },
                    child: Text('${widget.actionButtonText} (${selectedTargetIds.length})'),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSelectionHeader() {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        border: Border(
          top: BorderSide(color: Colors.grey.shade200),
          bottom: BorderSide(color: Colors.grey.shade200),
        ),
      ),
      child: Row(
        children: [
          Checkbox(
            value: selectAll,
            onChanged: (value) {
              setState(() {
                selectAll = value ?? false;
                if (selectAll) {
                  selectedTargetIds.clear();
                  selectedTargetIds.addAll(
                    widget.targets.map((t) => t.targetId),
                  );
                } else {
                  selectedTargetIds.clear();
                }
              });
            },
          ),
          const Text('全选', style: TextStyle(fontWeight: FontWeight.w500)),
          const Spacer(),
          LayoutBuilder(
            builder: (context, constraints) {
              // 响应式调整计数器显示
              final isNarrow = constraints.maxWidth < 300;

              return Container(
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                decoration: BoxDecoration(
                  color: Colors.grey.shade100,
                  borderRadius: BorderRadius.circular(16),
                  border: Border.all(color: Colors.grey.shade200),
                ),
                child: Text(
                  isNarrow
                      ? '${selectedTargetIds.length}/${widget.targets.length}'
                      : '已选择: ${selectedTargetIds.length}/${widget.targets.length}',
                  style: TextStyle(
                    fontSize: 13,
                    color: Colors.grey.shade700,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              );
            },
          ),
        ],
      ),
    );
  }
}

// 通用的状态标签组件
class StatusChip extends StatelessWidget {
  final IconData icon;
  final String label;
  final Color color;
  final bool showSpinner;
  final String? tooltip;

  const StatusChip({
    required this.icon,
    required this.label,
    required this.color,
    this.showSpinner = false,
    this.tooltip,
    Key? key,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final chip = LayoutBuilder(
        builder: (context, constraints) {
          // 响应式调整状态标签
          final isCompact = constraints.maxWidth < 80;

          return Container(
            padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 5),
            decoration: BoxDecoration(
              color: color.withOpacity(0.1),
              borderRadius: BorderRadius.circular(16),
              border: Border.all(color: color.withOpacity(0.3)),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                if (showSpinner)
                  SizedBox(
                    width: 14,
                    height: 14,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      color: color,
                    ),
                  )
                else
                  Icon(icon, size: 14, color: color),
                if (!isCompact) const SizedBox(width: 6),
                if (!isCompact)
                  Text(
                    label,
                    style: TextStyle(
                      fontSize: 12,
                      color: color,
                      fontWeight: FontWeight.w500,
                    ),
                    overflow: TextOverflow.ellipsis,
                  ),
              ],
            ),
          );
        }
    );

    if (tooltip != null) {
      return Tooltip(
        message: tooltip!,
        child: chip,
      );
    }
    return chip;
  }
}

// 批量初始化对话框
class _BatchInitializeDialog extends StatelessWidget {
  final List<Target> targets;
  final Function(List<String>) onInitialize;

  const _BatchInitializeDialog({
    required this.targets,
    required this.onInitialize,
  });

  @override
  Widget build(BuildContext context) {
    return _BatchOperationDialog(
      title: '标靶初始化',
      subtitle: '选择需要初始化的标靶',
      targets: targets,
      onAction: onInitialize,
      actionButtonText: '初始化',
      itemBuilder: (target, isSelected, onChanged) {
        return _buildInitializeTargetItem(context, target, isSelected, onChanged);
      },
    );
  }

  Widget _buildInitializeTargetItem(
      BuildContext context,
      Target target,
      bool isSelected,
      ValueChanged<bool?> onChanged
      ) {
    final targetBloc = context.watch<TargetBloc>();
    final bool isInitializing = targetBloc.state.initializingTargets[target.targetId] ?? false;
    final String? errorMessage = targetBloc.state.initializationErrors[target.targetId];
    final bool isInitialized = target.isInitialized;

    return CheckboxListTile(
      title: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  target.name.isEmpty ? '未命名标靶' : target.name,
                  style: const TextStyle(
                    fontWeight: FontWeight.w500,
                  ),
                  overflow: TextOverflow.ellipsis,
                  maxLines: 1,
                ),
                Text(
                  'ID: ${target.targetId}',
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey.shade600,
                  ),
                  overflow: TextOverflow.ellipsis,
                  maxLines: 1,
                ),
              ],
            ),
          ),
          const SizedBox(width: 8),
          if (isInitializing)
            const StatusChip(
              icon: Icons.hourglass_top,
              label: '初始化中',
              color: Colors.blue,
              showSpinner: true,
            )
          else if (isInitialized)
            const StatusChip(
              icon: Icons.check_circle,
              label: '已初始化',
              color: Colors.green,
            )
          else if (errorMessage != null)
              StatusChip(
                icon: Icons.error_outline,
                label: '初始化失败',
                color: Colors.red,
                tooltip: errorMessage,
              ),
        ],
      ),
      value: isSelected,
      onChanged: isInitializing ? null : onChanged,
      controlAffinity: ListTileControlAffinity.leading,
      contentPadding: const EdgeInsets.symmetric(horizontal: 8),
      dense: true,
    );
  }
}

// 批量调试对话框
class _BatchDebugDialog extends StatelessWidget {
  final List<Target> targets;
  final Function(List<String>) onDebug;

  const _BatchDebugDialog({
    required this.targets,
    required this.onDebug,
  });

  @override
  Widget build(BuildContext context) {
    return _BatchOperationDialog(
      title: '标靶调试',
      subtitle: '选择需要调试的标靶',
      targets: targets,
      onAction: onDebug,
      actionButtonText: '调试',
      itemBuilder: (target, isSelected, onChanged) {
        return _buildDebugTargetItem(target, isSelected, onChanged);
      },
    );
  }

  Widget _buildDebugTargetItem(
      Target target,
      bool isSelected,
      ValueChanged<bool?> onChanged
      ) {
    final bool isInitialized = target.isInitialized;

    return CheckboxListTile(
      title: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  target.name.isEmpty ? '未命名标靶' : target.name,
                  style: const TextStyle(
                    fontWeight: FontWeight.w500,
                  ),
                  overflow: TextOverflow.ellipsis,
                  maxLines: 1,
                ),
                Text(
                  'ID: ${target.targetId}',
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey.shade600,
                  ),
                  overflow: TextOverflow.ellipsis,
                  maxLines: 1,
                ),
              ],
            ),
          ),
          const SizedBox(width: 8),
          if (isInitialized)
            const StatusChip(
              icon: Icons.check_circle,
              label: '已初始化',
              color: Colors.green,
            )
          else
            const StatusChip(
              icon: Icons.warning_amber,
              label: '未初始化',
              color: Colors.orange,
            ),
        ],
      ),
      value: isSelected,
      onChanged: onChanged,
      controlAffinity: ListTileControlAffinity.leading,
      contentPadding: const EdgeInsets.symmetric(horizontal: 8),
      dense: true,
    );
  }
}
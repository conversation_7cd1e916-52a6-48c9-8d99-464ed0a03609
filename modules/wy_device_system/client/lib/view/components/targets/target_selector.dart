import 'package:client/bloc/targets_blocs.dart';
import 'package:client/model/target.dart';
import 'package:dropdown_search/dropdown_search.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class TargetDropDownSearch extends StatelessWidget {
  const TargetDropDownSearch({
    Key? key,
    required this.onChanged,
    this.selectedTargetId,
    this.hintText = '选择标靶',
    this.showSearchBox = true,
    this.dropdownWidth,
    this.dropdownHeight,
    this.enabled = true,
  }) : super(key: key);

  final Function(Target) onChanged;
  final String? selectedTargetId;
  final String hintText;
  final bool showSearchBox;
  final double? dropdownWidth;
  final double? dropdownHeight;
  final bool enabled;

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<TargetBloc, TargetState>(
      builder: (context, state) {
        // 如果标靶数据为空，尝试加载标靶列表
        if (state.targets.isEmpty && !state.isLoading) {
          context.read<TargetBloc>().add(const RequestTargets());
        }

        // 找到选中的标靶
        Target? selectedTarget;
        if (selectedTargetId != null) {
          selectedTarget = state.targets.firstWhere(
                (t) => t.targetId == selectedTargetId,
            orElse: () => Target(targetId: '', name: '', rect: Rect.zero),
          );
        }

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (state.isLoading)
              const LinearProgressIndicator()
            else
              DropdownSearch<Target>(
                popupProps: PopupProps.menu(
                  showSearchBox: showSearchBox,
                  searchFieldProps: const TextFieldProps(
                    decoration: InputDecoration(
                      hintText: '搜索标靶',
                      prefixIcon: Icon(Icons.search),
                      border: OutlineInputBorder(),
                    ),
                  ),
                  menuProps: MenuProps(
                    borderRadius: BorderRadius.circular(8),
                  ),
                  constraints: BoxConstraints(
                    maxWidth: dropdownWidth ?? MediaQuery.of(context).size.height * 0.1,
                    maxHeight: dropdownHeight ?? MediaQuery.of(context).size.height * 0.25,
                  ),
                ),
                items: (filter, infiniteScrollProps) => state.targets,
                enabled: enabled && state.targets.isNotEmpty,
                itemAsString: (Target target) {
                  if (target.name.isEmpty) return target.targetId;
                  final distance = target.actualMeasureDistance != null
                      ? " (${target.actualMeasureDistance}m)"
                      : "";

                  return "${target.name}$distance";
                },
                selectedItem: selectedTarget,
                compareFn: (Target item, Target selectedItem) =>
                item.targetId == selectedItem.targetId,
                onChanged: (Target? target) {
                  if (target != null) {
                    onChanged(target);
                  }
                },
                filterFn: (target, filter) {
                    if (filter.isEmpty) return true;
                    return target.name.toLowerCase().contains(filter.toLowerCase());
                },

              ),
          ],
        );
      },
    );
  }
}
import 'dart:async';

import 'package:client/model/target_info.dart';
import 'package:client/view/components/targets/target_card_controller.dart';
import 'package:client/view/components/targets/target_item.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';

import '../../../model/pixel_position.dart';

class TargetInfoCard extends StatefulWidget{

  late TargetCardController ctrl;

  TargetInfoCard() {
    List<Target> targetList = [];

    targetList.add(Target(targetId: "1", name: "标靶1", refPoint: null));
    targetList.add(Target(targetId: "2", name: "标靶2", refPoint: null));
    targetList.add(Target(targetId: "3", name: "标靶3", refPoint: null));
    targetList.add(Target(targetId: "4", name: "标靶4", refPoint: null));
    targetList.add(Target(targetId: "5", name: "标靶5", refPoint: null));
    ctrl = TargetCardController(targetList);
  }


  @override
  State<StatefulWidget> createState() {
    return TargetInfoCardState();
  }

}

class TargetInfoCardState extends State<TargetInfoCard> {
  TargetInfoCardState();

  @override
  void initState() {
    super.initState();
  }




  @override
  void dispose() {
    super.dispose();
  }
  @override
  Widget build(BuildContext context) {
    return ValueListenableBuilder<List<Target>>(
      valueListenable: widget.ctrl,
      builder: (context, targets, _) {
        return HorizontalListView(targets: targets);
      },
    );
  }
}


class HorizontalListView extends StatelessWidget {
   HorizontalListView({super.key, required this.targets});

  List<Target> targets;


  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: 200,
      child: ListView(
        reverse: false,
        shrinkWrap: true,
        scrollDirection: Axis.horizontal,
        children: targets
            .map((target) => Container(
          alignment: Alignment.center,
          width: 200,
          height: 100,
          child: TargetItem(target: target,),
        ))
            .toList(),
      ),
    );
  }
  String colorString(Color color) =>
      "标靶1";
}
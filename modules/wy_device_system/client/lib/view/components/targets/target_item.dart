import 'package:flutter/material.dart';
import '../../../model/target_info.dart';

enum DisplayMode { detailed, compact }

class TargetItem extends StatefulWidget {
  final Target target;
  final DisplayMode displayMode;

  TargetItem({required this.target, this.displayMode = DisplayMode.detailed});

  @override
  State<StatefulWidget> createState() => TargetItemState();
}

class TargetItemState extends State<TargetItem> {
  late TextEditingController _distanceController;
  late TextEditingController _heightController;

  @override
  void initState() {
    super.initState();
    _distanceController = TextEditingController(text: widget.target.actualMeasureDistance.toString());
    _heightController = TextEditingController(text: widget.target.heightDiff.toString());
  }

  @override
  void didUpdateWidget(covariant TargetItem oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.target != oldWidget.target) {
      _distanceController.text = widget.target.actualMeasureDistance.toString();
      _heightController.text = widget.target.heightDiff.toString();
    }
  }

  @override
  void dispose() {
    _distanceController.dispose();
    _heightController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 8,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(15),
        side: BorderSide(
          color: widget.target.isBasePoint ? Colors.blue.withOpacity(0.5) : Colors.transparent,
          width: 2,
        ),
      ),
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(15),
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [Colors.grey.shade900, Colors.grey.shade800],
          ),
        ),
        child: Padding(
          padding: const EdgeInsets.all(12.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 顶部标靶名称和开关
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Expanded(
                    child: Row(
                      children: [
                        IconButton(
                          icon: Icon(
                            Icons.location_on,
                            color: widget.target.isBasePoint ? Colors.blue : Colors.grey,
                            size: 28,
                          ),
                          onPressed: () {},
                          tooltip: '设为基准点',
                        ),
                        Expanded(
                          child: Text(
                            widget.target.name,
                            style: const TextStyle(
                              fontSize: 20,
                              fontWeight: FontWeight.bold,
                              color: Colors.white,
                            ),
                            overflow: TextOverflow.ellipsis, // 防止名称过长溢出
                          ),
                        ),
                        if (widget.target.isBasePoint)
                          Padding(
                            padding: const EdgeInsets.only(left: 8.0),
                            child: Icon(Icons.star, color: Colors.blue, size: 20),
                          ),
                      ],
                    ),
                  ),
                  Tooltip(
                    message: widget.target.skipMeasurement ? '启用测量' : '禁用测量',
                    child: Transform.scale(
                      scale: 0.8,
                      child: Switch(
                        value: !widget.target.skipMeasurement,
                        activeColor: Colors.blue,
                        onChanged: (value) {},
                      ),
                    ),
                  ),
                ],
              ),
              const Divider(color: Colors.grey),
              // ID和初始化状态
              Row(
                children: [
                  Expanded(
                    child: Container(
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: Colors.blue.withOpacity(0.2),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Text(
                        'ID: ${widget.target.targetId}',
                        style: const TextStyle(color: Colors.blue),
                        overflow: TextOverflow.ellipsis, // 防止ID过长溢出
                      ),
                    ),
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Container(
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: widget.target.refPoint == null
                            ? Colors.red.withOpacity(0.2)
                            : Colors.green.withOpacity(0.2),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Row(
                        children: [
                          Icon(
                            widget.target.refPoint == null
                                ? Icons.warning
                                : Icons.check_circle,
                            color: widget.target.refPoint == null ? Colors.red : Colors.green,
                            size: 16,
                          ),
                          const SizedBox(width: 4),
                          Expanded(
                            child: Text(
                              widget.target.refPoint == null ? '未初始化' : '已初始化',
                              style: TextStyle(
                                color: widget.target.refPoint == null ? Colors.red : Colors.green,
                              ),
                              overflow: TextOverflow.ellipsis, // 防止状态文本溢出
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 12),
              // 根据显示模式渲染输入框或文本
              if (widget.displayMode == DisplayMode.detailed) ...[
                Row(
                  children: [
                    Expanded(
                      child: _buildTextField(
                        controller: _distanceController,
                        label: '实际距离',
                        suffix: 'm',
                      ),
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: _buildTextField(
                        controller: _heightController,
                        label: '高度差',
                        suffix: 'm',
                      ),
                    ),
                  ],
                ),
              ] else ...[
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Expanded(
                      child: Text(
                        '距离: ${widget.target.actualMeasureDistance.toStringAsFixed(2)} m',
                        style: const TextStyle(color: Colors.white),
                        overflow: TextOverflow.ellipsis, // 防止距离文本溢出
                      ),
                    ),
                    Expanded(
                      child: Text(
                        '高度差: ${widget.target.heightDiff.toStringAsFixed(2)} m',
                        style: const TextStyle(color: Colors.white),
                        overflow: TextOverflow.ellipsis, // 防止高度差文本溢出
                      ),
                    ),
                  ],
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildTextField({
    required TextEditingController controller,
    required String label,
    required String suffix,
  }) {
    return TextField(
      controller: controller,
      style: const TextStyle(color: Colors.white),
      decoration: InputDecoration(
        labelText: label,
        suffixText: suffix,
        labelStyle: TextStyle(color: Colors.grey[400]),
        suffixStyle: const TextStyle(color: Colors.grey),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(10),
          borderSide: BorderSide(color: Colors.grey[700]!),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(10),
          borderSide: const BorderSide(color: Colors.blue),
        ),
        filled: true,
        fillColor: Colors.grey[850],
        contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      ),
      keyboardType: TextInputType.number,
      onSubmitted: (value) {},
    );
  }
}
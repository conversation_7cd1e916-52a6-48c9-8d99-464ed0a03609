import 'package:client/model/target.dart';
import 'package:flutter/material.dart';
import 'package:tdesign_flutter/tdesign_flutter.dart';

class TargetPicker extends StatefulWidget {
  final context;
  final List<Target> targetList;
  final ValueChanged<String>? onSelected;
  final String? selected;
  const TargetPicker(
      {super.key,
      required this.context,
      required this.targetList,
      this.onSelected,
      this.selected});

  @override
  State<TargetPicker> createState() => _TargetPickerState();
}

class _TargetPickerState extends State<TargetPicker> {
  String _selected = '';
  late List<Target> _filteredList;
  @override
  void initState() {
    super.initState();
    _selected = widget.selected ?? '';
    _filteredList = widget.targetList;
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final screenSize = MediaQuery.of(context).size;
    final boxHeight = screenSize.height * 0.6;
    return Container(
      width: screenSize.width * 0.85,
      height: boxHeight,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.2),
            blurRadius: 16,
            offset: const Offset(0, 8),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            alignment: Alignment.center,
            height: 60,
            child: TDText(
              '选择标靶',
              font: TDTheme.of(context).fontTitleExtraLarge,
            ),
          ),
          TDSearchBar(
            placeHolder: '搜索标靶',
            onTextChanged: (String text) {
              setState(() {
                _filteredList = widget.targetList
                    .where((item) => item.targetId.contains(text.toLowerCase()))
                    .toList();
              });
            },
          ),
          Container(
            padding: const EdgeInsets.only(right: 16),
            height: boxHeight - 120 - 80, // 计算剩余高度
            child: _buildListContent(),
          ),
          Container(
              alignment: Alignment.center,
              padding: const EdgeInsets.all(16),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  TDButton(
                    text: '取消',
                    theme: TDButtonTheme.light,
                    isBlock: true,
                    onTap: () {
                      Navigator.pop(widget.context);
                    },
                  ),
                  TDButton(
                    text: '确定',
                    theme: TDButtonTheme.primary,
                    isBlock: true,
                    onTap: () {
                      widget.onSelected!(_selected);
                    },
                  )
                ],
              ))
        ],
      ),
    );
  }

  Widget _buildListContent() {
    if (_filteredList.isEmpty) {
      return const Center(
        child: Padding(
          padding: EdgeInsets.all(16),
          child: Text('没有找到匹配项', style: TextStyle(color: Colors.grey)),
        ),
      );
    }
    return TDRadioGroup(
      onRadioGroupChange: (selectedId) {
        setState(() {
          _selected = selectedId!;
        });
      },
      selectId: _selected,
      child: ListView.builder(
        padding: EdgeInsets.zero,
        physics: const ClampingScrollPhysics(),
        itemCount: _filteredList.length,
        itemBuilder: (context, index) {
          return TDRadio(
            id: _filteredList[index].targetId,
            title: _filteredList[index].targetId,
          );
        },
      ),
    );
  }
}

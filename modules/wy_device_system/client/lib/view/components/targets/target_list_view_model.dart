import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';

import '../../../bloc/targets_blocs.dart';
import '../../../bloc/wy_device_blocs.dart';
import '../../../model/target.dart';
import '../../../repository/target_repository.dart';
import 'package:client/view/components/targets/target_list.dart';

import '../roi_selector/roi_scale_converter.dart';

class TargetListViewModel {
  final TargetBloc bloc;
  final TargetRepository repository;
  Target? _editingTarget;
  final WyDeviceBloc? wyDeviceBloc; // 添加WyDeviceBloc参数


  TargetListViewModel({
    required this.bloc,
    required this.repository,
    required this.wyDeviceBloc,
  });


  // 从Target对象构建ROI参数
  Map<String, dynamic> _buildRoiFromTarget(Target target) {
    if (target.rect == Rect.zero) {
      // 如果没有设置ROI，使用默认值
      return {
        'x': 245 * 6, // 245 * (3840/640) = 1470
        'y': 165 * 4.5, // 165 * (2160/480) = 742.5
        'width': 150 * 6, // 150 * (3840/640) = 900
        'height': 150 * 4.5, // 150 * (2160/480) = 675
        'factorX': 1,
        'factorY': 1,
      };
    }

    // 使用与 Target.convertToJson 相同的缩放逻辑
    var originalRect = ScaleConverter.scaleRectToOriginal(
        target.rect,
        Size(640, 480),
        ScaleConverter.defaultDeviceResolution
    );

    return {
      "x": originalRect.left.toInt(),
      "y": originalRect.top.toInt(),
      "width": originalRect.width.toInt(),
      "height": originalRect.height.toInt(),
      "factorX": 1,
      "factorY": 1
    };
  }


  Future<void> initializeTarget(String targetId) async {
    // 添加初始化请求
    bloc.add(TargetInitializeRequest(targetId));

    // 设置超时定时器
    Future.delayed(const Duration(seconds: 10), () {
      // 检查是否仍在初始化中
      if (bloc.state.initializingTargets[targetId] == true) {
        // 超时处理
        bloc.add(TargetInitializeFailedEvent(targetId, "初始化超时，请检查连接"));
      }
    });
  }

  Future<void> initializeTargets(List<String> targetIds) async {
    // 批量初始化请求
    bloc.add(TargetsBatchInitializeRequest(targetIds));

    // 为每个标靶设置超时定时器
    for (final targetId in targetIds) {
      Future.delayed(const Duration(seconds: 8), () {
        // 检查是否仍在初始化中
        if (bloc.state.initializingTargets[targetId] == true) {
          // 超时处理
          bloc.add(TargetInitializeFailedEvent(targetId, "初始化超时，请检查连接"));
        }
      });
    }
  }

// 在TargetListViewModel类中添加方法
  void debugTargets(List<String> targetIds) {
    // 获取所选标靶
    final selectedTargets = bloc.state.targets
        .where((target) => targetIds.contains(target.targetId))
        .toList();
    // 创建调试请求体
    final targetsForDebug = selectedTargets.map((target) => {
      "cameraId": target.cameraId,
      "targetId": target.targetId,
      "roi": _buildRoiFromTarget(target),
      "targetModel": target.targetModel.isNotEmpty ? target.targetModel : "T100"
    }).toList();

    // 发送调试请求
    bloc.add(TargetsBatchDebugRequest(targetIds));
  }




  void createTarget() {
    int cameraId = 0; // 默认值
    if (wyDeviceBloc != null) {
      cameraId = wyDeviceBloc!.state.selectedCameraId ?? 0;
    }
    final newTargetId = '';
    final newTarget = Target(
      targetId: newTargetId,
      name: '',
      cameraId: cameraId,
      targetModel: 'T100',
      actualMeasureDistance: 50.0,
      heightDiff: 0.0,
      skipMeasurement: false,
      basePoint: false,
      selfAdaption: false,
      rect: Rect.fromLTWH((640 - 50) / 2, (480 - 50) / 2, 50, 50),
    );

    // 使用 TargetCreated 事件代替直接加载
    bloc.add(TargetCreated(newTarget));
    bloc.add(TargetSelected(newTargetId));

  }

  void startEdit(String targetId) {
    final target = bloc.state.targets.firstWhere((t) => t.targetId == targetId);
    _editingTarget = target;
    bloc.add(TargetSelected(targetId));
  }

  void deleteTarget(String targetId) {
    final updatedTargets = bloc.state.targets.where((t) => t.targetId != targetId).toList();
    bloc.add(TargetDeleted(targetId));

    bloc.add(TargetsLoaded(updatedTargets));
  }


  void updateEditingTarget(Target updatedTarget) {
    if (_editingTarget != null) {
      final currentTarget = bloc.state.targets.firstWhere(
            (t) => t.targetId == updatedTarget.targetId,
        orElse: () => updatedTarget,
      );

      // 基于现有target创建更新版本
      final targetToUpdate = currentTarget.copyWith(
        name: updatedTarget.name,
        targetModel: updatedTarget.targetModel,
        actualMeasureDistance: updatedTarget.actualMeasureDistance,
        heightDiff: updatedTarget.heightDiff,
        skipMeasurement: updatedTarget.skipMeasurement,
        basePoint: updatedTarget.basePoint,
        selfAdaption: updatedTarget.selfAdaption,
      );

      final updatedTargets = bloc.state.targets.map((t) {
        if (t.targetId == targetToUpdate.targetId) {
          return targetToUpdate;
        } else {
          if (targetToUpdate.basePoint) {
            return t.copyWith(basePoint: false);
          }
          if (targetToUpdate.selfAdaption ?? false) {
            return t.copyWith(selfAdaption: false);
          }
          return t;
        }
      }).toList();

      bloc.add(TargetsLoaded(updatedTargets));
      _editingTarget = targetToUpdate;
    }
  }

  void confirmEdit(String targetId) {
    if (_editingTarget != null) {
      bloc.add(TargetUpdated(_editingTarget!));
      _editingTarget = null;
    }
  }

  void cancelEdit(String targetId) {
    _editingTarget = null;
  }

  Future<void> loadTargets() async {
    bloc.add(const RequestTargets());
  }

  void updateTarget(Target target) {
    final List<Target> updatedTargets = bloc.state.targets.map((t) {
      if (t.targetId == target.targetId) {
        return target;
      } else {
        // 如果当前标靶设置为基准点，其他标靶自动取消基准点
        if (target.basePoint) {
          return t.copyWith(basePoint: false);
        }
        // 如果当前标靶设置为自适应标靶，其他标靶自动取消自适应
        if (target.selfAdaption ?? false) {
          return t.copyWith(selfAdaption: false);
        }
        return t;
      }
    }).toList();

    bloc.add(TargetsLoaded(updatedTargets));
  }

  void selectTarget(String id) {
    bloc.add(TargetSelected(id));
  }

  TargetViewModel convertToViewModel(Target target, bool isSelected) {
    return TargetViewModel(
      targetId: target.targetId,
      cameraId: target.cameraId,
      name: target.name,
      distance: target.actualMeasureDistance,
      heightDiff: target.heightDiff,
      isSelected: isSelected,
      isBasePoint: target.basePoint,
      skipMeasurement: target.skipMeasurement,
      selfAdaption: target.selfAdaption,
    );
  }
}

class TargetViewModel {
  final String targetId;
  final int cameraId;
  final String name;
  final double? distance;
  final bool isSelected;
  final bool isBasePoint;
  final bool skipMeasurement;
  final double? heightDiff;
  final bool? selfAdaption;
  final String? model;

  const TargetViewModel( {
    required this.targetId,
    required this.name,
    required this.cameraId,
    this.distance,
    required this.isSelected,
    required this.isBasePoint,
    this.skipMeasurement = false,
    this.heightDiff,
    this.selfAdaption,
    this.model
  });

  // UI 显示相关的计算属性
  String get displayName => isBasePoint ? '$name (基准点)' : name;

  String get distanceText => distance != null
      ? '距离: ${distance!.toStringAsFixed(2)}m'
      : '未测量';

  String get heightText => heightDiff != null
      ? '高度差: ${heightDiff!.toStringAsFixed(2)}m'
      : '';

  String get modelText => model != null
      ? '型号: $model'
      : '';

  String get selfAdaptionText => selfAdaption != null&&selfAdaption!
      ? '自适应: $selfAdaption'
      : '';

  bool get showMeasurements => distance != null || heightDiff != null;

  // 测量状态
  bool get isMeasurable => !skipMeasurement;
}
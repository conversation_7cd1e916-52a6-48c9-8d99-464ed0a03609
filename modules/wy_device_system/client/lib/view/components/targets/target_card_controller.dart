
import 'package:client/model/target_info.dart';
import 'package:flutter/material.dart';
class TargetCardController extends ValueNotifier<List<Target>> {
  TargetCardController(super.value);

  void updateTargetDistance(String targetId, double distance) {
    int index = value.indexWhere((element) => element.targetId == targetId);
    if (index != -1) {
      var target = value[index];
      target.actualMeasureDistance = distance;
      notifyListeners();
    }
  }

  void toggleBasePoint(String targetId) {
    int index = value.indexWhere((element) => element.targetId == targetId);
    if (index != -1) {
      for (var target in value) {
        target.isBasePoint = false;
      }
      var target = value[index];
      target.isBasePoint = true;
      notifyListeners();
    }
  }

  void updateTargetHeight(String targetId, double height) {
    int index = value.indexWhere((element) => element.targetId == targetId);
    if (index != -1) {
      var target = value[index];
      target.heightDiff = height;
      notifyListeners();
    }
  }

  void toggleMeasurement(String targetId) {
    int index = value.indexWhere((element) => element.targetId == targetId);
    if (index != -1) {
      var target = value[index];
      target.skipMeasurement = !target.skipMeasurement;
      notifyListeners();
    }
  }
}
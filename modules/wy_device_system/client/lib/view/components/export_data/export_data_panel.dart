import 'package:flutter/material.dart';
import 'dart:convert';

import '../../../bloc/measurement_bloc.dart';
import '../../../bloc/wy_device_blocs.dart';
import '../../../model/target.dart';
import '../../../repository/telemetry_repository.dart';
import '../../../services/export_service.dart'; // 需要创建这个服务
import 'package:rinf/rinf.dart';
import 'package:inteagle_monitoring_robot_app/src/bindings/bindings.dart';
class ExportDataPanel extends StatefulWidget {
  final String deviceId;
  final List<Target> targets;
  final MeasurementChartBloc measurementChartBloc;
  final ScrollController? externalScrollController;
  final TelemetryRepository telemetryRepository ;

  const ExportDataPanel({
    Key? key,
    required this.deviceId ,
    required this.targets,
    required this.measurementChartBloc,
    this.externalScrollController,
    required this.telemetryRepository,
  }) : super(key: key);

  @override
  State<ExportDataPanel> createState() => _ExportDataPanelState();
}

class _ExportDataPanelState extends State<ExportDataPanel> {
  late List<bool> _selectedTargets = [];
  bool _includeEnvironmentalData = true;
  bool _includeIMUData = true;
  bool _includeTimeStamp = true;
  DateTimeRange? _dateRange;
  String _exportFormat = 'csv'; // 默认为CSV格式
  bool _isExporting = false;
  String _exportStatus = '';

  @override
  void initState() {
    super.initState();
    // 初始化所有目标为选中状态
    _selectedTargets = List.generate(widget.targets.length, (index) => true);
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildHeader(),
          const SizedBox(height: 16),
          Expanded(
            child: SingleChildScrollView(
              controller: widget.externalScrollController,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildTargetSelection(),
                  const Divider(),
                  _buildDataTypeSelection(),
                  const Divider(),
                  _buildDateRangeSelection(),
                  const Divider(),
                  _buildFormatOptions(),
                ],
              ),
            ),
          ),
          _buildExportButton(),
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return const Text(
      '导出数据',
      style: TextStyle(
        fontSize: 18,
        fontWeight: FontWeight.bold,
      ),
    );
  }

  Widget _buildTargetSelection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text('选择目标',
            style: TextStyle(fontWeight: FontWeight.bold)),
        const SizedBox(height: 8),
        Row(
          children: [
            TextButton.icon(
              icon: const Icon(Icons.select_all),
              label: const Text('全选'),
              onPressed: () {
                setState(() {
                  for (int i = 0; i < _selectedTargets.length; i++) {
                    _selectedTargets[i] = true;
                  }
                });
              },
            ),
            const SizedBox(width: 8),
            TextButton.icon(
              icon: const Icon(Icons.deselect),
              label: const Text('取消全选'),
              onPressed: () {
                setState(() {
                  for (int i = 0; i < _selectedTargets.length; i++) {
                    _selectedTargets[i] = false;
                  }
                });
              },
            ),
          ],
        ),
        const SizedBox(height: 8),
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: List.generate(
            widget.targets.length,
                (index) => FilterChip(
              selected: _selectedTargets[index],
              label: Text(widget.targets[index].name),
              onSelected: (selected) {
                setState(() {
                  _selectedTargets[index] = selected;
                });
              },
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildDataTypeSelection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text('数据类型',
            style: TextStyle(fontWeight: FontWeight.bold)),
        const SizedBox(height: 8),
        CheckboxListTile(
          title: const Text('环境数据'),
          subtitle: const Text('温度、湿度、气压等'),
          value: _includeEnvironmentalData,
          controlAffinity: ListTileControlAffinity.leading,
          contentPadding: EdgeInsets.zero,
          onChanged: (value) {
            setState(() {
              _includeEnvironmentalData = value ?? true;
            });
          },
        ),
        CheckboxListTile(
          title: const Text('IMU数据'),
          subtitle: const Text('加速度、角速度、姿态等'),
          value: _includeIMUData,
          controlAffinity: ListTileControlAffinity.leading,
          contentPadding: EdgeInsets.zero,
          onChanged: (value) {
            setState(() {
              _includeIMUData = value ?? true;
            });
          },
        ),
        CheckboxListTile(
          title: const Text('时间戳'),
          value: _includeTimeStamp,
          controlAffinity: ListTileControlAffinity.leading,
          contentPadding: EdgeInsets.zero,
          onChanged: (value) {
            setState(() {
              _includeTimeStamp = value ?? true;
            });
          },
        ),
      ],
    );
  }

  Widget _buildDateRangeSelection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text('时间范围',
            style: TextStyle(fontWeight: FontWeight.bold)),
        const SizedBox(height: 8),
        InkWell(
          onTap: () async {
            final DateTimeRange? picked = await showDateRangePicker(
              context: context,
              firstDate: DateTime.now().subtract(const Duration(days: 365)),
              lastDate: DateTime.now(),
              initialDateRange: _dateRange ??
                  DateTimeRange(
                    start: DateTime.now().subtract(const Duration(days: 7)),
                    end: DateTime.now(),
                  ),
            );
            if (picked != null) {
              setState(() {
                _dateRange = picked;
              });
            }
          },
          child: Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              border: Border.all(color: Colors.grey[300]!),
              borderRadius: BorderRadius.circular(4),
            ),
            child: Row(
              children: [
                const Icon(Icons.date_range),
                const SizedBox(width: 8),
                Text(
                  _dateRange == null
                      ? '选择日期范围'
                      : '${_dateRange!.start.toString().split(' ')[0]} 至 ${_dateRange!.end.toString().split(' ')[0]}',
                ),
                const Spacer(),
                const Icon(Icons.arrow_drop_down),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildFormatOptions() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text('导出格式',
            style: TextStyle(fontWeight: FontWeight.bold)),
        const SizedBox(height: 8),
        RadioListTile<String>(
          title: const Text('CSV (.csv)'),
          value: 'csv',
          groupValue: _exportFormat,
          onChanged: (value) {
            setState(() {
              _exportFormat = value!;
            });
          },
        ),
        RadioListTile<String>(
          title: const Text('Excel (.xlsx)'),
          value: 'xlsx',
          groupValue: _exportFormat,
          onChanged: (value) {
            setState(() {
              _exportFormat = value!;
            });
          },
        ),
      ],
    );
  }

  Widget _buildExportButton() {
    return Column(
      children: [
        if (_exportStatus.isNotEmpty)
          Padding(
            padding: const EdgeInsets.only(bottom: 8.0),
            child: Text(
              _exportStatus,
              style: TextStyle(
                color: _exportStatus.contains('成功')
                    ? Colors.green
                    : (_exportStatus.contains('失败') ? Colors.red : Colors.blue),
              ),
            ),
          ),
        SizedBox(
          width: double.infinity,
          child: ElevatedButton.icon(
            icon: _isExporting
                ? const SizedBox(
              width: 20,
              height: 20,
              child: CircularProgressIndicator(
                strokeWidth: 2,
                valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
              ),
            )
                : const Icon(Icons.download),
            label: Text(_isExporting ? '导出中...' : '导出数据'),
            style: ElevatedButton.styleFrom(
              padding: const EdgeInsets.symmetric(vertical: 16),
              backgroundColor: Theme.of(context).primaryColor,
              foregroundColor: Colors.white,
            ),
            onPressed: _isExporting ? null : _exportData,
          ),
        ),
      ],
    );
  }

  Future<void> _exportData() async {
    if (!_validateExportParameters()) {
      return;
    }

    setState(() {
      _isExporting = true;
      _exportStatus = '准备导出数据...';
    });

    try {
      // 构建导出请求参数
      final exportRequest = {
        'device_id':widget.deviceId,
        'targets': widget.targets
            .asMap()
            .entries
            .where((entry) => _selectedTargets[entry.key])
            .map((entry) => entry.value.targetId)
            .toList(),
        'include_environmental': _includeEnvironmentalData,
        'include_imu': _includeIMUData,
        'include_timestamp': _includeTimeStamp,
        'format': _exportFormat,
        'start_date': _dateRange?.start.toIso8601String(),
        'end_date': _dateRange?.end.toIso8601String(),
      };

      // 调用导出服务
      final exportService = ExportService();
      final result = await exportService.exportData(exportRequest);

      if (result.success) {
        setState(() {
          _exportStatus = '导出成功: ${result.message}';
        });
      } else {
        setState(() {
          _exportStatus = '导出失败: ${result.message}';
        });
      }
    } catch (e) {
      setState(() {
        _exportStatus = '导出失败: ${e.toString()}';
      });
    } finally {
      setState(() {
        _isExporting = false;
      });
    }
  }

  bool _validateExportParameters() {
    // 检查是否有选中的目标
    if (!_selectedTargets.contains(true)) {
      setState(() {
        _exportStatus = '请至少选择一个目标';
      });
      return false;
    }

    // 检查是否有选中的数据类型
    if (!_includeEnvironmentalData && !_includeIMUData) {
      setState(() {
        _exportStatus = '请至少选择一种数据类型';
      });
      return false;
    }

    return true;
  }
}
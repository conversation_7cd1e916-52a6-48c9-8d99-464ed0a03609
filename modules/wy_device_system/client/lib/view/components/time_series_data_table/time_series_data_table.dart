// import 'package:flutter/material.dart';
// import 'package:data_table_2/data_table_2.dart';
// import '../../../bloc/measurement_bloc.dart';
// import '../../../model/target.dart';
// import 'package:flutter_bloc/flutter_bloc.dart';
//
// /// 时序数据表格组件
// ///
// /// 用于显示大量时序数据，支持动态翻页、排序和筛选
// class TimeSeriesDataTable extends StatefulWidget {
//   final List<Target> targets;
//   final MeasurementChartBloc measurementChartBloc;
//
//   const TimeSeriesDataTable({
//     Key? key,
//     required this.targets,
//     required this.measurementChartBloc,
//   }) : super(key: key);
//
//   @override
//   State<TimeSeriesDataTable> createState() => _TimeSeriesDataTableState();
// }
//
// class _TimeSeriesDataTableState extends State<TimeSeriesDataTable> {
//   int _rowsPerPage = 20; // 每页显示行数
//   int _sortColumnIndex = 0; // 默认按时间排序
//   bool _sortAscending = false; // 默认降序（最新的数据在前）
//   Target? _selectedTarget; // 当前选中的目标
//   String? _searchQuery; // 搜索关键词
//   List<TimeSeriesDataRow> _filteredData = []; // 过滤后的数据
//   bool _isLoading = false; // 数据加载状态
//   int? _totalRows; // 总行数
//   int _currentPage = 0; // 当前页码
//
//   @override
//   void initState() {
//     super.initState();
//     _selectedTarget = widget.targets.isNotEmpty ? widget.targets.first : null;
//     _loadData();
//   }
//
//   /// 加载数据
//   Future<void> _loadData() async {
//     if (_selectedTarget == null) return;
//
//     setState(() {
//       _isLoading = true;
//     });
//
//     try {
//       // 通过Bloc获取数据
//       final state = widget.measurementChartBloc.state;
//       if (state is MeasurementChartLoaded) {
//         // 筛选当前选中目标的数据
//         final targetData = state.measurements
//             .where((m) => m.targetId == _selectedTarget!.targetId)
//             .toList();
//
//         // 计算总行数
//         _totalRows = targetData.length;
//
//         // 应用排序
//         if (_sortColumnIndex >= 0) {
//           targetData.sort((a, b) {
//             if (_sortColumnIndex == 0) { // 按时间排序
//               return _sortAscending
//                   ? a.timestamp.compareTo(b.timestamp)
//                   : b.timestamp.compareTo(a.timestamp);
//             } else {
//               // 对其他列排序的逻辑
//               final keyA = a.values.entries.elementAt(_sortColumnIndex - 1);
//               final keyB = b.values.entries.elementAt(_sortColumnIndex - 1);
//
//               if (keyA.value is num && keyB.value is num) {
//                 return _sortAscending
//                     ? (keyA.value as num).compareTo(keyB.value as num)
//                     : (keyB.value as num).compareTo(keyA.value as num);
//               } else {
//                 return _sortAscending
//                     ? keyA.value.toString().compareTo(keyB.value.toString())
//                     : keyB.value.toString().compareTo(keyA.value.toString());
//               }
//             }
//           });
//         }
//
//         // 应用搜索过滤
//         if (_searchQuery != null && _searchQuery!.isNotEmpty) {
//           final query = _searchQuery!.toLowerCase();
//           _filteredData = targetData
//               .where((m) =>
//           m.timestamp.toString().toLowerCase().contains(query) ||
//               m.values.values.any((v) =>
//                   v.toString().toLowerCase().contains(query)))
//               .map((m) => _measurementToRow(m))
//               .toList();
//         } else {
//           _filteredData = targetData.map((m) => _measurementToRow(m)).toList();
//         }
//       }
//     } catch (e) {
//       // 错误处理
//       ScaffoldMessenger.of(context).showSnackBar(
//         SnackBar(content: Text('加载数据失败: $e')),
//       );
//     } finally {
//       setState(() {
//         _isLoading = false;
//       });
//     }
//   }
//
//   /// 将测量数据转换为表格行数据
//   TimeSeriesDataRow _measurementToRow(dynamic measurement) {
//     final values = <String, dynamic>{};
//
//     // 添加目标指标值
//     for (final entry in measurement.values.entries) {
//       values[entry.key] = entry.value;
//     }
//
//     // 如果有环境数据，也添加进来
//     if (measurement.environmentalData != null) {
//       for (final entry in measurement.environmentalData.entries) {
//         values['env_${entry.key}'] = entry.value;
//       }
//     }
//
//     // 如果有IMU数据，也添加进来
//     if (measurement.imuData != null) {
//       for (final entry in measurement.imuData.entries) {
//         values['imu_${entry.key}'] = entry.value;
//       }
//     }
//
//     return TimeSeriesDataRow(
//       id: measurement.id,
//       timestamp: measurement.timestamp,
//       values: values,
//     );
//   }
//
//   @override
//   Widget build(BuildContext context) {
//     // 获取当前可用的数据列
//     final availableColumns = _getAvailableColumns();
//
//     return Card(
//       margin: const EdgeInsets.all(8.0),
//       child: Padding(
//         padding: const EdgeInsets.all(16.0),
//         child: Column(
//           crossAxisAlignment: CrossAxisAlignment.start,
//           children: [
//             // 标题和目标选择器
//             Row(
//               children: [
//                 const Text(
//                   '时序数据表',
//                   style: TextStyle(
//                     fontSize: 18,
//                     fontWeight: FontWeight.bold,
//                   ),
//                 ),
//                 const Spacer(),
//                 if (widget.targets.length > 1) ...[
//                   const Text('选择目标:'),
//                   const SizedBox(width: 8),
//                   DropdownButton<Target>(
//                     value: _selectedTarget,
//                     onChanged: (Target? newValue) {
//                       if (newValue != null && newValue != _selectedTarget) {
//                         setState(() {
//                           _selectedTarget = newValue;
//                           _currentPage = 0;
//                         });
//                         _loadData();
//                       }
//                     },
//                     items: widget.targets.map<DropdownMenuItem<Target>>((Target target) {
//                       return DropdownMenuItem<Target>(
//                         value: target,
//                         child: Text(target.name),
//                       );
//                     }).toList(),
//                   ),
//                 ],
//               ],
//             ),
//             const SizedBox(height: 8),
//
//             // 搜索框
//             TextField(
//               decoration: const InputDecoration(
//                 labelText: '搜索',
//                 prefixIcon: Icon(Icons.search),
//                 border: OutlineInputBorder(),
//               ),
//               onChanged: (value) {
//                 setState(() {
//                   _searchQuery = value;
//                   _currentPage = 0;
//                 });
//                 _loadData();
//               },
//             ),
//             const SizedBox(height: 16),
//
//             // 数据表格
//             Expanded(
//               child: _isLoading
//                   ? const Center(child: CircularProgressIndicator())
//                   : _buildDataTable(availableColumns),
//             ),
//           ],
//         ),
//       ),
//     );
//   }
//
//   /// 构建数据表格
//   Widget _buildDataTable(List<DataColumn2> columns) {
//     // 计算当前页的数据
//     final int startIndex = _currentPage * _rowsPerPage;
//     final int endIndex = (_currentPage + 1) * _rowsPerPage;
//     final pageData = _filteredData.length > startIndex
//         ? _filteredData.sublist(
//         startIndex,
//         _filteredData.length >= endIndex ? endIndex : _filteredData.length)
//         : <TimeSeriesDataRow>[];
//
//     return Column(
//       children: [
//         Expanded(
//           child: DataTable2(
//             columnSpacing: 12,
//             horizontalMargin: 12,
//             minWidth: 600,
//             sortColumnIndex: _sortColumnIndex,
//             sortAscending: _sortAscending,
//             showBottomBorder: true,
//             headingRowHeight: 40,
//             columns: columns,
//             empty: Center(
//               child: Text(_selectedTarget == null
//                   ? '请选择一个目标'
//                   : '没有数据可显示'),
//             ),
//             rows: pageData.map((data) {
//               return DataRow2(
//                 cells: [
//                   DataCell(Text(
//                     _formatTimestamp(data.timestamp),
//                     style: const TextStyle(fontFamily: 'monospace'),
//                   )),
//                   ...data.values.entries.map((entry) {
//                     final value = entry.value;
//                     String displayValue;
//
//                     if (value is double) {
//                       displayValue = value.toStringAsFixed(2);
//                     } else {
//                       displayValue = value.toString();
//                     }
//
//                     return DataCell(Text(
//                       displayValue,
//                       style: const TextStyle(fontFamily: 'monospace'),
//                     ));
//                   }).toList(),
//                 ],
//               );
//             }).toList(),
//           ),
//         ),
//
//         // 分页控制
//         _buildPagination(),
//       ],
//     );
//   }
//
//   /// 构建分页控件
//   Widget _buildPagination() {
//     final int pageCount = (_filteredData.length / _rowsPerPage).ceil();
//
//     return Padding(
//       padding: const EdgeInsets.only(top: 8.0),
//       child: Row(
//         mainAxisAlignment: MainAxisAlignment.spaceBetween,
//         children: [
//           // 显示当前页信息
//           Text(
//             '显示 ${_filteredData.isEmpty ? 0 : _currentPage * _rowsPerPage + 1} - ${(_currentPage + 1) * _rowsPerPage > _filteredData.length ? _filteredData.length : (_currentPage + 1) * _rowsPerPage} 条，共 ${_filteredData.length} 条',
//             style: const TextStyle(fontSize: 12),
//           ),
//
//           // 页面控制
//           Row(
//             children: [
//               // 每页行数选择
//               DropdownButton<int>(
//                 value: _rowsPerPage,
//                 onChanged: (int? newValue) {
//                   if (newValue != null) {
//                     setState(() {
//                       _rowsPerPage = newValue;
//                       _currentPage = 0;
//                     });
//                   }
//                 },
//                 items: [10, 20, 50, 100]
//                     .map<DropdownMenuItem<int>>((int value) {
//                   return DropdownMenuItem<int>(
//                     value: value,
//                     child: Text('$value 行/页'),
//                   );
//                 }).toList(),
//               ),
//               const SizedBox(width: 16),
//
//               // 页码控制
//               IconButton(
//                 icon: const Icon(Icons.first_page),
//                 onPressed: _currentPage > 0
//                     ? () {
//                   setState(() {
//                     _currentPage = 0;
//                   });
//                 }
//                     : null,
//               ),
//               IconButton(
//                 icon: const Icon(Icons.navigate_before),
//                 onPressed: _currentPage > 0
//                     ? () {
//                   setState(() {
//                     _currentPage--;
//                   });
//                 }
//                     : null,
//               ),
//               Text('${_currentPage + 1} / $pageCount'),
//               IconButton(
//                 icon: const Icon(Icons.navigate_next),
//                 onPressed: _currentPage < pageCount - 1
//                     ? () {
//                   setState(() {
//                     _currentPage++;
//                   });
//                 }
//                     : null,
//               ),
//               IconButton(
//                 icon: const Icon(Icons.last_page),
//                 onPressed: _currentPage < pageCount - 1
//                     ? () {
//                   setState(() {
//                     _currentPage = pageCount - 1;
//                   });
//                 }
//                     : null,
//               ),
//             ],
//           ),
//         ],
//       ),
//     );
//   }
//
//   /// 获取可用的数据列
//   List<DataColumn2> _getAvailableColumns() {
//     if (_filteredData.isEmpty || _selectedTarget == null) {
//       return [
//         DataColumn2(
//           label: const Text('时间'),
//           size: ColumnSize.M,
//           onSort: (columnIndex, ascending) {
//             setState(() {
//               _sortColumnIndex = columnIndex;
//               _sortAscending = ascending;
//               _loadData();
//             });
//           },
//         ),
//       ];
//     }
//
//     // 第一列始终是时间戳
//     final columns = <DataColumn2>[
//       DataColumn2(
//         label: const Text('时间'),
//         size: ColumnSize.M,
//         onSort: (columnIndex, ascending) {
//           setState(() {
//             _sortColumnIndex = columnIndex;
//             _sortAscending = ascending;
//             _loadData();
//           });
//         },
//       ),
//     ];
//
//     // 从数据中提取列名
//     if (_filteredData.isNotEmpty) {
//       final sampleRow = _filteredData.first;
//       int columnIndex = 1; // 时间戳是第0列
//
//       sampleRow.values.forEach((key, value) {
//         // 根据键的前缀确定列的显示名称
//         String columnName = key;
//         if (key.startsWith('env_')) {
//           columnName = '环境-${key.substring(4)}';
//         } else if (key.startsWith('imu_')) {
//           columnName = 'IMU-${key.substring(4)}';
//         }
//
//         columns.add(
//           DataColumn2(
//             label: Text(columnName),
//             size: value is num ? ColumnSize.S : ColumnSize.M,
//             numeric: value is num,
//             onSort: (columnIndex, ascending) {
//               setState(() {
//                 _sortColumnIndex = columnIndex;
//                 _sortAscending = ascending;
//                 _loadData();
//               });
//             },
//           ),
//         );
//         columnIndex++;
//       });
//     }
//
//     return columns;
//   }
//
//   /// 格式化时间戳显示
//   String _formatTimestamp(DateTime timestamp) {
//     return '${timestamp.year}-${timestamp.month.toString().padLeft(2, '0')}-${timestamp.day.toString().padLeft(2, '0')} '
//         '${timestamp.hour.toString().padLeft(2, '0')}:${timestamp.minute.toString().padLeft(2, '0')}:${timestamp.second.toString().padLeft(2, '0')}';
//   }
// }
//
// /// 时序数据行
// class TimeSeriesDataRow {
//   final String id;
//   final DateTime timestamp;
//   final Map<String, dynamic> values;
//
//   TimeSeriesDataRow({
//     required this.id,
//     required this.timestamp,
//     required this.values,
//   });
// }
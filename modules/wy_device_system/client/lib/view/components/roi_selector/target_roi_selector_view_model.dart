import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'dart:typed_data';
import 'dart:ui' as ui;
import 'dart:math' as math;

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:path_provider/path_provider.dart';

import '../../../bloc/targets_blocs.dart';
import '../../../bloc/web_socket_bloc.dart';
import '../../../bloc/wy_device_blocs.dart';
import '../../../model/target.dart';
import '../../../repository/target_repository.dart';
import 'package:utils/utils.dart';
import 'package:dio/dio.dart';

class ROISelectorViewModel {
  final TargetBloc bloc;
  final TargetRepository repository;
  final WebSocketBloc? webSocketBloc;
  final WyDeviceBloc wyDeviceBloc;
  int _selectedCameraId = 0;

  // 用于接收图片的流控制器
  final StreamController<ImageData> _imageStreamController =
  StreamController<ImageData>.broadcast();
  Stream<ImageData> get imageStream => _imageStreamController.stream;
  final Map<int, List<String>> _imageHistory = {};
  final int _maxHistoryPerCamera = 10;
  List<String> getCameraImageHistory(int cameraId) {
    return _imageHistory[cameraId] ?? [];
  }
  StreamSubscription? _webSocketSubscription;
  bool _hasLoadedImage = false;
  bool _defaultImageLoaded = false;

  int get selectedCameraId => _selectedCameraId;
  final _cameraChangeController = StreamController<int>.broadcast();
  Stream<int> get cameraChangeStream => _cameraChangeController.stream;

  ROISelectorViewModel({
    required this.bloc,
    required this.repository,
    required this.wyDeviceBloc,
    this.webSocketBloc,
  }) {

    _selectedCameraId = wyDeviceBloc.state.selectedCameraId;

    // 监听WyDeviceBloc的状态变化
    wyDeviceBloc.stream.listen((state) {
      if (_selectedCameraId != state.selectedCameraId) {
        _selectedCameraId = state.selectedCameraId;
        // 通知摄像头变更
        _cameraChangeController.add(_selectedCameraId);
      }
    });
    _initializeWebSocketListener();
  }

  Future<void> loadHistoryImage(int cameraId, {int index = 0}) async {
    final history = _imageHistory[cameraId] ?? [];
    if (history.isNotEmpty && index < history.length) {
      await loadImageFromFile(history[index], cameraId);
    } else {
      // 如果没有历史，尝试获取最新图像
      getLatestPicture();
    }
  }

  void _addToHistory(int cameraId, String path) {
    if (!_imageHistory.containsKey(cameraId)) {
      _imageHistory[cameraId] = [];
    }

    // 添加到历史的开头
    _imageHistory[cameraId]!.insert(0, path);

    // 限制历史大小
    if (_imageHistory[cameraId]!.length > _maxHistoryPerCamera) {
      _imageHistory[cameraId]!.removeLast();
    }
  }

  Future<void> getAllCamerasPictures(List<int> cameraIds) async {
    if (!_hasLoadedImage) {
      await loadDefaultImage();
    }

    try {
      for (int cameraId in cameraIds) {
        var rpc_map = {
          "method": "wyGetLatestPicture",
          "params": {
            "cameraId": cameraId,
            "resizeX": 640,
            "resizeY": 480,
            "inverted": false,
            "showROI": false,
          }
        };

        var result = await HttpUtil.instance.client.post(
          '/rpc',
          data: json.encode(rpc_map),
        );

        debugPrint("获取摄像头 $cameraId 的最新图片: ${result.data}");
      }
    } catch (e) {
      debugPrint('获取图片失败: $e');
      String errorMessage = e is DioException
          ? (e.response?.data != null
          ? (e.response?.data['msg'] ?? e.response?.data.toString())
          : e.message ?? e.toString())
          : e.toString();
      bloc.add(TargetErrorOccurred("获取图像失败:$errorMessage"));
    }
  }

  // 添加一个方法用于处理本地图像文件
  Future<void> loadImageFromFile(String path, int cameraId) async {
    try {
      final file = File(path);
      if (await file.exists()) {
        final bytes = await file.readAsBytes();
        final codec = await ui.instantiateImageCodec(bytes);
        final frame = await codec.getNextFrame();

        // 添加相机ID信息到流中传递的图像数据
        _imageStreamController.add(ImageData(
            frame.image,
            cameraId: cameraId,
            source: 'file',
            path: path
        ));
      } else {
        debugPrint('文件不存在: $path');
      }
    } catch (e) {
      debugPrint('加载图像文件失败: $e');
      loadBackupImage();
    }
  }

  void updateSelectedCameraId(int cameraId) {
    if (_selectedCameraId != cameraId) {
      _selectedCameraId = cameraId;
      // 同步到WyDeviceBloc
      wyDeviceBloc.add(SelectedCameraId(cameraId));
      // 通知变更
      _cameraChangeController.add(cameraId);
    }
  }

  void _initializeWebSocketListener() {
    if (webSocketBloc != null) {
      _webSocketSubscription = webSocketBloc!.stream.listen((state) {
        if (state is WebSocketMessageReceived) {
          _handleWebSocketMessage(state.message);
        }
      });
    }
  }

  // 添加初始化方法
  Future<void> initialize() async {
    // 立即加载默认图像，确保有初始显示内容
    await loadDefaultImage();
    // 然后尝试获取最新图片
    getLatestPicture();
  }



  void _handleWebSocketMessage(Map<String, dynamic> message) {
    if (message.containsKey('Telemetry') && message['Telemetry'] is Map<String, dynamic> &&
        (message['Telemetry'] as Map<String, dynamic>).containsKey('ImageData')) {
      _processImageData(message['Telemetry']['ImageData']);
    }
  }

  Future<void> _processImageData(Map<String, dynamic> data) async {
    try {
      var imageBase64 = data['base64'];
      int cameraId = data["cameraId"];

      //解析base64字符串
      Uint8List imageBytes = base64.decode(imageBase64);
      // 将字节数据解码为图片
      final ui.Codec codec = await ui.instantiateImageCodec(imageBytes);
      final ui.FrameInfo frameInfo = await codec.getNextFrame();

      // 将图片发送到流
      _imageStreamController.add(
        ImageData(
          frameInfo.image,
          cameraId: cameraId,
          source: 'websocket',
        ),
      );
      _hasLoadedImage = true;

      // 将图像保存到本地
      try {
        // 获取临时目录路径
        final directory = await getApplicationDocumentsDirectory();
        final timestamp = DateTime.now().millisecondsSinceEpoch;
        String deviceId = wyDeviceBloc.state.deviceId;

        String savePath = "${directory.path}/cameraImage/$deviceId/$cameraId";
        // 检查目录是否存在，如果不存在则创建
        final dir = Directory(savePath);
        if (!await dir.exists()) {
          await dir.create(recursive: true);
        }

        final filePath = '$savePath/${cameraId}_image_$timestamp.jpg';

        wyDeviceBloc.add(
          DevicePictureUpdate(
            cameraId,
            filePath,
          ),
        );


        // 将图像写入文件

        final file = File(filePath);
        await file.writeAsBytes(imageBytes);
        debugPrint('图像已保存到: $filePath');
        _addToHistory(cameraId, filePath);
      } catch (saveError) {
        debugPrint('保存图像到本地失败: $saveError');
      }


    } catch (e) {
      debugPrint('处理图片数据时出错: $e');
      // 加载失败时尝试加载默认图像
      if (!_hasLoadedImage) {
        loadDefaultImage();
      }
    }
  }




  Future<void> loadDefaultImage() async {
    loadBackupImage();
  }


  void loadBackupImage() async {
    try {
      const int width = 640;
      const int height = 480;

      final recorder = ui.PictureRecorder();
      final canvas = Canvas(recorder);

      // 创建更柔和的渐变背景
      final gradient = LinearGradient(
        begin: Alignment.topLeft,
        end: Alignment.bottomRight,
        colors: [Colors.blueGrey.shade100, Colors.blueGrey.shade200],
        stops: const [0.3, 0.9],
      );

      final rect = Rect.fromLTWH(0, 0, width.toDouble(), height.toDouble());
      canvas.drawRect(rect, Paint()..shader = gradient.createShader(rect));

      // 添加更精细的网格图案
      final gridPaint = Paint()
        ..color = Colors.white.withOpacity(0.2)
        ..strokeWidth = 0.5;

      // 大网格
      for (int i = 0; i < width; i += 40) {
        canvas.drawLine(Offset(i.toDouble(), 0), Offset(i.toDouble(), height.toDouble()), gridPaint);
      }
      for (int i = 0; i < height; i += 40) {
        canvas.drawLine(Offset(0, i.toDouble()), Offset(width.toDouble(), i.toDouble()), gridPaint);
      }

      // 小网格（更淡）
      final smallGridPaint = Paint()
        ..color = Colors.white.withOpacity(0.1)
        ..strokeWidth = 0.5;

      for (int i = 0; i < width; i += 10) {
        if (i % 40 != 0) { // 避免与大网格重叠
          canvas.drawLine(Offset(i.toDouble(), 0), Offset(i.toDouble(), height.toDouble()), smallGridPaint);
        }
      }
      for (int i = 0; i < height; i += 10) {
        if (i % 40 != 0) { // 避免与大网格重叠
          canvas.drawLine(Offset(0, i.toDouble()), Offset(width.toDouble(), i.toDouble()), smallGridPaint);
        }
      }

      // 添加圆角矩形作为加载指示背景
      final loadingBoxRect = Rect.fromCenter(
        center: Offset(width / 2, height / 2),
        width: 240,
        height: 70,
      );

      // 添加阴影效果
      canvas.drawRRect(
        RRect.fromRectAndRadius(loadingBoxRect.inflate(2), const Radius.circular(12)),
        Paint()..color = Colors.black.withOpacity(0.2)..maskFilter = const MaskFilter.blur(BlurStyle.normal, 8),
      );

      // 绘制主矩形
      canvas.drawRRect(
        RRect.fromRectAndRadius(loadingBoxRect, const Radius.circular(10)),
        Paint()..color = Colors.white.withOpacity(0.85),
      );

      // 添加渐变边框
      final borderGradient = LinearGradient(
        begin: Alignment.topLeft,
        end: Alignment.bottomRight,
        colors: [Colors.blueGrey.shade300, Colors.blueGrey.shade400],
      );

      final borderPaint = Paint()
        ..shader = borderGradient.createShader(loadingBoxRect)
        ..style = PaintingStyle.stroke
        ..strokeWidth = 1.5;

      canvas.drawRRect(
        RRect.fromRectAndRadius(loadingBoxRect, const Radius.circular(10)),
        borderPaint,
      );

      // 添加加载动画提示点
      final now = DateTime.now().millisecondsSinceEpoch / 300;

      for (int i = 0; i < 3; i++) {
        final dotRadius = 4.0 + 2.0 * math.sin((now + i * 1.5) % math.pi);
        canvas.drawCircle(
          Offset(
            loadingBoxRect.center.dx + (i - 1) * 20,
            loadingBoxRect.center.dy + 16,
          ),
          dotRadius,
          Paint()..color = Colors.blueGrey.shade400,
        );
      }

      // 添加加载文字
      const textStyle = TextStyle(
        color: Colors.blueGrey,
        fontSize: 18,
        fontWeight: FontWeight.w500,
        letterSpacing: 1.0,
      );

      final textSpan = TextSpan(
        text: '正在加载图像',
        style: textStyle,
      );

      final textPainter = TextPainter(
        text: textSpan,
        textAlign: TextAlign.center,
        textDirection: TextDirection.ltr,
      );

      textPainter.layout(minWidth: 0, maxWidth: width.toDouble());
      textPainter.paint(
          canvas,
          Offset(
            (width - textPainter.width) / 2,
            loadingBoxRect.center.dy - textPainter.height - 4,
          )
      );

      // 添加图像边框
      canvas.drawRect(
          rect.deflate(2),
          Paint()
            ..color = Colors.blueGrey.shade300
            ..style = PaintingStyle.stroke
            ..strokeWidth = 1
      );

      final picture = recorder.endRecording();
      final image = await picture.toImage(width, height);

      // 将图像发送到流
      _imageStreamController.add(
        ImageData(
          image,
          cameraId: _selectedCameraId,
          source: 'default',
        ),
      );
      _hasLoadedImage = true;
      _defaultImageLoaded = true;
    } catch (e) {
      debugPrint('创建备用图像失败: $e');
    }
  }


  List<Target> get targets => bloc.state.targets;
  String? get selectedTargetId => bloc.state.selectedTargetId;
  bool get isLoading => bloc.state.isLoading;
  bool get isDrawMode => bloc.state.isDrawMode;
  double get zoomLevel => bloc.state.zoomLevel;
  Offset get imageOffset => bloc.state.imageOffset;

  void selectTarget(String id) {
    bloc.add(TargetSelected(id));
  }

  Future<void> getLatestPicture() async {
    // 先加载默认图片，确保界面立即有图像显示
    if (!_hasLoadedImage) {
      await loadDefaultImage();
    }

    try {
      // 创建请求摄像头0的任务
      final task1 = HttpUtil.instance.client.post(
        '/rpc',
        data: json.encode({
          "method": "wyGetLatestPicture",
          "params": {
            "cameraId": 0,
            "resizeX": 640,
            "resizeY": 480,
            "inverted": false,
            "showROI": false,
          }
        }),
      );

      // 创建请求摄像头1的任务
      final task2 = HttpUtil.instance.client.post(
        '/rpc',
        data: json.encode({
          "method": "wyGetLatestPicture",
          "params": {
            "cameraId": 1,
            "resizeX": 640,
            "resizeY": 480,
            "inverted": false,
            "showROI": false,
          }
        }),
      );

      // 并行执行两个请求
      final results = await Future.wait([task1, task2], eagerError: true);
      debugPrint("获取最新图片完成: 摄像头0和摄像头1");

    } catch (e) {
      // 检查对象是否已经被销毁，如果已销毁则不处理错误
      if (_imageStreamController.isClosed) {
        debugPrint('ViewModel已经被销毁，忽略获取图片错误');
        return;
      }

      debugPrint('获取最新图片失败: $e');
      String errorMessage = e is DioException
          ? (e.response?.data != null
          ? (e.response?.data['msg'] ?? e.response?.data.toString())
          : e.message ?? e.toString())
          : e.toString();
      bloc.add(TargetErrorOccurred("获取图像失败:$errorMessage"));
    }
  }
  void updateTargetROI(String targetId, Rect newRect) {
    bloc.add(TargetROIMoved(targetId, newRect));
  }

  void deleteTarget(String targetId) {
    bloc.add(TargetDeleted(targetId));
  }

  void updateTarget(Target target) {
    bloc.add(TargetUpdated(target));
  }

  void setDrawMode(bool isDrawMode) {
    bloc.add(DrawModeChanged(isDrawMode));
  }

  void updateZoom(double level) {
    bloc.add(ZoomLevelChanged(level));
  }

  void updateImageOffset(Offset offset) {
    bloc.add(ImageOffsetChanged(offset));
  }


  void dispose() {
    _webSocketSubscription?.cancel();
    _imageStreamController.close();
    _cameraChangeController.close();
  }
}

class ImageData {
  final ui.Image image;
  final int cameraId;
  final String source;
  final String? path;

  ImageData(this.image, {
    required this.cameraId,
    this.source = 'websocket',
    this.path
  });
}
import 'dart:async';
import 'dart:io';
import 'dart:math' as math;

import 'package:client/view/components/roi_selector/target_roi_selector_view_model.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'dart:ui' as ui;
import 'package:flutter/services.dart';
import '../../../bloc/targets_blocs.dart';
import '../../../bloc/web_socket_bloc.dart';
import '../../../bloc/wy_device_blocs.dart';
import '../../../model/target.dart';
import '../../../repository/target_repository.dart';
import 'package:provider/provider.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:path_provider/path_provider.dart';
import 'package:flutter/rendering.dart';

enum OperationMode { draw, select }

class CanvasROISelector extends StatefulWidget {
  final String imagePath;
  final double canvasWidth;
  final double canvasHeight;
  final bool showTitles;
  final void Function(List<Map<String, dynamic>> roiData)? onROIsChanged;
  final double minWidthRect;
  final double minHeightRect;
  final double cornerSize;
  // 紧凑模式，用于嵌入到小尺寸界面时启用紧凑按钮和标靶展示
  final bool compactMode;
  final showTargetInfoCard;
  final TargetBloc targetBloc;
  final bool showROIs;
  final Function(String path)? onSaveCanvas;
  final WyDeviceBloc wyDeviceBloc;

  const CanvasROISelector({
    required this.imagePath,
    this.canvasWidth = 640,
    this.canvasHeight = 480,
    this.showTitles = true,
    this.onROIsChanged,
    this.minWidthRect = 10.0,
    this.minHeightRect = 10.0,
    this.cornerSize = 30,
    this.compactMode = false,
    this.showTargetInfoCard = true,
    this.showROIs = true,
    this.onSaveCanvas,
    required this.targetBloc,
    required this.wyDeviceBloc,
    Key? key,
  }) : super(key: key);

  @override
  _CanvasROISelectorState createState() => _CanvasROISelectorState();
}

class _CanvasROISelectorState extends State<CanvasROISelector> {
  final GlobalKey _canvasKey = GlobalKey();

  List<Map<String, dynamic>> roiRects = [];
  Rect? tempRect;
  Rect? selectedRect;
  String? selectedRectId;
  Offset? dragStartPoint;
  Offset? dragStartOffset;
  String? activeCorner;
  ui.Image? _image;
  Rect _imageDisplayRect = Rect.zero;
  bool _isDrawMode = false;
  late bool _showROIs;
  bool _isCalibrationMode = false;

  double _zoomLevel = 1.0;
  Offset _imageOffset = Offset.zero;

  // 紧凑模式下标靶区域用 PageView 展示
  late PageController _pageController;
  final TextEditingController _editingController = TextEditingController();
  late TargetBloc _targetBloc;
  late WyDeviceBloc _wyDeviceBloc;
  late ROISelectorViewModel modelView;
  StreamSubscription? _imageSubscription;


  @override
  void initState() {
    super.initState();

    TargetRepository repository = TargetRepositoryImpl(wyDeviceBloc: widget.wyDeviceBloc);
    final webSocketBloc = context.read<WebSocketBloc>();

    modelView = ROISelectorViewModel(
      bloc: widget.targetBloc,
      repository: repository,
      webSocketBloc: webSocketBloc,
      wyDeviceBloc: widget.wyDeviceBloc,
    );

    // 先订阅图片流
    _imageSubscription = modelView.imageStream.listen(_onImageReceived);

    // 立即使用备用图像初始化
    modelView.loadBackupImage();

    // 初始化 modelView 并加载默认图像
    modelView.initialize();

    _pageController = PageController();
    _showROIs = widget.showROIs;
    _checkCalibrationMode();

  }

  void _onImageReceived(ImageData  image) {
    setState(() {
      // 处理接收到的图像数据
      _image = image.image;
      _imageDisplayRect = _calculateImageDisplayRect(
        Size(widget.canvasWidth, widget.canvasHeight),
        _image!,
        _zoomLevel,
        _imageOffset,
      );
    });
  }

  @override
  void didUpdateWidget(CanvasROISelector oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.imagePath != widget.imagePath) {
      modelView.getLatestPicture();
      _zoomLevel = 1.0;
      _imageOffset = Offset.zero;
    }
  }

  Future<void> _saveCanvas() async {
    try {
      RenderRepaintBoundary boundary = _canvasKey.currentContext!
          .findRenderObject() as RenderRepaintBoundary;

      ui.Image image = await boundary.toImage(pixelRatio: 1.0);
      ByteData? byteData =
      await image.toByteData(format: ui.ImageByteFormat.png);

      if (byteData == null) {
        throw Exception('Failed to get image data');
      }

      Uint8List pngBytes = byteData.buffer.asUint8List();

      final directory = await _getStorageDirectory();
      final fileName = 'wy_canvas_${DateTime.now().millisecondsSinceEpoch}.png';
      final filePath = '${directory.path}/$fileName';

      final file = File(filePath);
      await file.writeAsBytes(pngBytes);

      if (widget.onSaveCanvas != null) {
        widget.onSaveCanvas!(filePath);
      }

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Image saved to: $filePath')),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Failed to save image: $e')),
      );
    }
  }

  // Method to update the image from external code
  void updateImage(String newImagePath) {
    modelView.getLatestPicture();
  }

  void _resetZoomAndPosition() {
    setState(() {
      _zoomLevel = 1.0;
      _imageOffset = Offset.zero;
    });
  }

  Future<Directory> _getStorageDirectory() async {
    if (Platform.isAndroid) {
      return await getExternalStorageDirectory() ??
          await getTemporaryDirectory();
    } else if (Platform.isIOS) {
      return await getApplicationDocumentsDirectory();
    } else if (Platform.isMacOS || Platform.isLinux || Platform.isWindows) {
      return await getDownloadsDirectory() ??
          await getApplicationDocumentsDirectory();
    }
    // Fallback
    return await getTemporaryDirectory();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    _targetBloc = widget.targetBloc;
    _wyDeviceBloc = widget.wyDeviceBloc;
    _checkCalibrationMode();
    _syncWithCurrentMode();

    _wyDeviceBloc.add(LoadHistoryImage());

  }

  @override
  void dispose() {
    _pageController.dispose();
    _editingController.dispose();
    _imageSubscription?.cancel();
    modelView.dispose();
    super.dispose();
  }

  void _toggleROIVisibility() {
    setState(() {
      _showROIs = !_showROIs;
    });
  }

  void _syncWithBloc() {
    final targets = _targetBloc.state.targets;
    setState(() {
      roiRects = targets.map((target) {
        return {
          'rect': target.rect,
          'label': target.name,
          'id': target.targetId,
        };
      }).toList();

      if (_targetBloc.state.selectedTargetId != null) {
        selectedRectId = _targetBloc.state.selectedTargetId;
        final selectedTarget = targets.firstWhere(
          (t) => t.targetId == selectedRectId,
          orElse: () => Target(targetId: '', name: '', rect: Rect.zero),
        );
        if (selectedTarget.targetId.isNotEmpty) {
          selectedRect = selectedTarget.rect;
        }
      }
    });
  }

  void _checkCalibrationMode() {
    final calibrationMode = widget.wyDeviceBloc.state.isFactoryCalibrationMode;
    setState(() {
      _isCalibrationMode = calibrationMode;
    });
  }

  void _syncWithCurrentMode() {
    if (_isCalibrationMode) {
      _syncWithWyDeviceBloc();
    } else {
      _syncWithTargetBloc();
    }
  }

  void _syncWithWyDeviceBloc() {
    final calibrationRect = widget.wyDeviceBloc.state.calibrationRect;
    setState(() {
      if (calibrationRect != null) {
        roiRects = [
          {
            'rect': calibrationRect,
            'label': 'Calibration',
            'id': 'calibration',
          }
        ];
        selectedRect = calibrationRect;
        selectedRectId = 'calibration';
      } else {
        roiRects = [];
        selectedRect = null;
        selectedRectId = null;
      }
    });
  }

  void _syncWithTargetBloc() {
    final targets = _targetBloc.state.targets;
    setState(() {
      roiRects = targets.map((target) {
        return {
          'cameraId': target.cameraId,
          'rect': target.rect,
          'label': target.name,
          'id': target.targetId,
        };
      }).toList();

      if (_targetBloc.state.selectedTargetId != null) {
        selectedRectId = _targetBloc.state.selectedTargetId;
        final selectedTarget = targets.firstWhere(
              (t) => t.targetId == selectedRectId,
          orElse: () => Target(targetId: '', name: '', rect: Rect.zero),
        );
        if (selectedTarget.targetId.isNotEmpty) {
          selectedRect = selectedTarget.rect;
        }
      }
    });
  }

  /// 控制面板，采取两种布局：
  /// 紧凑模式下采用一行 IconButton，加上 tooltip 提示；
  /// 非紧凑模式则采用 Wrap 展开所有按钮。
  Widget _buildControlPanel() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 8),
      child: SingleChildScrollView(
        scrollDirection: Axis.horizontal,
        child: Row(
          children: [
            // IconButton(
            //   icon: Icon(_isDrawMode ? Icons.create : Icons.select_all),
            //   tooltip: _isDrawMode ? '框选模式' : '选择模式',
            //   onPressed: () {
            //     setState(() {
            //       _isDrawMode = !_isDrawMode;
            //       selectedRect = null;
            //     });
            //   },
            // ),
            IconButton(
              icon: Icon(_showROIs ? Icons.visibility : Icons.visibility_off),
              onPressed: _toggleROIVisibility,
              tooltip: _showROIs ? '隐藏标靶框' : '显示标靶框',
            ),
            IconButton(
              icon: const Icon(Icons.zoom_in),
              tooltip: '放大',
              onPressed: _zoomIn,
            ),
            IconButton(
              icon: const Icon(Icons.zoom_out),
              tooltip: '缩小',
              onPressed: _zoomOut,
            ),
            IconButton(
              icon: const Icon(Icons.aspect_ratio),
              tooltip: '正常大小',
              onPressed: _normalSize,
            ),
            IconButton(
              icon: const Icon(Icons.save),
              onPressed: _saveCanvas,
              tooltip: '保存画面',
            ),
            // IconButton(
            //   icon: const Icon(Icons.find_in_page),
            //   tooltip: '识别标靶',
            //   onPressed: () {
            //     // 在这里添加识别标靶代码
            //   },
            // ),
            IconButton(
              icon: const Icon(Icons.image),
              tooltip: '请求图片',
              onPressed: _pickAndSwitchImage,
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _pickAndSwitchImage() async {
    await modelView.getLatestPicture();

    // final List<Map<String, dynamic>> currentROIs = List.from(roiRects);
    // final Size? currentImageSize = _image != null
    //     ? Size(_image!.width.toDouble(), _image!.height.toDouble())
    //     : null;


    // if (_image != null && currentImageSize != null && currentROIs.isNotEmpty) {
    //   final scaleX = _image!.width / currentImageSize.width;
    //   final scaleY = _image!.height / currentImageSize.height;
    //
    //   setState(() {
    //     for (var i = 0; i < roiRects.length; i++) {
    //       final oldRect = roiRects[i]['rect'] as Rect;
    //       roiRects[i]['rect'] = Rect.fromLTRB(
    //         oldRect.left * scaleX,
    //         oldRect.top * scaleY,
    //         oldRect.right * scaleX,
    //         oldRect.bottom * scaleY,
    //       );
    //     }
    //
    //     // Also update selected rect if needed
    //     if (selectedRect != null) {
    //       selectedRect = Rect.fromLTRB(
    //         selectedRect!.left * scaleX,
    //         selectedRect!.top * scaleY,
    //         selectedRect!.right * scaleX,
    //         selectedRect!.bottom * scaleY,
    //       );
    //     }
    //
    //     _zoomLevel = 1.0;
    //     _imageOffset = Offset.zero;
    //   });
    //
    //   for (var roi in roiRects) {
    //     final id = roi['id'] as String?;
    //     final rect = roi['rect'] as Rect;
    //     if (id != null) {
    //       _targetBloc.add(TargetROIMoved(id, rect));
    //     }
    //   }
    // }


  }


  @override
  Widget build(BuildContext context) {
    return MultiBlocListener(
      listeners: [
        BlocListener<TargetBloc, TargetState>(
          bloc: _targetBloc,
          listener: (context, state) {
            if (!_isCalibrationMode) {
              _syncWithTargetBloc();
            }
          },
          listenWhen: (previous, current) =>
          previous.targets != current.targets ||
              previous.selectedTargetId != current.selectedTargetId,
        ),
        BlocListener<WyDeviceBloc, WyDeviceState>(
          bloc: _wyDeviceBloc,
          listener: (context, state) {
            final wasCalibrationMode = _isCalibrationMode;
            _checkCalibrationMode();

            if (_isCalibrationMode || wasCalibrationMode != _isCalibrationMode) {
              _syncWithCurrentMode();
            }
          },
          listenWhen: (previous, current) =>
          previous.isFactoryCalibrationMode != current.isFactoryCalibrationMode,
        ),
      ],
      child: LayoutBuilder(
        builder: (context, constraints) {
          // 计算画布尺寸，基于可用空间和配置
          final availableWidth = constraints.maxWidth;
          final availableHeight = constraints.maxHeight;
          final canvasW = math.min(availableWidth, widget.canvasWidth);
          final canvasH = math.min(availableHeight, widget.canvasHeight);

          // 计算容器尺寸，基于图像纵横比
          var width = canvasW;
          var height = canvasH;

          if (_image != null) {
            final imageAspectRatio = _image!.width / _image!.height;
            if (width / height > imageAspectRatio) {
              width = height * imageAspectRatio;
            } else {
              height = width / imageAspectRatio;
            }

            _imageDisplayRect = _calculateImageDisplayRect(
              Size(width, height),
              _image!,
              _zoomLevel,
              _imageOffset,
            );
          }

          return Column(
            children: [
              Expanded(
                child: RepaintBoundary(
                  key: _canvasKey,
                  child: FittedBox(
                    fit: BoxFit.contain,
                    child: _buildImageContainer(width, height),
                  ),
                ),
              ),
              const SizedBox(height: 16),
              _buildControlPanel(),
            ],
          );
        },
      ),
    );
  }

  Widget _buildImageContainer(double width, double height) {
    return Container(
      width: width,
      height: height,
      decoration: BoxDecoration(
        border: Border.all(color: Colors.black12),
        color: Colors.grey[300],
      ),
      child: GestureDetector(
        onPanStart: _handlePanStart,
        onPanUpdate: (details) => _handlePanUpdate(details, Size(width, height)),
        onPanEnd: _handlePanEnd,
        child: SizedBox.expand(
          child: CustomPaint(
            painter: ROIPainter(
              image: _image,
              imageDisplayRect: _imageDisplayRect,
              roiRects: roiRects,
              tempRect: tempRect,
              selectedRect: selectedRect,
              showTitles: widget.showTitles,
              showROIs: _showROIs,
            ),
            size: Size(width, height),
          ),
        ),
      ),
    );
  }

  void _handlePanStart(DragStartDetails details) {
    final touchPosition = details.localPosition;
    if (!_showROIs) {
      // 当ROI隐藏时，仅跟踪图像平移的起始点
      dragStartPoint = touchPosition;
      return;
    }

    final imagePosition = _mapTouchToImage(details.localPosition);
    if (imagePosition == null) return;

    if (_isDrawMode) {
      setState(() {
        tempRect = Rect.fromCenter(
          center: imagePosition,
          width: widget.minWidthRect,
          height: widget.minHeightRect,
        );
      });
    } else {
      if (selectedRect != null) {
        activeCorner = _hitTestCorner(imagePosition, selectedRect!);
        if (activeCorner != null) {
          dragStartPoint = imagePosition;
          return;
        }
      }

      // 检查点击是否选中现有ROI
      for (final roi in roiRects.reversed) {
        final rect = roi['rect'] as Rect;
        final id = roi['id'] as String?;
        if (rect.contains(imagePosition)) {
          setState(() {
            selectedRect = rect;
            selectedRectId = id;
            dragStartPoint = imagePosition;
            dragStartOffset = Offset(rect.left, rect.top);
          });
          return;
        }
      }

      // 未点击任何ROI，清除选择
      setState(() {
        selectedRect = null;
        selectedRectId = null;
      });
    }
  }

  void _handlePanUpdate(DragUpdateDetails details, Size containerSize) {
    if (!_showROIs) {
      // 当ROI隐藏时，仅处理图像平移
      _updateImageOffset(details.delta, containerSize);
      return;
    }

    final imagePosition = _mapTouchToImage(details.localPosition);
    if (imagePosition == null) return;

    if (_isDrawMode && tempRect != null) {
      _updateTempRect(imagePosition);
    } else if (!_isDrawMode && selectedRect != null) {
      if (activeCorner != null && dragStartPoint != null) {
        _resizeSelectedRect(imagePosition);
      } else if (dragStartPoint != null) {
        _moveSelectedRect(imagePosition);
      }
    } else {
      _updateImageOffset(details.delta, containerSize);
    }
  }

  void _updateTempRect(Offset imagePosition) {
    setState(() {
      final newRect = Rect.fromPoints(
        Offset(tempRect!.left, tempRect!.top),
        imagePosition,
      );
      tempRect = Rect.fromLTRB(
        newRect.left,
        newRect.top,
        (newRect.width < widget.minWidthRect)
            ? newRect.left + widget.minWidthRect
            : newRect.right,
        (newRect.height < widget.minHeightRect)
            ? newRect.top + widget.minHeightRect
            : newRect.bottom,
      );
    });
  }

  void _resizeSelectedRect(Offset imagePosition) {
    setState(() {
      selectedRect = _resizeRect(
        selectedRect!,
        activeCorner!,
        dragStartPoint!,
        imagePosition,
      );
      selectedRect = _clampRectToImageBounds(selectedRect!);
      final index = roiRects.indexWhere((roi) => roi['id'] == selectedRectId);
      if (index != -1) {
        roiRects[index]['rect'] = selectedRect!;
      }
      dragStartPoint = imagePosition;
    });
  }

  void _moveSelectedRect(Offset imagePosition) {
    final delta = imagePosition - dragStartPoint!;
    setState(() {
      selectedRect = Rect.fromLTWH(
        dragStartOffset!.dx + delta.dx,
        dragStartOffset!.dy + delta.dy,
        selectedRect!.width,
        selectedRect!.height,
      );
      selectedRect = _clampRectToImageBounds(selectedRect!);
      final index = roiRects.indexWhere((roi) => roi['id'] == selectedRectId);
      if (index != -1) {
        roiRects[index]['rect'] = selectedRect!;
      }
    });
  }

  void _handlePanEnd(DragEndDetails details) {
    if (!_showROIs) {
      // 当ROI隐藏时，跳过ROI相关操作
      dragStartPoint = null;
      return;
    }

    if (_isDrawMode && tempRect != null) {
      _finalizeTempRect();
    } else if (!_isDrawMode) {
      // 更新选中的ROI
      widget.onROIsChanged?.call(roiRects);

      if (_isCalibrationMode && selectedRect != null) {
        widget.wyDeviceBloc.add(CalibrationRectChanged(selectedRect!));
      } else if (selectedRectId != null && selectedRect != null) {
        _targetBloc.add(TargetROIMoved(selectedRectId!, selectedRect!));
      }
    }

    // 清理状态
    dragStartPoint = null;
    dragStartOffset = null;
    activeCorner = null;
  }

  void _finalizeTempRect() {
    setState(() {
      if (_isCalibrationMode) {
        // 在校准模式下，只有一个ROI
        roiRects = [
          {
            'rect': tempRect!,
            'label': 'Calibration',
            'id': 'calibration',
          }
        ];
        widget.wyDeviceBloc.add(CalibrationRectChanged(tempRect!));
      } else {
        // 注：这里的创建标靶代码已被注释
        // roiRects.add({
        //   'rect': tempRect!,
        //   'label': 'T${roiRects.length + 1}',
        // });
        // _targetBloc.add(TargetCreated(tempRect!));
      }
      tempRect = null;
      widget.onROIsChanged?.call(roiRects);
    });
  }
  Rect _clampRectToImageBounds(Rect rect) {
    final double left = rect.left.clamp(0.0, _image!.width.toDouble());
    final double top = rect.top.clamp(0.0, _image!.height.toDouble());
    final double right =
        (left + rect.width).clamp(0.0, _image!.width.toDouble());
    final double bottom =
        (top + rect.height).clamp(0.0, _image!.height.toDouble());
    if (left == 0 && top == 0) {
      return Rect.fromLTWH(left, top, rect.width, rect.height);
    } else if (left == 0 && bottom == _image!.height.toDouble()) {
      return Rect.fromLTWH(left, bottom - rect.height, rect.width, rect.height);
    } else if (right == _image!.width.toDouble() && top == 0) {
      return Rect.fromLTWH(right - rect.width, top, rect.width, rect.height);
    } else if (right == _image!.width.toDouble() &&
        bottom == _image!.height.toDouble()) {
      return Rect.fromLTWH(
          right - rect.width, bottom - rect.height, rect.width, rect.height);
    } else if (left == 0) {
      return Rect.fromLTWH(left, top, rect.width, rect.height);
    } else if (right == _image!.width.toDouble()) {
      return Rect.fromLTWH(right - rect.width, top, rect.width, rect.height);
    } else if (top == 0) {
      return Rect.fromLTWH(left, top, rect.width, rect.height);
    } else if (bottom == _image!.height.toDouble()) {
      return Rect.fromLTWH(left, bottom - rect.height, rect.width, rect.height);
    } else {
      return Rect.fromLTWH(left, top, rect.width, rect.height);
    }
  }

  Offset? _mapTouchToImage(Offset touch) {
    if (_image == null || !_imageDisplayRect.contains(touch)) {
      return null;
    }
    final dx = (touch.dx - _imageDisplayRect.left) /
        _imageDisplayRect.width *
        _image!.width;
    final dy = (touch.dy - _imageDisplayRect.top) /
        _imageDisplayRect.height *
        _image!.height;
    return Offset(dx, dy);
  }

  String? _hitTestCorner(Offset position, Rect rect) {
    double cornerSize = widget.cornerSize * 1.5;
    if (position.dx >= rect.bottomRight.dx &&
        position.dx <= rect.bottomRight.dx + cornerSize &&
        position.dy >= rect.bottomRight.dy &&
        position.dy <= rect.bottomRight.dy + cornerSize) {
      return 'bottomRight';
    }
    return null;
  }

  Rect _resizeRect(Rect rect, String corner, Offset start, Offset end) {
    final double minWidth = widget.minWidthRect;
    final double minHeight = widget.minHeightRect;
    double left = rect.left;
    double top = rect.top;
    double right = rect.right;
    double bottom = rect.bottom;
    switch (corner) {
      case 'topLeft':
        left =
            (rect.left + (end.dx - start.dx)).clamp(0.0, rect.right - minWidth);
        top = (rect.top + (end.dy - start.dy))
            .clamp(0.0, rect.bottom - minHeight);
        break;
      case 'topRight':
        right = (rect.right + (end.dx - start.dx))
            .clamp(rect.left + minWidth, _image!.width.toDouble());
        top = (rect.top + (end.dy - start.dy))
            .clamp(0.0, rect.bottom - minHeight);
        break;
      case 'bottomLeft':
        left =
            (rect.left + (end.dx - start.dx)).clamp(0.0, rect.right - minWidth);
        bottom = (rect.bottom + (end.dy - start.dy))
            .clamp(rect.top + minHeight, _image!.height.toDouble());
        break;
      case 'bottomRight':
        right = (rect.right + (end.dx - start.dx))
            .clamp(rect.left + minWidth, _image!.width.toDouble());
        bottom = (rect.bottom + (end.dy - start.dy))
            .clamp(rect.top + minHeight, _image!.height.toDouble());
        break;
    }
    return Rect.fromLTRB(left, top, right, bottom);
  }

  void _zoomIn() {
    setState(() {
      _zoomLevel *= 1.1;
      _imageDisplayRect = _calculateImageDisplayRect(
        Size(widget.canvasWidth, widget.canvasHeight),
        _image!,
        _zoomLevel,
        _imageOffset,
      );
    });
  }

  void _zoomOut() {
    setState(() {
      _zoomLevel = (_zoomLevel / 1.1).clamp(1.0, double.infinity);
      _imageDisplayRect = _calculateImageDisplayRect(
        Size(widget.canvasWidth, widget.canvasHeight),
        _image!,
        _zoomLevel,
        _imageOffset,
      );
    });
  }

  void _normalSize() {
    setState(() {
      _zoomLevel = 1.0;
      _imageOffset = Offset.zero;
      _imageDisplayRect = _calculateImageDisplayRect(
        Size(widget.canvasWidth, widget.canvasHeight),
        _image!,
        _zoomLevel,
        _imageOffset,
      );
    });
  }

  Rect _calculateImageDisplayRect(Size canvasSize, ui.Image image,
      [double zoomLevel = 1.0, Offset offset = Offset.zero]) {
    final imageAspectRatio = image.width / image.height;
    final canvasAspectRatio = canvasSize.width / canvasSize.height;
    double displayWidth, displayHeight;
    if (imageAspectRatio > canvasAspectRatio) {
      displayWidth = canvasSize.width * zoomLevel;
      displayHeight = displayWidth / imageAspectRatio;
    } else {
      displayHeight = canvasSize.height * zoomLevel;
      displayWidth = displayHeight * imageAspectRatio;
    }
    final centerX = (canvasSize.width - displayWidth) / 2 + offset.dx;
    final centerY = (canvasSize.height - displayHeight) / 2 + offset.dy;
    return Rect.fromLTWH(centerX, centerY, displayWidth, displayHeight);
  }

  void _updateImageOffset(Offset delta, Size containerSize) {
    final newOffset = _imageOffset + delta;
    final imageAspectRatio = _image!.width / _image!.height;
    double scaledWidth, scaledHeight;
    if (imageAspectRatio > containerSize.width / containerSize.height) {
      scaledWidth = containerSize.width * _zoomLevel;
      scaledHeight = scaledWidth / imageAspectRatio;
    } else {
      scaledHeight = containerSize.height * _zoomLevel;
      scaledWidth = scaledHeight * imageAspectRatio;
    }
    final maxOffsetX =
        ((scaledWidth - containerSize.width) / 2).clamp(0.0, double.infinity) +
            containerSize.width / 2;
    final maxOffsetY = ((scaledHeight - containerSize.height) / 2)
            .clamp(0.0, double.infinity) +
        containerSize.height / 2;
    setState(() {
      _imageOffset = Offset(
        newOffset.dx.clamp(-maxOffsetX, maxOffsetX),
        newOffset.dy.clamp(-maxOffsetY, maxOffsetY),
      );
      _imageDisplayRect = _calculateImageDisplayRect(
        containerSize,
        _image!,
        _zoomLevel,
        _imageOffset,
      );
    });
  }
}

class ROIPainter extends CustomPainter {
  final ui.Image? image;
  final Rect imageDisplayRect;
  final List<Map<String, dynamic>> roiRects;
  final Rect? tempRect;
  final Rect? selectedRect;
  final bool showTitles;
  final bool showROIs;
  final int maxLabelLength;

  ROIPainter({
    required this.image,
    required this.imageDisplayRect,
    required this.roiRects,
    this.tempRect,
    this.selectedRect,
    required this.showTitles,
    this.showROIs = true,
    this.maxLabelLength = 10,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint();
    canvas.clipRect(Rect.fromLTWH(0, 0, size.width, size.height));
    if (image != null) {
      canvas.drawImageRect(
        image!,
        Rect.fromLTWH(0, 0, image!.width.toDouble(), image!.height.toDouble()),
        imageDisplayRect,
        paint,
      );
    }

    if (showROIs) {
      final roiPaint = Paint()
        ..color = Colors.red
        ..style = PaintingStyle.stroke
        ..strokeWidth = 2;
      final selectedPaint = Paint()
        ..color = Colors.blue
        ..style = PaintingStyle.stroke
        ..strokeWidth = 2;
      final cornerPaint = Paint()
        ..color = Colors.blue
        ..style = PaintingStyle.fill;
      for (final roi in roiRects) {
        final rect = roi['rect'] as Rect;

        final mappedRect = _mapImageRectToDisplay(rect, imageDisplayRect);
        canvas.drawRect(
            mappedRect, rect == selectedRect ? selectedPaint : roiPaint);
        if (rect == selectedRect) {
          const double cornerSize = 30;
          final path = Path()
            ..moveTo(mappedRect.bottomRight.dx, mappedRect.bottomRight.dy)
            ..lineTo(mappedRect.bottomRight.dx + cornerSize,
                mappedRect.bottomRight.dy)
            ..lineTo(mappedRect.bottomRight.dx,
                mappedRect.bottomRight.dy + cornerSize)
            ..close();
          canvas.drawPath(
              path, cornerPaint..color = Colors.blue.withOpacity(0.5));
        }

        if (showTitles) {
          final label = roi['label'] as String;
          final displayLabel = label.length > maxLabelLength
              ? '${label.substring(0, maxLabelLength)}...'
              : label;
          final textSpan = TextSpan(
            text: displayLabel,
            style: const TextStyle(
              color: Colors.green,
              fontSize: 12,
              fontWeight: FontWeight.bold,
              shadows: [
                Shadow(
                  offset: Offset(1.0, 1.0),
                  blurRadius: 3.0,
                  color: Colors.black,
                ),
              ],
            ),
          );
          final textPainter =
              TextPainter(text: textSpan, textDirection: TextDirection.ltr);
          textPainter.layout();

          // 计算标签位置
          double labelX = mappedRect.left;
          double labelY = mappedRect.top - textPainter.height - 2;

          // 检查标签是否超出上边界
          if (labelY < imageDisplayRect.top) {
            labelY = mappedRect.bottom + 2; // 移动到框的底部
          }

          // 检查标签是否超出右边界
          if (labelX + textPainter.width > imageDisplayRect.right) {
            labelX = mappedRect.right - textPainter.width;
          }

          // 检查标签是否超出左边界
          if (labelX < imageDisplayRect.left) {
            labelX = imageDisplayRect.left;
          }

          textPainter.paint(canvas, Offset(labelX, labelY));
        }
      }

      if (tempRect != null) {
        canvas.drawRect(
            _mapImageRectToDisplay(tempRect!, imageDisplayRect), roiPaint);
      }
    }
  }

  Rect _mapImageRectToDisplay(Rect imageRect, Rect displayRect) {
    final scaleX = displayRect.width / image!.width;
    final scaleY = displayRect.height / image!.height;
    return Rect.fromLTRB(
      displayRect.left + imageRect.left * scaleX,
      displayRect.top + imageRect.top * scaleY,
      displayRect.left + imageRect.right * scaleX,
      displayRect.top + imageRect.bottom * scaleY,
    );
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}

import 'dart:async';
import 'dart:io';
import 'dart:math' as math;
import 'dart:typed_data';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'dart:ui' as ui;
import 'package:flutter/services.dart';
import '../../../bloc/targets_blocs.dart';
import '../../../bloc/web_socket_bloc.dart';
import '../../../bloc/wy_device_blocs.dart';
import '../../../model/target.dart';
import '../../../repository/target_repository.dart';
import 'package:provider/provider.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:path_provider/path_provider.dart';
import 'package:flutter/rendering.dart';
import 'target_roi_selector_view_model.dart';

/// 操作模式枚举
enum OperationMode { draw, select }

/// 视图模式枚举
enum ViewMode { single, tiled }

/// 多摄像头ROI选择器组件
class MultiCameraROISelector extends StatefulWidget {
  final String imagePath;
  final double canvasWidth;
  final double canvasHeight;
  final bool showTitles;
  final void Function(List<Map<String, dynamic>> roiData)? onROIsChanged;
  final double minWidthRect;
  final double minHeightRect;
  final double cornerSize;
  final bool compactMode;
  final bool showTargetInfoCard;
  final TargetBloc targetBloc;
  final bool showROIs;
  final Function(String path)? onSaveCanvas;
  final WyDeviceBloc wyDeviceBloc;

  /// 新增的多摄像头支持参数
  final List<int> availableCameraIds;
  final Function(int cameraId)? onCameraChanged;

  const MultiCameraROISelector({
    Key? key,
    required this.imagePath,
    this.canvasWidth = 640,
    this.canvasHeight = 480,
    this.showTitles = true,
    this.onROIsChanged,
    this.minWidthRect = 10.0,
    this.minHeightRect = 10.0,
    this.cornerSize = 30,
    this.compactMode = false,
    this.showTargetInfoCard = true,
    this.showROIs = true,
    this.onSaveCanvas,
    required this.targetBloc,
    required this.wyDeviceBloc,
    this.availableCameraIds = const [0, 1],
    this.onCameraChanged,
  }) : super(key: key);

  @override
  _MultiCameraROISelectorState createState() => _MultiCameraROISelectorState();
}

class _MultiCameraROISelectorState extends State<MultiCameraROISelector> {
  final GlobalKey _canvasKey = GlobalKey();

  // ROI 相关数据
  List<Map<String, dynamic>> roiRects = [];
  Rect? tempRect;
  Rect? selectedRect;
  String? selectedRectId;
  Offset? dragStartPoint;
  Offset? dragStartOffset;
  String? activeCorner;

  // 图像相关数据
  Map<int, ui.Image?> _cameraImages = {};
  Rect _imageDisplayRect = Rect.zero;

  // 操作状态
  bool _isDrawMode = false;
  late bool _showROIs;
  bool _isCalibrationMode = false;

  // 视图模式
  ViewMode _viewMode = ViewMode.single;
  int _selectedCameraId = 0;

  // 主视图缩放和平移
  double _zoomLevel = 1.0;
  Offset _imageOffset = Offset.zero;

  // 拼接视图相关
  final Map<int, GlobalKey> _tileKeys = {};
  final Map<int, Rect> _tileRects = {};

  // 控制器
  late PageController _pageController;
  final TextEditingController _editingController = TextEditingController();
  late TargetBloc _targetBloc;
  late WyDeviceBloc _wyDeviceBloc;
  late ROISelectorViewModel _modelView;
  StreamSubscription? _imageSubscription;

  Map<int, String?> _cameraSelectedRectIds = {};




  @override
  void initState() {
    super.initState();

    TargetRepository repository = TargetRepositoryImpl(wyDeviceBloc: widget.wyDeviceBloc);
    final webSocketBloc = context.read<WebSocketBloc>();

    _modelView = ROISelectorViewModel(
      bloc: widget.targetBloc,
      repository: repository,
      webSocketBloc: webSocketBloc,
      wyDeviceBloc: widget.wyDeviceBloc,
    );

    _selectedCameraId = _modelView.selectedCameraId;
    _modelView.cameraChangeStream.listen((cameraId) {
      if (_selectedCameraId != cameraId) {
        setState(() {
          _selectedCameraId = cameraId;

          // 重置缩放和位置
          _zoomLevel = 1.0;
          _imageOffset = Offset.zero;

          if (_cameraImages[cameraId] != null) {
            _imageDisplayRect = _calculateImageDisplayRect(
              Size(widget.canvasWidth, widget.canvasHeight),
              _cameraImages[cameraId]!,
              _zoomLevel,
              _imageOffset,
            );
          }
        });
      }
    });

    // 为每个摄像头初始化
    // for (int cameraId in widget.availableCameraIds) {
    //   _tileKeys[cameraId] = GlobalKey();
    //   _cameraImages[cameraId] = null;
    //
    //   // 直接订阅ViewModel的流，不创建额外的控制器
    //   _imageSubscriptions[cameraId] = _modelView.imageStream.listen((image) {
    //     if (_selectedCameraId == cameraId || _viewMode == ViewMode.tiled) {
    //       _onImageReceived(image.image, cameraId);
    //     }
    //   });
    //
    //   // 初始化拼贴矩形
    //   _tileRects[cameraId] = Rect.zero;
    // }

    _imageSubscription = _modelView.imageStream.listen((imageData) {
      // 对所有图像进行处理
      setState(() {
        _cameraImages[imageData.cameraId] = imageData.image;

        // 如果是当前选中的摄像头或拼贴模式，更新显示
        if (_selectedCameraId == imageData.cameraId || _viewMode == ViewMode.tiled) {
          _updateImageDisplayRect(imageData.cameraId);
        }

        // 如果是从文件加载的图像，可以进行额外处理
        if (imageData.source == 'file') {
          // 处理本地文件图像
        }
      });
    });

    // 设置初始选中的摄像头
    _selectedCameraId =
    widget.availableCameraIds.isNotEmpty ? widget.availableCameraIds[0] : 0;

    // 加载备用图像
    _modelView.loadBackupImage();

    // 初始化视图模型并加载默认图像
    _modelView.initialize();

    _pageController = PageController();
    _showROIs = widget.showROIs;
    _checkCalibrationMode();
  }

  void _onImageReceived(ui.Image image, int cameraId) {
    setState(() {
      _cameraImages[cameraId] = image;

      if (cameraId == _selectedCameraId) {
        _imageDisplayRect = _calculateImageDisplayRect(
          Size(widget.canvasWidth, widget.canvasHeight),
          image,
          _zoomLevel,
          _imageOffset,
        );
      }

      // 更新拼贴矩形
      _updateTileRect(cameraId);
    });

  }

  void _updateImageDisplayRect(int cameraId) {
    if (_cameraImages[cameraId] != null) {
      if (cameraId == _selectedCameraId) {
        _imageDisplayRect = _calculateImageDisplayRect(
          Size(widget.canvasWidth, widget.canvasHeight),
          _cameraImages[cameraId]!,
          _zoomLevel,
          _imageOffset,
        );
      }

      // 更新拼贴矩形
      _updateTileRect(cameraId);
    }
  }

  void _updateTileRect(int cameraId) {
    if (_cameraImages[cameraId] != null) {
      setState(() {
        // 将在布局期间计算
        _tileRects[cameraId] = Rect.zero;
      });
    }
  }

  @override
  void didUpdateWidget(MultiCameraROISelector oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.imagePath != widget.imagePath) {
      _modelView.getLatestPicture();
      _zoomLevel = 1.0;
      _imageOffset = Offset.zero;
    }

    // 检查摄像头列表变化
    if (oldWidget.availableCameraIds != widget.availableCameraIds) {
      _updateCameraSubscriptions();
    }
  }

  void _updateCameraSubscriptions() {
    // 确保选中的摄像头在可用列表中
    if (!widget.availableCameraIds.contains(_selectedCameraId) &&
        widget.availableCameraIds.isNotEmpty) {
      _selectedCameraId = widget.availableCameraIds[0];
      _modelView.getLatestPicture();
    }
  }

  Future<void> _saveCanvas() async {
    try {
      RenderRepaintBoundary boundary = _canvasKey.currentContext!
          .findRenderObject() as RenderRepaintBoundary;

      ui.Image image = await boundary.toImage(pixelRatio: 1.0);
      ByteData? byteData =
      await image.toByteData(format: ui.ImageByteFormat.png);

      if (byteData == null) {
        throw Exception('Failed to get image data');
      }

      Uint8List pngBytes = byteData.buffer.asUint8List();

      final directory = await _getStorageDirectory();
      final fileName = 'wy_canvas_${DateTime
          .now()
          .millisecondsSinceEpoch}.png';
      final filePath = '${directory.path}/$fileName';

      final file = File(filePath);
      await file.writeAsBytes(pngBytes);

      if (widget.onSaveCanvas != null) {
        widget.onSaveCanvas!(filePath);
      }

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('图像已保存到: $filePath')),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('保存图像失败: $e')),
      );
    }
  }

  // 外部代码更新图像的方法
  void updateImage(String newImagePath) {
    _modelView.getLatestPicture();
  }

  void _resetZoomAndPosition() {
    setState(() {
      _zoomLevel = 1.0;
      _imageOffset = Offset.zero;

      if (_cameraImages[_selectedCameraId] != null) {
        _imageDisplayRect = _calculateImageDisplayRect(
          Size(widget.canvasWidth, widget.canvasHeight),
          _cameraImages[_selectedCameraId]!,
          _zoomLevel,
          _imageOffset,
        );
      }
    });
  }

  Future<Directory> _getStorageDirectory() async {
    if (Platform.isAndroid) {
      return await getExternalStorageDirectory() ??
          await getTemporaryDirectory();
    } else if (Platform.isIOS) {
      return await getApplicationDocumentsDirectory();
    } else if (Platform.isMacOS || Platform.isLinux || Platform.isWindows) {
      return await getDownloadsDirectory() ??
          await getApplicationDocumentsDirectory();
    }
    // 回退
    return await getTemporaryDirectory();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    _targetBloc = widget.targetBloc;
    _wyDeviceBloc = widget.wyDeviceBloc;
    _checkCalibrationMode();
    _syncWithCurrentMode();

    _wyDeviceBloc.add(LoadHistoryImage());
  }

  @override
  void dispose() {
    _pageController.dispose();
    _editingController.dispose();
    _imageSubscription?.cancel();
    _modelView.dispose();
    super.dispose();
  }
// 添加一个历史图像弹出菜单
// 通用的历史按钮构建方法，可以根据大小调整显示
  Widget _buildHistoryButton(int cameraId, {bool compact = false}) {
    final history = _modelView.getCameraImageHistory(cameraId);

    return PopupMenuButton<int>(
      icon: Icon(
        Icons.history,
        size: compact ? 16 : 24,
      ),
      tooltip: '查看历史图像',
      onSelected: (index) {
        _modelView.loadHistoryImage(cameraId, index: index);
      },
      itemBuilder: (context) {
        if (history.isEmpty) {
          return [
            const PopupMenuItem<int>(
              enabled: false,
              value: -1,
              child: Text('没有历史图像'),
            ),
          ];
        }

        return List.generate(
          history.length,
              (index) => PopupMenuItem<int>(
            value: index,
            child: Row(
              children: [
                Icon(Icons.photo, size: 16),
                SizedBox(width: 8),
                Text(
                  compact ? '历史 ${index + 1}' : '历史图像 ${index + 1}',
                  style: TextStyle(fontSize: compact ? 12 : 14),
                ),
              ],
            ),
          ),
        );
      },
    );
  }  void _toggleROIVisibility() {
    setState(() {
      _showROIs = !_showROIs;
    });
  }

  void _toggleViewMode() {
    setState(() {
      _viewMode =
      _viewMode == ViewMode.single ? ViewMode.tiled : ViewMode.single;

      // 切换模式时重置缩放和位置
      if (_viewMode == ViewMode.single) {
        _zoomLevel = 1.0;
        _imageOffset = Offset.zero;

        if (_cameraImages[_selectedCameraId] != null) {
          _imageDisplayRect = _calculateImageDisplayRect(
            Size(widget.canvasWidth, widget.canvasHeight),
            _cameraImages[_selectedCameraId]!,
            _zoomLevel,
            _imageOffset,
          );
        }
      }
    });
  }

  // 修改_switchCamera方法，确保切换摄像头时更新ROI数据
  void _switchCamera(int cameraId) {
    if (_selectedCameraId != cameraId) {
      // 保存当前摄像头的选中状态
      _cameraSelectedRectIds[_selectedCameraId] = selectedRectId;

      // 更新摄像头ID
      _selectedCameraId = cameraId;

      // 通过ViewModel更新摄像头ID
      _modelView.updateSelectedCameraId(cameraId);

      // 通知父级摄像头变更
      widget.onCameraChanged?.call(cameraId);

      // 请求此摄像头的最新图像
      _modelView.getLatestPicture();

      // 同步当前摄像头的ROI数据
      _syncWithCurrentMode();
    }
  }
  void _syncWithBloc() {
    final targets = _targetBloc.state.targets;
    setState(() {
      roiRects = targets.map((target) {
        return {
          'rect': target.rect,
          'label': target.name,
          'id': target.targetId,
        };
      }).toList();

      if (_targetBloc.state.selectedTargetId != null) {
        selectedRectId = _targetBloc.state.selectedTargetId;
        final selectedTarget = targets.firstWhere(
              (t) => t.targetId == selectedRectId,
          orElse: () => Target(targetId: '', name: '', rect: Rect.zero),
        );
        if (selectedTarget.targetId.isNotEmpty) {
          selectedRect = selectedTarget.rect;
        }
      }
    });
  }

  void _checkCalibrationMode() {
    final calibrationMode = widget.wyDeviceBloc.state.isFactoryCalibrationMode;
    setState(() {
      _isCalibrationMode = calibrationMode;
    });
  }

  void _syncWithCurrentMode() {
    if (_isCalibrationMode) {
      _syncWithWyDeviceBloc();
    } else {
      _syncWithTargetBloc();
    }
  }

  void _syncWithWyDeviceBloc() {
    final calibrationRect = widget.wyDeviceBloc.state.calibrationRect;
    setState(() {
      if (calibrationRect != null) {
        roiRects = [
          {
            'rect': calibrationRect,
            'label': 'Calibration',
            'id': 'calibration',
          }
        ];
        selectedRect = calibrationRect;
        selectedRectId = 'calibration';
      } else {
        roiRects = [];
        selectedRect = null;
        selectedRectId = null;
      }
    });
  }

  void _syncWithTargetBloc() {
    final targets = _targetBloc.state.targets;

    // 当标靶被选中时
    if (_targetBloc.state.selectedTargetId != null) {
      final selectedTargetId = _targetBloc.state.selectedTargetId;

      // 查找被选中的标靶
      final selectedTarget = targets.firstWhere(
            (t) => t.targetId == selectedTargetId,
        orElse: () => Target(targetId: '', name: '', rect: Rect.zero),
      );

      // 如果找到有效标靶且其摄像头ID不是当前选中的摄像头
      if (selectedTarget.targetId.isNotEmpty &&
          selectedTarget.cameraId != _selectedCameraId &&
          selectedTarget.cameraId != null) {

        // 切换到标靶所属的摄像头
        _switchCamera(selectedTarget.cameraId!);

        // 更新选中状态 - 保留这个状态更新
        setState(() {
          selectedRectId = selectedTargetId;
          selectedRect = selectedTarget.rect;
        });
        return; // 提前返回，因为_switchCamera会再次调用_syncWithCurrentMode
      }
    }

    // 更新当前摄像头的ROI列表
    setState(() {
      // 筛选当前摄像头的目标
      final currentCameraTargets = targets.where(
              (target) => target.cameraId == _selectedCameraId
      ).toList();

      roiRects = currentCameraTargets.map((target) {
        return {
          'rect': target.rect,
          'label': target.name,
          'id': target.targetId,
        };
      }).toList();

      // 处理选中目标
      if (_targetBloc.state.selectedTargetId != null) {
        // 检查选中的目标是否属于当前摄像头
        final selectedTarget = targets.firstWhere(
              (t) => t.targetId == _targetBloc.state.selectedTargetId &&
              t.cameraId == _selectedCameraId,
          orElse: () => Target(targetId: '', name: '', rect: Rect.zero),
        );

        if (selectedTarget.targetId.isNotEmpty) {
          // 只有当目标属于当前摄像头时才更新选中状态
          selectedRectId = _targetBloc.state.selectedTargetId;
          selectedRect = selectedTarget.rect;
        }
      }
    });
  }
  /// 控制面板，带有ROI操作和视图模式的按钮
  Widget _buildControlPanel() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 8),
      child: SingleChildScrollView(
        scrollDirection: Axis.horizontal,
        child: Row(
          children: [
            // 视图模式切换按钮
            IconButton(
              icon: Icon(_viewMode == ViewMode.single ? Icons.grid_view : Icons
                  .fullscreen),
              tooltip: _viewMode == ViewMode.single
                  ? '拼接视图'
                  : '单摄像头视图',
              onPressed: _toggleViewMode,
            ),
            // 历史图像按钮 - 为当前选中的摄像头显示历史
            // _buildHistoryButton(_selectedCameraId),

            // 显示/隐藏ROI切换
            IconButton(
              icon: Icon(_showROIs ? Icons.visibility : Icons.visibility_off),
              onPressed: _toggleROIVisibility,
              tooltip: _showROIs ? '隐藏标靶框' : '显示标靶框',
            ),
            // 绘制模式切换
            // IconButton(
            //   icon: Icon(_isDrawMode ? Icons.create : Icons.select_all),
            //   tooltip: _isDrawMode ? '框选模式' : '选择模式',
            //   onPressed: () {
            //     setState(() {
            //       _isDrawMode = !_isDrawMode;
            //       selectedRect = null;
            //     });
            //   },
            // ),
            // 缩放控制（仅在单视图模式下）
            if (_viewMode == ViewMode.single) ...[
              IconButton(
                icon: const Icon(Icons.zoom_in),
                tooltip: '放大',
                onPressed: _zoomIn,
              ),
              IconButton(
                icon: const Icon(Icons.zoom_out),
                tooltip: '缩小',
                onPressed: _zoomOut,
              ),
              IconButton(
                icon: const Icon(Icons.aspect_ratio),
                tooltip: '正常大小',
                onPressed: _normalSize,
              ),
            ],
            // 保存画布按钮
            IconButton(
              icon: const Icon(Icons.save),
              onPressed: _saveCanvas,
              tooltip: '保存画面',
            ),
            // 请求最新图像
            IconButton(
              icon: const Icon(Icons.refresh),
              tooltip: '请求图片',
              onPressed: _pickAndSwitchImage,
            ),
            // 摄像头选择器（仅在单视图模式下）
            if (_viewMode == ViewMode.single) ...[
              const SizedBox(width: 8),
              const VerticalDivider(width: 1, thickness: 1),
              const SizedBox(width: 8),
              ...widget.availableCameraIds.map((cameraId) =>
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 4),
                    child: ElevatedButton(
                      style: ElevatedButton.styleFrom(
                        backgroundColor: _selectedCameraId == cameraId
                            ? Theme
                            .of(context)
                            .colorScheme
                            .primary
                            : Theme
                            .of(context)
                            .colorScheme
                            .surfaceVariant,
                        foregroundColor: _selectedCameraId == cameraId
                            ? Theme
                            .of(context)
                            .colorScheme
                            .onPrimary
                            : Theme
                            .of(context)
                            .colorScheme
                            .onSurfaceVariant,
                      ),
                      onPressed: () => _switchCamera(cameraId),
                      child: Text('摄像头 $cameraId'),
                    ),
                  )),
            ],
          ],
        ),
      ),
    );
  }

  Future<void> _pickAndSwitchImage() async {
    await _modelView.getLatestPicture();
  }

  // 主构建方法
  @override
  Widget build(BuildContext context) {
    return MultiBlocListener(
      listeners: [
        BlocListener<TargetBloc, TargetState>(
          bloc: _targetBloc,
          listener: (context, state) {
            if (!_isCalibrationMode) {
              _syncWithTargetBloc();
            }
          },
          listenWhen: (previous, current) {
            return previous.targets != current.targets ||
                previous.selectedTargetId != current.selectedTargetId;
          },
        ),
        BlocListener<WyDeviceBloc, WyDeviceState>(
          bloc: _wyDeviceBloc,
          listener: (context, state) {
            debugPrint('Calibration mode changed===>: ${state
                .isFactoryCalibrationMode}');
            final wasCalibrationMode = _isCalibrationMode;
            _checkCalibrationMode();

            if (_isCalibrationMode ||
                wasCalibrationMode != _isCalibrationMode) {
              _syncWithCurrentMode();
            }
          },
          listenWhen: (previous, current) {
            bool shouldListen = previous.isFactoryCalibrationMode !=
                current.isFactoryCalibrationMode;
            return shouldListen;
          },
        ),
      ],
      child: LayoutBuilder(
        builder: (context, constraints) {
          return Column(
            children: [
              // 主内容区域（扩展以填充可用空间）
              Expanded(
                child: _viewMode == ViewMode.single
                    ? _buildSingleCameraView(constraints)
                    : _buildTiledCameraView(constraints),
              ),
              // 底部控制面板
              const SizedBox(height: 16),
              _buildControlPanel(),
            ],
          );
        },
      ),
    );
  }

  // 单摄像头视图与ROI选择功能
  Widget _buildSingleCameraView(BoxConstraints constraints) {
    final double availableWidth = constraints.maxWidth;
    final double availableHeight = constraints.maxHeight;
    final double canvasW = availableWidth < widget.canvasWidth
        ? availableWidth
        : widget.canvasWidth;
    final double canvasH = availableHeight < widget.canvasHeight
        ? availableHeight
        : widget.canvasHeight;

    // 根据图像纵横比计算容器尺寸
    double width = canvasW;
    double height = canvasH;

    if (_cameraImages[_selectedCameraId] != null) {
      final imageAspectRatio = _cameraImages[_selectedCameraId]!.width /
          _cameraImages[_selectedCameraId]!.height;
      if (width / height > imageAspectRatio) {
        width = height * imageAspectRatio;
      } else {
        height = width / imageAspectRatio;
      }

      _imageDisplayRect = _calculateImageDisplayRect(
        Size(width, height),
        _cameraImages[_selectedCameraId]!,
        _zoomLevel,
        _imageOffset,
      );
    }

    return RepaintBoundary(
      key: _canvasKey,
      child: FittedBox(
        fit: BoxFit.contain,
        child: Container(
          width: width,
          height: height,
          decoration: BoxDecoration(
            border: Border.all(color: Colors.black12),
            color: Colors.grey[300],
          ),
          child: GestureDetector(
            onPanStart: (details) => _handlePanStart(details, width, height),
            onPanUpdate: (details) => _handlePanUpdate(details, width, height),
            onPanEnd: (details) => _handlePanEnd(details),
            child: SizedBox.expand(
              child: CustomPaint(
                painter: ROIPainter(
                  image: _cameraImages[_selectedCameraId],
                  imageDisplayRect: _imageDisplayRect,
                  roiRects: roiRects,
                  tempRect: tempRect,
                  selectedRect: selectedRect,
                  showTitles: widget.showTitles,
                  showROIs: _showROIs,
                ),
                size: Size(width, height),
              ),
            ),
          ),
        ),
      ),
    );
  }

  // 拼贴视图显示所有摄像头
  Widget _buildTiledCameraView(BoxConstraints constraints) {
    final double availableWidth = constraints.maxWidth;
    final double availableHeight = constraints.maxHeight;

    // 计算网格布局
    final int camerasCount = widget.availableCameraIds.length;
    final int columns = math.min(
        math.max(1, math.sqrt(camerasCount).ceil()), 3); // 最多3列
    final int rows = (camerasCount / columns).ceil();

    return Container(
      width: availableWidth,
      height: availableHeight,
      color: Colors.black12,
      child: GridView.builder(
        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: columns,
          childAspectRatio: widget.canvasWidth / widget.canvasHeight,
          crossAxisSpacing: 4,
          mainAxisSpacing: 4,
        ),
        itemCount: camerasCount,
        itemBuilder: (context, index) {
          final cameraId = widget.availableCameraIds[index];
          return _buildCameraTile(
              cameraId, availableWidth / columns, availableHeight / rows);
        },
      ),
    );
  }

  // 拼贴视图的单个摄像头小块
  Widget _buildCameraTile(int cameraId, double width, double height) {
    return GestureDetector(
      onTap: () {
        // 切换到带有此摄像头的单视图
        _switchCamera(cameraId);
        setState(() {
          _viewMode = ViewMode.single;
        });
      },
      child: Container(
        decoration: BoxDecoration(
          border: Border.all(
            color: cameraId == _selectedCameraId
                ? Theme.of(context).colorScheme.primary
                : Colors.transparent,
            width: 2.0,
          ),
        ),
        child: Stack(
          fit: StackFit.expand,
          children: [
            // 摄像头画面
            RepaintBoundary(
              key: _tileKeys[cameraId],
              child: Container(
                color: Colors.grey[300],
                child: _cameraImages[cameraId] != null
                    ? Center(
                  child: AspectRatio(
                    aspectRatio: _cameraImages[cameraId]!.width /
                        _cameraImages[cameraId]!.height,
                    child: FittedBox(
                      fit: BoxFit.contain,
                      child: SizedBox(
                        width: _cameraImages[cameraId]!.width.toDouble(),
                        height: _cameraImages[cameraId]!.height.toDouble(),
                        child: CustomPaint(
                          painter: ROIPainter(
                            image: _cameraImages[cameraId],
                            imageDisplayRect: Rect.fromLTWH(
                              0,
                              0,
                              _cameraImages[cameraId]!.width.toDouble(),
                              _cameraImages[cameraId]!.height.toDouble(),
                            ),
                            roiRects: [],
                            showTitles: true,
                            showROIs: true,
                          ),
                        ),
                      ),
                    ),
                  ),
                )
                    : const Center(
                  child: Text(
                    '无图像',
                    style: TextStyle(color: Colors.grey),
                  ),
                ),
              ),
            ),
            // 摄像头标签
            Positioned(
              bottom: 8,
              left: 8,
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: Colors.black54,
                  borderRadius: BorderRadius.circular(4),
                ),
                child: Text(
                  '摄像头 $cameraId',
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }  // ROI操作的平移手势处理程序
// 在 _handlePanStart 方法中，修改选择逻辑
  void _handlePanStart(DragStartDetails details, double width, double height) {
    final touchPosition = details.localPosition;
    if (!_showROIs) {
      // 当ROIs隐藏时，仅跟踪图像平移的起始点
      dragStartPoint = touchPosition;
      return;
    }

    final imagePosition = _mapTouchToImage(details.localPosition);
    if (imagePosition == null) return;

    if (_isDrawMode) {
      setState(() {
        tempRect = Rect.fromCenter(
          center: imagePosition,
          width: widget.minWidthRect,
          height: widget.minHeightRect,
        );
      });
    } else {
      // 首先检查是否点击了当前选中的矩形的角点
      if (selectedRect != null) {
        activeCorner = _hitTestCorner(imagePosition, selectedRect!);
        if (activeCorner != null) {
          dragStartPoint = imagePosition;
          return;
        }

        // 检查是否点击了当前选中的矩形内部
        if (selectedRect!.contains(imagePosition)) {
          dragStartPoint = imagePosition;
          dragStartOffset = Offset(selectedRect!.left, selectedRect!.top);
          return;
        }
      }

      // 检查是否点击了其他矩形
      bool foundMatch = false;
      for (final roi in roiRects.reversed) {
        final rect = roi['rect'] as Rect;
        final id = roi['id'] as String?;
        if (rect.contains(imagePosition)) {
          setState(() {
            selectedRect = rect;
            selectedRectId = id;
            dragStartPoint = imagePosition;
            dragStartOffset = Offset(rect.left, rect.top);
          });
          foundMatch = true;
          break;
        }
      }

      // 仅当点击空白区域时才清除选择
      if (!foundMatch && (dragStartPoint == null || !_imageDisplayRect.contains(touchPosition))) {
        setState(() {
          selectedRect = null;
          selectedRectId = null;
        });
      }
    }
  }
  void _handlePanUpdate(DragUpdateDetails details, double width, double height) {
    if (!_showROIs) {
      // 当ROIs隐藏时，仅处理图像平移
      _updateImageOffset(details.delta, Size(width, height));
      return;
    }

    final imagePosition = _mapTouchToImage(details.localPosition);
    if (imagePosition == null) return;

    if (_isDrawMode && tempRect != null) {
      setState(() {
        final newRect = Rect.fromPoints(
          Offset(tempRect!.left, tempRect!.top),
          imagePosition,
        );
        tempRect = Rect.fromLTRB(
          newRect.left,
          newRect.top,
          (newRect.width < widget.minWidthRect)
              ? newRect.left + widget.minWidthRect
              : newRect.right,
          (newRect.height < widget.minHeightRect)
              ? newRect.top + widget.minHeightRect
              : newRect.bottom,
        );
      });
    } else if (!_isDrawMode && selectedRect != null) {
      if (activeCorner != null && dragStartPoint != null) {
        setState(() {
          selectedRect = _resizeRect(
            selectedRect!,
            activeCorner!,
            dragStartPoint!,
            imagePosition,
          );
          selectedRect = _clampRectToImageBounds(selectedRect!);
          final index = roiRects.indexWhere((roi) => roi['id'] == selectedRectId);
          if (index != -1) {
            roiRects[index]['rect'] = selectedRect!;
          }
          dragStartPoint = imagePosition;
        });
      } else if (dragStartPoint != null && dragStartOffset != null) {
        final delta = imagePosition - dragStartPoint!;
        setState(() {
          selectedRect = Rect.fromLTWH(
            dragStartOffset!.dx + delta.dx,
            dragStartOffset!.dy + delta.dy,
            selectedRect!.width,
            selectedRect!.height,
          );
          selectedRect = _clampRectToImageBounds(selectedRect!);
          final index = roiRects.indexWhere((roi) => roi['id'] == selectedRectId);
          if (index != -1) {
            roiRects[index]['rect'] = selectedRect!;
          }
        });
      } else {
        _updateImageOffset(details.delta, Size(width, height));
      }
    } else {
      _updateImageOffset(details.delta, Size(width, height));
    }
  }
  void _handlePanEnd(DragEndDetails details) {
    if (!_showROIs) {
      // 当ROIs隐藏时，跳过ROI相关操作
      dragStartPoint = null;
      return;
    }

    if (_isDrawMode && tempRect != null) {
      setState(() {
        if (_isCalibrationMode) {
          // 在标定模式下，我们只有一个ROI
          roiRects = [
            {
              'rect': tempRect!,
              'label': 'Calibration',
              'id': 'calibration',
            }
          ];
          widget.wyDeviceBloc.add(CalibrationRectChanged(tempRect!));
        } else {
          // 在普通模式下创建新的标靶
          final newTarget = Target(
            targetId: 'target_${DateTime.now().millisecondsSinceEpoch}',
            name: 'T${roiRects.length + 1}',
            rect: tempRect!,
            cameraId: _selectedCameraId,
          );
          _targetBloc.add(TargetCreated(newTarget));
        }
        tempRect = null;
        if (widget.onROIsChanged != null) {
          widget.onROIsChanged!(roiRects);
        }
      });
    } else if (!_isDrawMode) {
      if (widget.onROIsChanged != null) {
        widget.onROIsChanged!(roiRects);
      }

      if (_isCalibrationMode && selectedRect != null) {
        widget.wyDeviceBloc.add(CalibrationRectChanged(selectedRect!));
      } else if (selectedRectId != null && selectedRect != null) {
        _targetBloc.add(TargetROIMoved(selectedRectId!, selectedRect!));
      }
    }

    // 不要在这里清除selectedRect和selectedRectId
    dragStartPoint = null;
    dragStartOffset = null;
    activeCorner = null;
  }
  Offset? _mapTouchToImage(Offset touch) {
    if (_cameraImages[_selectedCameraId] == null ||
        !_imageDisplayRect.contains(touch)) {
      return null;
    }

    final dx = (touch.dx - _imageDisplayRect.left) /
        _imageDisplayRect.width *
        _cameraImages[_selectedCameraId]!.width;
    final dy = (touch.dy - _imageDisplayRect.top) /
        _imageDisplayRect.height *
        _cameraImages[_selectedCameraId]!.height;

    return Offset(dx, dy);
  }

  String? _hitTestCorner(Offset position, Rect rect) {
    double cornerSize = widget.cornerSize;

    // 测试右下角
    if (position.dx >= rect.bottomRight.dx - cornerSize &&
        position.dx <= rect.bottomRight.dx + cornerSize &&
        position.dy >= rect.bottomRight.dy - cornerSize &&
        position.dy <= rect.bottomRight.dy + cornerSize) {
      return 'bottomRight';
    }

    // 测试右上角
    if (position.dx >= rect.topRight.dx - cornerSize &&
        position.dx <= rect.topRight.dx + cornerSize &&
        position.dy >= rect.topRight.dy - cornerSize &&
        position.dy <= rect.topRight.dy + cornerSize) {
      return 'topRight';
    }

    // 测试左下角
    if (position.dx >= rect.bottomLeft.dx - cornerSize &&
        position.dx <= rect.bottomLeft.dx + cornerSize &&
        position.dy >= rect.bottomLeft.dy - cornerSize &&
        position.dy <= rect.bottomLeft.dy + cornerSize) {
      return 'bottomLeft';
    }

    // 测试左上角
    if (position.dx >= rect.topLeft.dx - cornerSize &&
        position.dx <= rect.topLeft.dx + cornerSize &&
        position.dy >= rect.topLeft.dy - cornerSize &&
        position.dy <= rect.topLeft.dy + cornerSize) {
      return 'topLeft';
    }

    // 没有命中任何角
    return null;
  }

  Rect _clampRectToImageBounds(Rect rect) {
    if (_cameraImages[_selectedCameraId] == null) return rect;

    final imageWidth = _cameraImages[_selectedCameraId]!.width.toDouble();
    final imageHeight = _cameraImages[_selectedCameraId]!.height.toDouble();

    // 确保矩形的左上右下角都在图像边界内
    final double left = rect.left.clamp(0.0, imageWidth - widget.minWidthRect);
    final double top = rect.top.clamp(0.0, imageHeight - widget.minHeightRect);
    final double right = rect.right.clamp(
        left + widget.minWidthRect, imageWidth);
    final double bottom = rect.bottom.clamp(
        top + widget.minHeightRect, imageHeight);

    return Rect.fromLTRB(left, top, right, bottom);
  }

  Rect _resizeRect(Rect rect, String corner, Offset start, Offset end) {
    if (_cameraImages[_selectedCameraId] == null) return rect;

    final double minWidth = widget.minWidthRect;
    final double minHeight = widget.minHeightRect;
    final imageWidth = _cameraImages[_selectedCameraId]!.width.toDouble();
    final imageHeight = _cameraImages[_selectedCameraId]!.height.toDouble();

    double left = rect.left;
    double top = rect.top;
    double right = rect.right;
    double bottom = rect.bottom;

    switch (corner) {
      case 'topLeft':
        left =
            (rect.left + (end.dx - start.dx)).clamp(0.0, rect.right - minWidth);
        top = (rect.top + (end.dy - start.dy))
            .clamp(0.0, rect.bottom - minHeight);
        break;
      case 'topRight':
        right = (rect.right + (end.dx - start.dx))
            .clamp(rect.left + minWidth, imageWidth);
        top = (rect.top + (end.dy - start.dy))
            .clamp(0.0, rect.bottom - minHeight);
        break;
      case 'bottomLeft':
        left =
            (rect.left + (end.dx - start.dx)).clamp(0.0, rect.right - minWidth);
        bottom = (rect.bottom + (end.dy - start.dy))
            .clamp(rect.top + minHeight, imageHeight);
        break;
      case 'bottomRight':
        right = (rect.right + (end.dx - start.dx))
            .clamp(rect.left + minWidth, imageWidth);
        bottom = (rect.bottom + (end.dy - start.dy))
            .clamp(rect.top + minHeight, imageHeight);
        break;
    }

    return Rect.fromLTRB(left, top, right, bottom);
  }

  void _zoomIn() {
    if (_cameraImages[_selectedCameraId] == null) return;

    setState(() {
      _zoomLevel *= 1.1;
      _imageDisplayRect = _calculateImageDisplayRect(
        Size(widget.canvasWidth, widget.canvasHeight),
        _cameraImages[_selectedCameraId]!,
        _zoomLevel,
        _imageOffset,
      );
    });
  }

  void _zoomOut() {
    if (_cameraImages[_selectedCameraId] == null) return;

    setState(() {
      _zoomLevel = (_zoomLevel / 1.1).clamp(1.0, double.infinity);
      _imageDisplayRect = _calculateImageDisplayRect(
        Size(widget.canvasWidth, widget.canvasHeight),
        _cameraImages[_selectedCameraId]!,
        _zoomLevel,
        _imageOffset,
      );
    });
  }

  void _normalSize() {
    if (_cameraImages[_selectedCameraId] == null) return;

    setState(() {
      _zoomLevel = 1.0;
      _imageOffset = Offset.zero;
      _imageDisplayRect = _calculateImageDisplayRect(
        Size(widget.canvasWidth, widget.canvasHeight),
        _cameraImages[_selectedCameraId]!,
        _zoomLevel,
        _imageOffset,
      );
    });
  }

  Rect _calculateImageDisplayRect(Size canvasSize, ui.Image image,
      [double zoomLevel = 1.0, Offset offset = Offset.zero]) {
    final imageAspectRatio = image.width / image.height;
    final canvasAspectRatio = canvasSize.width / canvasSize.height;
    double displayWidth, displayHeight;

    if (imageAspectRatio > canvasAspectRatio) {
      // 图像比画布宽
      displayWidth = canvasSize.width * zoomLevel;
      displayHeight = displayWidth / imageAspectRatio;
    } else {
      // 图像比画布高
      displayHeight = canvasSize.height * zoomLevel;
      displayWidth = displayHeight * imageAspectRatio;
    }

    final centerX = (canvasSize.width - displayWidth) / 2 + offset.dx;
    final centerY = (canvasSize.height - displayHeight) / 2 + offset.dy;

    return Rect.fromLTWH(centerX, centerY, displayWidth, displayHeight);
  }

  void _updateImageOffset(Offset delta, Size containerSize) {
    if (_cameraImages[_selectedCameraId] == null) return;

    final newOffset = _imageOffset + delta;
    final imageAspectRatio = _cameraImages[_selectedCameraId]!.width /
        _cameraImages[_selectedCameraId]!.height;
    double scaledWidth, scaledHeight;

    if (imageAspectRatio > containerSize.width / containerSize.height) {
      scaledWidth = containerSize.width * _zoomLevel;
      scaledHeight = scaledWidth / imageAspectRatio;
    } else {
      scaledHeight = containerSize.height * _zoomLevel;
      scaledWidth = scaledHeight * imageAspectRatio;
    }

    // 计算允许的最大偏移量以保持图像可见
    final maxOffsetX = math.max(0.0, (scaledWidth - containerSize.width) / 2);
    final maxOffsetY = math.max(0.0, (scaledHeight - containerSize.height) / 2);

    setState(() {
      _imageOffset = Offset(
        newOffset.dx.clamp(-maxOffsetX, maxOffsetX),
        newOffset.dy.clamp(-maxOffsetY, maxOffsetY),
      );

      _imageDisplayRect = _calculateImageDisplayRect(
        containerSize,
        _cameraImages[_selectedCameraId]!,
        _zoomLevel,
        _imageOffset,
      );
    });
  }

}

/// ROIPainter 类 - 负责绘制图像和 ROI
class ROIPainter extends CustomPainter {
  final ui.Image? image;
  final Rect imageDisplayRect;
  final List<Map<String, dynamic>> roiRects;
  final Rect? tempRect;
  final Rect? selectedRect;
  final bool showTitles;
  final bool showROIs;
  final int maxLabelLength;

  ROIPainter({
    required this.image,
    required this.imageDisplayRect,
    required this.roiRects,
    this.tempRect,
    this.selectedRect,
    required this.showTitles,
    this.showROIs = true,
    this.maxLabelLength = 10,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint();

    // 裁剪到画布边界
    canvas.clipRect(Rect.fromLTWH(0, 0, size.width, size.height));

    // 绘制背景色
    canvas.drawRect(
      Rect.fromLTWH(0, 0, size.width, size.height),
      Paint()..color = Colors.grey.shade300,
    );

    // 如果有图像则绘制图像
    if (image != null) {
      canvas.drawImageRect(
        image!,
        Rect.fromLTWH(0, 0, image!.width.toDouble(), image!.height.toDouble()),
        imageDisplayRect,
        paint,
      );
    }

    // 如果应该显示ROIs，则绘制它们
    if (showROIs) {
      final roiPaint = Paint()
        ..color = Colors.red
        ..style = PaintingStyle.stroke
        ..strokeWidth = 2;

      final selectedPaint = Paint()
        ..color = Colors.blue
        ..style = PaintingStyle.stroke
        ..strokeWidth = 2;

      final cornerPaint = Paint()
        ..color = Colors.blue.withOpacity(0.5)
        ..style = PaintingStyle.fill;

      // 绘制所有ROI矩形
      for (final roi in roiRects) {
        final rect = roi['rect'] as Rect;
        final isSelected = rect == selectedRect;

        // 将ROI坐标映射到显示坐标
        final mappedRect = _mapImageRectToDisplay(rect, imageDisplayRect);

        // 用适当颜色绘制ROI矩形
        canvas.drawRect(mappedRect, isSelected ? selectedPaint : roiPaint);

        // 如果这是选中的ROI，绘制角点控制柄
        if (isSelected) {
          const double cornerSize = 10; // 调整大小手柄的大小

          // 绘制每个角手柄
          // 右下角
          _drawCornerHandle(canvas, mappedRect.bottomRight, cornerPaint, cornerSize);

          // 右上角
          _drawCornerHandle(canvas, mappedRect.topRight, cornerPaint, cornerSize);

          // 左下角
          _drawCornerHandle(canvas, mappedRect.bottomLeft, cornerPaint, cornerSize);

          // 左上角
          _drawCornerHandle(canvas, mappedRect.topLeft, cornerPaint, cornerSize);
        }

        // 如果启用，绘制标题标签
        if (showTitles) {
          final label = roi['label'] as String? ?? '';
          final displayLabel = label.length > maxLabelLength
              ? '${label.substring(0, maxLabelLength)}...'
              : label;

          final textSpan = TextSpan(
            text: displayLabel,
            style: const TextStyle(
              color: Colors.white,
              fontSize: 12,
              fontWeight: FontWeight.bold,
              shadows: [
                Shadow(
                  offset: Offset(1.0, 1.0),
                  blurRadius: 3.0,
                  color: Colors.black,
                ),
              ],
            ),
          );

          final textPainter = TextPainter(
            text: textSpan,
            textDirection: TextDirection.ltr,
          );
          textPainter.layout();

          // 计算标签位置
          double labelX = mappedRect.left;
          double labelY = mappedRect.top - textPainter.height - 2;

          // 如果标签会在可见区域外，调整标签位置
          if (labelY < imageDisplayRect.top) {
            labelY = mappedRect.bottom + 2; // 移到ROI底部
          }

          if (labelX + textPainter.width > imageDisplayRect.right) {
            labelX = mappedRect.right - textPainter.width;
          }

          if (labelX < imageDisplayRect.left) {
            labelX = imageDisplayRect.left;
          }

          // 为标签绘制背景，以提高可见性
          final labelRect = Rect.fromLTWH(
            labelX - 2,
            labelY - 2,
            textPainter.width + 4,
            textPainter.height + 4,
          );
          canvas.drawRect(
            labelRect,
            Paint()..color = Colors.black.withOpacity(0.5),
          );

          // 绘制标签文本
          textPainter.paint(canvas, Offset(labelX, labelY));
        }
      }

      // 绘制正在创建的临时矩形（如果有）
      if (tempRect != null) {
        canvas.drawRect(
          _mapImageRectToDisplay(tempRect!, imageDisplayRect),
          roiPaint,
        );
      }
    }
  }

  // 绘制角点控制柄
  void _drawCornerHandle(Canvas canvas, Offset corner, Paint paint, double size) {
    canvas.drawRect(
      Rect.fromCenter(
        center: corner,
        width: size,
        height: size,
      ),
      paint,
    );
  }

  // 将坐标从图像空间映射到显示空间
  Rect _mapImageRectToDisplay(Rect imageRect, Rect displayRect) {
    if (image == null) return Rect.zero;

    final scaleX = displayRect.width / image!.width;
    final scaleY = displayRect.height / image!.height;

    return Rect.fromLTRB(
      displayRect.left + imageRect.left * scaleX,
      displayRect.top + imageRect.top * scaleY,
      displayRect.left + imageRect.right * scaleX,
      displayRect.top + imageRect.bottom * scaleY,
    );
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    if (oldDelegate is ROIPainter) {
      return oldDelegate.image != image ||
          oldDelegate.imageDisplayRect != imageDisplayRect ||
          oldDelegate.roiRects != roiRects ||
          oldDelegate.tempRect != tempRect ||
          oldDelegate.selectedRect != selectedRect ||
          oldDelegate.showTitles != showTitles ||
          oldDelegate.showROIs != showROIs;
    }
    return true;
  }
}
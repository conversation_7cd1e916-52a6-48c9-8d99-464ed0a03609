import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'dart:convert';

import '../../../model/wy_device.dart';
import '../../cron/cron-component.dart';

// 设置Cron表达式的弹窗组件
class CronEditorDialog extends StatefulWidget {
  final String initialCronExpression;
  final String initialDescription;
  final Color? primaryColor;
  final bool isDarkMode;

  const CronEditorDialog({
    Key? key,
    required this.initialCronExpression,
    required this.initialDescription,
    this.primaryColor,
    this.isDarkMode = false,
  }) : super(key: key);

  @override
  _CronEditorDialogState createState() => _CronEditorDialogState();
}

class _CronEditorDialogState extends State<CronEditorDialog> {
  late String _cronExpression;
  late String _cronDescription;

  @override
  void initState() {
    super.initState();
    _cronExpression = widget.initialCronExpression;
    _cronDescription = widget.initialDescription;
  }

  @override
  Widget build(BuildContext context) {
    final themeColor = widget.primaryColor ?? Theme.of(context).primaryColor;

    return Dialog(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      clipBehavior: Clip.antiAlias,
      child: Container(
        width: double.infinity,
        constraints: const BoxConstraints(maxWidth: 600, maxHeight: 600),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // 标题栏
            Container(
              padding: const EdgeInsets.all(16),
              color: themeColor,
              child: Row(
                children: [
                  const Text(
                    '定时测量设置',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const Spacer(),
                  IconButton(
                    icon: const Icon(Icons.close, color: Colors.white),
                    onPressed: () => Navigator.pop(context),
                    tooltip: '关闭',
                  ),
                ],
              ),
            ),

            // Cron编辑器内容
            Expanded(
              child: SingleChildScrollView(
                child: Padding(
                  padding: const EdgeInsets.all(0),
                  child: DailyCronBuilder(
                    initialCronExpression: widget.initialCronExpression,
                    onCronExpressionChanged: (expression, description) {
                      setState(() {
                        _cronExpression = expression;
                        _cronDescription = description;
                      });
                    },
                    primaryColor: themeColor,
                    backgroundColor: widget.isDarkMode ? Colors.grey[800] : Colors.grey[200],
                    showCopyButton: false,
                    showResetButton: true,
                    showGenerateButton: false,
                    showNextExecutionPreview: true,
                    previewCount: 3,
                    useDarkMode: widget.isDarkMode,
                  ),
                ),
              ),
            ),

            // 底部按钮
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  TextButton(
                    onPressed: () => Navigator.pop(context),
                    child: const Text('取消'),
                  ),
                  const SizedBox(width: 8),
                  ElevatedButton(
                    onPressed: () {
                      Navigator.pop(context, {
                        'expression': _cronExpression,
                        'description': _cronDescription,
                      });
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: themeColor,
                    ),
                    child: const Text('确定'),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}



/// 测量请求回调函数类型定义
typedef MeasurementRequestCallback = void Function(Map<String, dynamic> request);

/// 测量控制组件
///
/// 一个响应式的测量控制界面，用于生成测量控制JSON请求
/// 支持自动适应不同屏幕尺寸，特别优化了在底部抽屉中的使用体验
class MeasurementControlPanel extends StatefulWidget {
  /// 测量状态变化回调
  final MeasurementRequestCallback? onMeasurementRequest;

  /// 当前测量状态
  final WyDeviceStatus status;

  /// 主题颜色
  final Color? primaryColor;

  /// 背景颜色
  final Color? backgroundColor;

  /// 是否显示JSON预览（默认不显示）
  final bool showJsonPreview;

  /// 初始测量模式
  final String initialMeasureMode;

  /// 初始调度模式
  final String initialScheduleMode;

  /// 初始Cron表达式（当调度模式为Cron时使用）
  final String initialCronExpression;

  /// 初始同步测量持续时间（秒）
  final int initialSyncDuration;

  /// 初始轮询间隔（秒）
  final int initialRoundRobinInterval;

  /// 外部滚动控制器，用于和父组件的滚动视图协调
  final ScrollController? externalScrollController;

  /// 创建测量控制组件
  const MeasurementControlPanel({
    Key? key,
    this.onMeasurementRequest,
    this.status = WyDeviceStatus.idle,
    this.primaryColor,
    this.backgroundColor,
    this.showJsonPreview = false,
    this.initialMeasureMode = 'Sync',
    this.initialScheduleMode = 'Continuous',
    this.initialCronExpression = '0 * * * *',
    this.initialSyncDuration = 10,
    this.initialRoundRobinInterval = 10,
    this.externalScrollController,
  }) : super(key: key);

  @override
  _MeasurementControlPanelState createState() => _MeasurementControlPanelState();
}

class _MeasurementControlPanelState extends State<MeasurementControlPanel> {
  // 测量模式选项
  final List<String> _measureModes = ['Sync', 'RoundRobin'];
  late String _measureMode;

  // 调度模式选项
  final List<String> _scheduleModes = ['Continuous', 'Cron'];
  late String _scheduleMode;

  // Cron表达式
  late String _cronExpression;

  // Cron描述
  String _cronDescription = '';

  // 同步测量持续时间（秒）
  late TextEditingController _syncDurationController;

  // 轮询间隔（秒）
  late TextEditingController _roundRobinIntervalController;

  // 最后一次生成的请求JSON
  String? _lastRequestJson;

  @override
  void initState() {
    super.initState();
    // 初始化参数
    _measureMode = widget.initialMeasureMode;
    _scheduleMode = widget.initialScheduleMode;
    _cronExpression = widget.initialCronExpression;
    _cronDescription = "每天 00:00 执行"; // 默认描述
    _syncDurationController = TextEditingController(text: widget.initialSyncDuration.toString());
    _roundRobinIntervalController = TextEditingController(text: widget.initialRoundRobinInterval.toString());
  }

  @override
  void dispose() {
    _syncDurationController.dispose();
    _roundRobinIntervalController.dispose();
    super.dispose();
  }

  // 判断是否应该显示持续时间输入框
  bool _shouldShowDurationInput() {
    // 当测量模式为Sync且调度模式为Cron时显示
    return _measureMode == 'Sync' && _scheduleMode == 'Cron';
  }

  // 判断是否应该显示轮询间隔输入框
  bool _shouldShowIntervalInput() {
    // 当测量模式为RoundRobin时显示 (无论是Cron还是Continuous)
    return _measureMode == 'RoundRobin';
  }

  // 打开Cron编辑器对话框
  Future<void> _openCronEditorDialog() async {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final themeColor = widget.primaryColor ?? Theme.of(context).primaryColor;

    final result = await showDialog<Map<String, String>>(
      context: context,
      builder: (context) => CronEditorDialog(
        initialCronExpression: _cronExpression,
        initialDescription: _cronDescription,
        primaryColor: themeColor,
        isDarkMode: isDarkMode,
      ),
    );

    if (result != null) {
      setState(() {
        _cronExpression = result['expression']!;
        _cronDescription = result['description']!;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    // 获取主题颜色
    final themeColor = widget.primaryColor ?? Theme.of(context).primaryColor;
    final cardColor = widget.backgroundColor ?? Theme.of(context).cardColor;

    // 获取屏幕尺寸
    final screenWidth = MediaQuery.of(context).size.width;
    final isLargeScreen = screenWidth > 600;

    // 是否可以开始测量（只有空闲状态才能开始）
    final canStartMeasurement = widget.status == WyDeviceStatus.idle;

    // 是否可以编辑参数（只有空闲状态才能编辑）
    final canEditParams = widget.status == WyDeviceStatus.idle;

    // 内容与按钮直接分离，不使用Stack，而是使用Column
    return Column(
      children: [
        // 内容区域 - 可直接使用外部提供的滚动控制器
        Expanded(
          child: SingleChildScrollView(
            // 如果提供了外部控制器，就使用它，这样可以与父组件滚动协调
            controller: widget.externalScrollController,
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // 标题和状态指示器（在抽屉中可能已有标题，按需显示）
                  if (widget.externalScrollController == null) // 如果不是在抽屉中使用
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        const Text(
                          '测量控制',
                          style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                        ),
                        _buildStatusIndicator(themeColor),
                      ],
                    ),

                  if (widget.externalScrollController == null) // 如果不是在抽屉中使用
                    const Divider(height: 24),

                  // 响应式布局 - 大屏幕并排，小屏幕堆叠
                  if (isLargeScreen)
                    _buildWideLayout(themeColor, canEditParams)
                  else
                    _buildNarrowLayout(themeColor, canEditParams),

                  // 条件性显示Cron设置按钮
                  if (_scheduleMode == 'Cron') ...[
                    const SizedBox(height: 16),
                    _buildCronSummary(themeColor, canEditParams),
                  ],

                  // 条件性显示持续时间输入框
                  if (_shouldShowDurationInput()) ...[
                    const SizedBox(height: 16),
                    _buildDurationInput(canEditParams),
                  ],

                  // 条件性显示轮询间隔输入框
                  if (_shouldShowIntervalInput()) ...[
                    const SizedBox(height: 16),
                    _buildIntervalInput(canEditParams),
                  ],

                  // JSON预览（可选）
                  if (widget.showJsonPreview && _lastRequestJson != null) ...[
                    const SizedBox(height: 16),
                    const Divider(),
                    const SizedBox(height: 8),
                    const Text('JSON预览:', style: TextStyle(fontWeight: FontWeight.bold, fontSize: 14)),
                    const SizedBox(height: 8),
                    Container(
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: Theme.of(context).brightness == Brightness.dark
                            ? Colors.grey[850]
                            : Colors.grey[200],
                        borderRadius: BorderRadius.circular(4),
                      ),
                      width: double.infinity,
                      child: SelectableText(
                        _lastRequestJson!,
                        style: const TextStyle(fontFamily: 'monospace', fontSize: 12),
                      ),
                    ),
                  ],

                  // 底部空间，确保内容不被按钮遮挡
                  const SizedBox(height: 24),
                ],
              ),
            ),
          ),
        ),

        // 固定在底部的操作按钮
        Container(
          width: double.infinity,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: cardColor,
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.1),
                blurRadius: 4,
                offset: const Offset(0, -2),
              ),
            ],
          ),
          child: _buildActionButton(themeColor, canStartMeasurement),
        ),
      ],
    );
  }

  // 状态指示器
  Widget _buildStatusIndicator(Color themeColor) {
    // 根据不同状态显示不同颜色和文本
    Color statusColor;
    String statusText;
    IconData statusIcon;

    switch (widget.status) {
      case WyDeviceStatus.idle:
        statusColor = Colors.grey;
        statusText = '空闲';
        statusIcon = Icons.circle_outlined;
        break;
      case WyDeviceStatus.measuring:
        statusColor = Colors.green;
        statusText = '测量中';
        statusIcon = Icons.fiber_manual_record;
        break;
      case WyDeviceStatus.initializing:
        statusColor = Colors.orange;
        statusText = '初始化中';
        statusIcon = Icons.pending_outlined;
        break;
      case WyDeviceStatus.testing:
        statusColor = Colors.orange;
        statusText = '调试中';
        statusIcon = Icons.pending_outlined;
        break;
      case WyDeviceStatus.accuracyCalibration:
        statusColor = Colors.yellow;
        statusText = '精度标定中';
        statusIcon = Icons.build_circle_outlined;
        break;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 4),
      decoration: BoxDecoration(
        color: statusColor.withOpacity(0.2),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: statusColor),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            statusIcon,
            color: statusColor,
            size: 12,
          ),
          const SizedBox(width: 6),
          Text(
            statusText,
            style: TextStyle(
              fontWeight: FontWeight.bold,
              color: statusColor,
              fontSize: 12,
            ),
          ),
        ],
      ),
    );
  }

  // 宽屏布局 - 并排显示控件
  Widget _buildWideLayout(Color themeColor, bool canEdit) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Expanded(
          child: Padding(
            padding: const EdgeInsets.only(right: 8.0),
            child: _buildMeasureModeRadio(canEdit, themeColor),
          ),
        ),
        Expanded(
          child: Padding(
            padding: const EdgeInsets.only(left: 8.0),
            child: _buildScheduleModeRadio(canEdit, themeColor),
          ),
        ),
      ],
    );
  }

  // 窄屏布局 - 控件堆叠显示
  Widget _buildNarrowLayout(Color themeColor, bool canEdit) {
    return Column(
      children: [
        _buildMeasureModeRadio(canEdit, themeColor),
        const SizedBox(height: 16),
        _buildScheduleModeRadio(canEdit, themeColor),
      ],
    );
  }
// 测量模式单选框
  Widget _buildMeasureModeRadio(bool canEdit, Color themeColor) {
    // 测量模式标签映射（便于后期国际化）
    final Map<String, String> measureModeLabels = {
      'Sync': '同步测量',
      'RoundRobin': '轮询测量',
    };

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text('测量模式', style: TextStyle(fontSize: 14, fontWeight: FontWeight.bold)),
        const SizedBox(height: 8),
        Row(
          children: List.generate(_measureModes.length, (index) {
            final mode = _measureModes[index];
            return Expanded(
              child: RadioListTile<String>(
                title: Text(
                  measureModeLabels[mode] ?? mode,
                  style: const TextStyle(fontSize: 13),
                ),
                value: mode,
                groupValue: _measureMode,
                activeColor: themeColor,
                contentPadding: EdgeInsets.zero,
                dense: true,
                onChanged: canEdit ? (value) => value != null ? setState(() => _measureMode = value) : null : null,
              ),
            );
          }),
        ),
      ],
    );
  }

// 调度模式单选框
  Widget _buildScheduleModeRadio(bool canEdit, Color themeColor) {
    final Map<String, String> scheduleModeLabels = {
      'Continuous': '连续测量',
      'Cron': '定时测量',
    };

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text('调度模式', style: TextStyle(fontSize: 14, fontWeight: FontWeight.bold)),
        const SizedBox(height: 8),
        Row(
          children: List.generate(_scheduleModes.length, (index) {
            final mode = _scheduleModes[index];
            return Expanded(
              child: RadioListTile<String>(
                title: Text(
                  scheduleModeLabels[mode] ?? mode,
                  style: const TextStyle(fontSize: 13),
                ),
                value: mode,
                groupValue: _scheduleMode,
                activeColor: themeColor,
                contentPadding: EdgeInsets.zero,
                dense: true,
                onChanged: canEdit ? (value) {
                  if (value != null) {
                    setState(() {
                      _scheduleMode = value;
                    });
                  }
                } : null,
              ),
            );
          }),
        ),
      ],
    );
  }
  // Cron表达式摘要与编辑按钮
  Widget _buildCronSummary(Color themeColor, bool canEdit) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            const Text('定时测量时间', style: TextStyle(fontSize: 14, fontWeight: FontWeight.bold)),
            const Spacer(),
            if (canEdit)
              TextButton.icon(
                onPressed: _openCronEditorDialog,
                icon: const Icon(Icons.timer_sharp, size: 16),
                label: const Text('设置'),
                style: TextButton.styleFrom(
                  foregroundColor: themeColor,
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                ),
              ),
          ],
        ),
        const SizedBox(height: 8),
        Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: isDarkMode ? Colors.grey[800] : Colors.grey[200],
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: isDarkMode ? Colors.grey[700]! : Colors.grey[300]!),
          ),
          width: double.infinity,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Row(
              //   crossAxisAlignment: CrossAxisAlignment.start,
              //   children: [
              //     const Text('表达式: ', style: TextStyle(fontWeight: FontWeight.bold)),
              //     Expanded(
              //       child: SelectableText(
              //         _cronExpression,
              //         style: const TextStyle(fontFamily: 'monospace'),
              //       ),
              //     ),
              //   ],
              // ),
              // const SizedBox(height: 8),
              Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // const Text('说明: ', style: TextStyle(fontWeight: FontWeight.bold)),
                  Expanded(
                    child: Text(
                      _cronDescription,
                      style: TextStyle(
                        color: isDarkMode ? Colors.grey[400] : Colors.grey[700],
                        fontStyle: FontStyle.italic,
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ],
    );
  }
  // 持续时间输入框
  Widget _buildDurationInput(bool canEdit) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text('持续时间 (秒)', style: TextStyle(fontSize: 14, fontWeight: FontWeight.bold)),
        const SizedBox(height: 8),
        TextFormField(
          controller: _syncDurationController,
          decoration: const InputDecoration(
            border: OutlineInputBorder(),
            hintText: '输入持续时间（1-3600秒）',
            contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 12),
            isDense: true,
            suffixText: '秒',
          ),
          keyboardType: TextInputType.number,
          inputFormatters: [
            FilteringTextInputFormatter.digitsOnly,
            _MaxValueTextInputFormatter(3600),
          ],
          enabled: canEdit,
          validator: (value) {
            if (value == null || value.isEmpty) {
              return '请输入持续时间';
            }
            final duration = int.tryParse(value);
            if (duration == null || duration < 1 || duration > 3600) {
              return '持续时间应在1-3600秒之间';
            }
            return null;
          },
        ),
      ],
    );
  }

  // 轮询间隔输入框
  Widget _buildIntervalInput(bool canEdit) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text('轮询间隔 (秒)', style: TextStyle(fontSize: 14, fontWeight: FontWeight.bold)),
        const SizedBox(height: 8),
        TextFormField(
          controller: _roundRobinIntervalController,
          decoration: const InputDecoration(
            border: OutlineInputBorder(),
            hintText: '输入轮询间隔（1-3600秒）',
            contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 12),
            isDense: true,
            suffixText: '秒',
          ),
          keyboardType: TextInputType.number,
          inputFormatters: [
            FilteringTextInputFormatter.digitsOnly,
            _MaxValueTextInputFormatter(3600),
          ],
          enabled: canEdit,
          validator: (value) {
            if (value == null || value.isEmpty) {
              return '请输入轮询间隔';
            }
            final interval = int.tryParse(value);
            if (interval == null || interval < 1 || interval > 3600) {
              return '轮询间隔应在1-3600秒之间';
            }
            return null;
          },
        ),
      ],
    );
  }

  // 操作按钮
  Widget _buildActionButton(Color themeColor, bool canStart) {
    // 按钮状态
    final bool isIdle = widget.status == WyDeviceStatus.idle;
    final bool isInitializing = widget.status == WyDeviceStatus.initializing;

    // 按钮样式和文本
    Color buttonColor;
    String buttonText;
    IconData buttonIcon;
    VoidCallback? onPressed;

    if (isIdle) {
      // 空闲状态，显示开始按钮
      buttonColor = Colors.green;
      buttonText = '开始测量';
      buttonIcon = Icons.play_arrow;
      onPressed = canStart ? _startMeasurement : null;
    } else if (isInitializing) {
      // 初始化中，显示停止按钮但禁用
      buttonColor = Colors.red;
      buttonText = '停止测量 (初始化中)';
      buttonIcon = Icons.stop;
      onPressed = null; // 初始化时暂时禁用停止按钮
    } else {
      // 其他所有状态（测量中、调试中、标定中等），显示停止按钮
      buttonColor = Colors.red;
      buttonText = '停止测量';
      buttonIcon = Icons.stop;
      onPressed = _stopMeasurement;
    }

    return ElevatedButton.icon(
      onPressed: onPressed,
      icon: Icon(buttonIcon, size: 20),
      label: Text(buttonText, style: const TextStyle(fontSize: 16)),
      style: ElevatedButton.styleFrom(
        backgroundColor: buttonColor,
        foregroundColor: Colors.white,
        padding: const EdgeInsets.symmetric(vertical: 16),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
        minimumSize: const Size(double.infinity, 54), // 确保按钮有足够的高度
        disabledBackgroundColor: buttonColor.withOpacity(0.6),
        disabledForegroundColor: Colors.white70,
      ),
    );
  }
  /// 开始测量
  void _startMeasurement() {
    final request = _buildStartRequest();

    setState(() {
      _lastRequestJson = JsonEncoder.withIndent('  ').convert(request);
    });

    if (widget.onMeasurementRequest != null) {
      widget.onMeasurementRequest!(request);
    }
  }

  /// 停止测量
  void _stopMeasurement() {
    final request = _buildStopRequest();

    setState(() {
      _lastRequestJson = JsonEncoder.withIndent('  ').convert(request);
    });

    if (widget.onMeasurementRequest != null) {
      widget.onMeasurementRequest!(request);
    }
  }

  /// 获取同步测量持续时间
  int _getSyncDuration() {
    return int.tryParse(_syncDurationController.text) ?? widget.initialSyncDuration;
  }

  /// 获取轮询间隔
  int _getRoundRobinInterval() {
    return int.tryParse(_roundRobinIntervalController.text) ?? widget.initialRoundRobinInterval;
  }

  /// 构建开始测量请求
  Map<String, dynamic> _buildStartRequest() {
    final params = {
      'measureMode': _measureMode,
      'syncMeasureDuration': _getSyncDuration(),
      'roundRobinInterval': _getRoundRobinInterval(),
    };

    // 根据调度模式设置不同的scheduleMode参数
    if (_scheduleMode == 'Cron') {
      params['scheduleMode'] = {'Cron': _cronExpression};
    } else {
      params['scheduleMode'] = 'Continuous';
    }

    return {
      'method': 'startMeasurement',
      'params': params
    };
  }

  /// 构建停止测量请求
  Map<String, dynamic> _buildStopRequest() {
    return {
      'method': 'stopMeasurement',
      'params': {}
    };
  }
}

/// 最大值限制输入格式器
class _MaxValueTextInputFormatter extends TextInputFormatter {
  final int maxValue;

  _MaxValueTextInputFormatter(this.maxValue);

  @override
  TextEditingValue formatEditUpdate(
      TextEditingValue oldValue, TextEditingValue newValue) {
    if (newValue.text.isEmpty) {
      return newValue;
    }

    final int? parsedValue = int.tryParse(newValue.text);
    if (parsedValue == null) {
      return oldValue;
    }

    if (parsedValue > maxValue) {
      return TextEditingValue(
        text: maxValue.toString(),
        selection: TextSelection.collapsed(offset: maxValue.toString().length),
      );
    }

    return newValue;
  }
}

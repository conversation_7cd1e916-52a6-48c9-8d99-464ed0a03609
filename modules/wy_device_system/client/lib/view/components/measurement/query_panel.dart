import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

import '../../../bloc/targets_blocs.dart';
import '../../../bloc/wy_device_blocs.dart';
import '../../../model/measurement_query.dart';
import '../../../model/target.dart';
import '../targets/target_selector.dart';
import 'package:provider/provider.dart';
import 'package:flutter_bloc/flutter_bloc.dart';


class QueryPanel extends StatefulWidget {
  final Function(MeasurementQuery query)? onQueryChanged;
  const QueryPanel({
    super.key,
    this.onQueryChanged,
  });

  @override
  State<QueryPanel> createState() => _QueryPanelState();
}

class _QueryPanelState extends State<QueryPanel> {
  MeasurementType _selectedChartType = MeasurementType.displacement;
  DateTime _startDate = DateTime.now().subtract(const Duration(hours: 24));
  DateTime _endDate = DateTime.now();
  bool _isRealtime = true;
  Duration _selectedTimeWindow = const Duration(minutes: 10);

  // 检查是否需要显示标靶选择器
  bool get _showTargetSelector => _selectedChartType == MeasurementType.displacement;

  final _dateFormat = DateFormat('yyyy-MM-dd HH:mm');
  final _shortDateFormat = DateFormat('MM-dd HH:mm');

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Container(
      margin: const EdgeInsets.symmetric(vertical: 8, horizontal: 2),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12.0),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            offset: const Offset(0, 2),
            blurRadius: 8.0,
            spreadRadius: 0.0,
          ),
          BoxShadow(
            color: Colors.black.withOpacity(0.02),
            offset: const Offset(0, 1),
            blurRadius: 3.0,
            spreadRadius: 0.0,
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: () => _showQueryDialog(context),
          borderRadius: BorderRadius.circular(12.0),
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                // 图标部分
                Container(
                  width: 40,
                  height: 40,
                  decoration: BoxDecoration(
                    color: theme.primaryColor.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(10.0),
                  ),
                  child: Icon(
                    _selectedChartType == MeasurementType.displacement
                        ? Icons.show_chart
                        : Icons.thermostat,
                    color: theme.primaryColor,
                    size: 22,
                  ),
                ),
                const SizedBox(width: 16),

                // 左侧：查询条件摘要
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      // 当前图表类型
                      Row(
                        children: [
                          Text(
                            _selectedChartType.displayName,
                            style: const TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                          if (_showTargetSelector) ...[
                            const SizedBox(width: 6),
                            Container(
                              height: 4,
                              width: 4,
                              decoration: BoxDecoration(
                                color: Colors.grey.shade400,
                                shape: BoxShape.circle,
                              ),
                            ),
                            const SizedBox(width: 6),
                            Expanded(
                              child: Text(
                                _getSelectedTargetName(),
                                style: TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.w400,
                                  color: Colors.grey.shade800,
                                ),
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                          ],
                        ],
                      ),
                      const SizedBox(height: 6),

                      // 时间范围摘要
                      Row(
                        children: [
                          Icon(
                            _isRealtime ? Icons.access_time : Icons.date_range,
                            size: 14,
                            color: Colors.grey.shade500,
                          ),
                          const SizedBox(width: 4),
                          Expanded(
                            child: Text(
                              _isRealtime
                                  ? '实时 (${_formatDuration(_selectedTimeWindow)})'
                                  : '${_shortDateFormat.format(_startDate)} ~ ${_shortDateFormat.format(_endDate)}',
                              style: TextStyle(
                                fontSize: 14,
                                color: Colors.grey.shade600,
                              ),
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),

                // 右侧：下拉指示器
                Container(
                  width: 28,
                  height: 28,
                  decoration: BoxDecoration(
                    color: Colors.grey.shade100,
                    borderRadius: BorderRadius.circular(8.0),
                  ),
                  child: Icon(
                    Icons.keyboard_arrow_down_rounded,
                    size: 20,
                    color: Colors.grey.shade600,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }




  String _formatDuration(Duration duration) {
    if (duration.inDays > 0) {
      return '${duration.inDays}天';
    } else if (duration.inHours > 0) {
      return '${duration.inHours}小时';
    } else {
      return '${duration.inMinutes}分钟';
    }
  }

  String _getSelectedTargetName() {
    TargetBloc targetBloc = context.read<TargetBloc>();
    final selectedTargetId = targetBloc.state.selectedTargetId;

    if (selectedTargetId == null) {
      return '未选择标靶';
    }

    // 尝试从标靶列表中获取标靶名称
    try {
      final targetBloc = context.read<TargetBloc>();
      final targets = targetBloc.state.targets;
      final target = targets.firstWhere((t) => t.targetId == selectedTargetId);
      return target.name;
    } catch (e) {
      return selectedTargetId;
    }
  }

  void _showQueryDialog(BuildContext context) {
    // 获取当前需要的 Bloc 数据
    final wyDeviceBloc = context.read<WyDeviceBloc>();
    final selectedTargetId = wyDeviceBloc.state.selectedTargetId;

    showDialog(
      context: context,
      builder: (dialogContext) => MultiBlocProvider(
        providers: [
          BlocProvider.value(value: context.read<WyDeviceBloc>()),
          BlocProvider.value(value: context.read<TargetBloc>()),
        ],
        child: QueryDialog(
          initialChartType: _selectedChartType,
          initialIsRealtime: _isRealtime,
          initialTimeWindow: _selectedTimeWindow,
          initialStartDate: _startDate,
          initialEndDate: _endDate,
          selectedTargetId: selectedTargetId,
            onApply: (chartType, isRealtime, timeWindow, startDate, endDate) {
              setState(() {
                _selectedChartType = chartType;
                _isRealtime = isRealtime;
                _selectedTimeWindow = timeWindow;
                _startDate = startDate;
                _endDate = endDate;

                // 触发整合后的回调
                if (widget.onQueryChanged != null) {
                  if (isRealtime) {
                    final now = DateTime.now();
                    widget.onQueryChanged!(MeasurementQuery(
                      chartType: chartType,
                      startTime: now.subtract(timeWindow),
                      endTime: now,
                      isRealtime: true,
                      selectedTargetId: _showTargetSelector ? context.read<TargetBloc>().state.selectedTargetId : null,
                    ));
                  } else {
                    widget.onQueryChanged!(MeasurementQuery(
                      chartType: chartType,
                      startTime: startDate,
                      endTime: endDate,
                      isRealtime: false,
                      selectedTargetId: _showTargetSelector ? context.read<TargetBloc>().state.selectedTargetId : null,
                    ));
                  }
                }
              });
            },
        ),
      ),
    );
  }}

class QueryDialog extends StatefulWidget {
  final MeasurementType initialChartType;
  final bool initialIsRealtime;
  final Duration initialTimeWindow;
  final DateTime initialStartDate;
  final DateTime initialEndDate;
  final String? selectedTargetId;
  final Function(MeasurementType, bool, Duration, DateTime, DateTime) onApply;

  const QueryDialog({
    super.key,
    required this.initialChartType,
    required this.initialIsRealtime,
    required this.initialTimeWindow,
    required this.initialStartDate,
    required this.initialEndDate,
    this.selectedTargetId,
    required this.onApply,
  });

  @override
  State<QueryDialog> createState() => _QueryDialogState();
}

class _QueryDialogState extends State<QueryDialog> {
  late MeasurementType _selectedChartType;
  late bool _isRealtime;
  late Duration _selectedTimeWindow;
  late DateTime _startDate;
  late DateTime _endDate;

  bool get _showTargetSelector => _selectedChartType == MeasurementType.displacement;

  final _dateFormat = DateFormat('yyyy-MM-dd HH:mm');

  @override
  void initState() {
    super.initState();
    _selectedChartType = widget.initialChartType;
    _isRealtime = widget.initialIsRealtime;
    _selectedTimeWindow = widget.initialTimeWindow;
    _startDate = widget.initialStartDate;
    _endDate = widget.initialEndDate;
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      elevation: 8,
      child: SingleChildScrollView(
        child: Container(
          width: double.infinity,
          padding: const EdgeInsets.all(20),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 标题与关闭按钮
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Row(
                    children: [
                      Container(
                        width: 32,
                        height: 32,
                        decoration: BoxDecoration(
                          color: theme.primaryColor.withOpacity(0.1),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Icon(
                          Icons.tune,
                          color: theme.primaryColor,
                          size: 20,
                        ),
                      ),
                      const SizedBox(width: 12),
                      const Text(
                        '查询条件设置',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                  IconButton(
                    padding: EdgeInsets.zero,
                    constraints: const BoxConstraints(),
                    icon: const Icon(Icons.close),
                    color: Colors.grey.shade500,
                    onPressed: () => Navigator.of(context).pop(),
                  ),
                ],
              ),
              const Divider(height: 32),

              // 图表类型选择
              _buildChartTypeSelector(),
              const SizedBox(height: 20),

              // 标靶选择（仅当图表类型为位移时显示）
              if (_showTargetSelector) ...[
                _buildTargetSelector(context),
                const SizedBox(height: 20),
              ],

              // 时间范围控制
              _buildTimeRangeControls(),

              const SizedBox(height: 24),

              // 底部按钮
              Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  TextButton(
                    onPressed: () => Navigator.of(context).pop(),
                    style: TextButton.styleFrom(
                      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
                    ),
                    child: const Text('取消'),
                  ),
                  const SizedBox(width: 12),
                  ElevatedButton(
                    onPressed: () {
                      widget.onApply(
                        _selectedChartType,
                        _isRealtime,
                        _selectedTimeWindow,
                        _startDate,
                        _endDate,
                      );
                      Navigator.of(context).pop();
                    },
                    style: ElevatedButton.styleFrom(
                      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    child: const Text('应用'),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildChartTypeSelector() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text('图表类型', style: TextStyle(
          fontWeight: FontWeight.bold,
          fontSize: 16,
        )),
        const SizedBox(height: 10),
        Container(
          decoration: BoxDecoration(
            border: Border.all(color: Colors.grey.shade300),
            borderRadius: BorderRadius.circular(10),
          ),
          child: DropdownButtonFormField<MeasurementType>(
            isExpanded: true,
            decoration: InputDecoration(
              contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              border: InputBorder.none,
              fillColor: Colors.white,
              filled: true,
            ),
            value: _selectedChartType,
            items: const [
              DropdownMenuItem(value: MeasurementType.displacement, child: Text('位移')),
              DropdownMenuItem(value: MeasurementType.envStatus, child: Text('环境状态')),
            ],
            onChanged: (MeasurementType? value) {
              if (value != null) {
                setState(() {
                  _selectedChartType = value;
                });
              }
            },
          ),
        ),
      ],
    );
  }

  Widget _buildTargetSelector(BuildContext ctx) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text('标靶选择', style: TextStyle(
          fontWeight: FontWeight.bold,
          fontSize: 16,
        )),
        const SizedBox(height: 10),
        Container(
          decoration: BoxDecoration(
            border: Border.all(color: Colors.grey.shade300),
            borderRadius: BorderRadius.circular(10),
          ),
          child: BlocBuilder<TargetBloc, TargetState>(
            builder: (context, state) {
              debugPrint('当前选中的标靶ID: ${state.selectedTargetId}');
              return TargetDropDownSearch(
                onChanged: (Target target) {
                  context.read<TargetBloc>().add(TargetSelected(target.targetId));
                },
                selectedTargetId: state.selectedTargetId,
                hintText: '请选择标靶',
                dropdownWidth: double.infinity,
             
              );
            },
          ),
        ),
      ],
    );
  }

  Widget _buildTimeRangeControls() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text('时间范围', style: TextStyle(
          fontWeight: FontWeight.bold,
          fontSize: 16,
        )),
        const SizedBox(height: 10),

        // 实时/历史数据切换
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          decoration: BoxDecoration(
            color: Colors.grey.shade50,
            border: Border.all(color: Colors.grey.shade200),
            borderRadius: BorderRadius.circular(10),
          ),
          child: Row(
            children: [
              Switch(
                value: _isRealtime,
                activeColor: Theme.of(context).primaryColor,
                onChanged: (value) {
                  setState(() {
                    _isRealtime = value;
                  });
                },
              ),
              const SizedBox(width: 8),
              Text(
                '实时数据',
                style: TextStyle(
                  fontSize: 15,
                  color: _isRealtime ? Theme.of(context).primaryColor : Colors.grey.shade700,
                  fontWeight: _isRealtime ? FontWeight.w500 : FontWeight.normal,
                ),
              ),
            ],
          ),
        ),

        const SizedBox(height: 16),

        // 根据模式显示不同的控件
        if (_isRealtime)
          _buildTimeWindowSelector()
        else
          _buildDateRangeSelector(),
      ],
    );
  }

  Widget _buildTimeWindowSelector() {
    // 预定义快捷时间选项
    final quickTimeOptions = [
      {'label': '1分钟', 'value': const Duration(minutes: 1)},
      {'label': '5分钟', 'value': const Duration(minutes: 5)},
      {'label': '10分钟', 'value': const Duration(minutes: 10)},
      {'label': '30分钟', 'value': const Duration(minutes: 30)},
      {'label': '1小时', 'value': const Duration(hours: 1)},
      {'label': '6小时', 'value': const Duration(hours: 6)},
      {'label': '12小时', 'value': const Duration(hours: 12)},
      {'label': '1天', 'value': const Duration(days: 1)},
      {'label': '7天', 'value': const Duration(days: 7)},
    ];

    // 找到当前选中的预设选项
    bool isCustomDuration = !quickTimeOptions.any((option) => option['value'] == _selectedTimeWindow);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 快捷选择按钮组
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: [
            ...quickTimeOptions.map((option) {
              final duration = option['value'] as Duration;
              final isSelected = _selectedTimeWindow == duration;

              return ChoiceChip(
                label: Text(option['label'] as String),
                selected: isSelected,
                selectedColor: Theme.of(context).primaryColor.withOpacity(0.2),
                onSelected: (selected) {
                  if (selected) {
                    setState(() {
                      _selectedTimeWindow = duration;
                    });
                  }
                },
              );
            }).toList(),

            // 自定义选项
            ChoiceChip(
              label: const Text('自定义'),
              selected: isCustomDuration,
              selectedColor: Theme.of(context).primaryColor.withOpacity(0.2),
              onSelected: (selected) {
                if (selected) {
                  _showCustomDurationDialog();
                }
              },
            ),
          ],
        ),

        // 如果是自定义值，显示当前选择
        if (isCustomDuration)
          Padding(
            padding: const EdgeInsets.only(top: 8),
            child: Chip(
              label: Text('当前: ${_formatDuration(_selectedTimeWindow)}'),
              deleteIcon: const Icon(Icons.edit, size: 16),
              onDeleted: _showCustomDurationDialog,
            ),
          ),
      ],
    );
  }

// 格式化时间间隔为易读的文本
  String _formatDuration(Duration duration) {
    if (duration.inDays > 0) {
      return '${duration.inDays}天';
    } else if (duration.inHours > 0) {
      return '${duration.inHours}小时';
    } else {
      return '${duration.inMinutes}分钟';
    }
  }

// 显示自定义时长对话框
  void _showCustomDurationDialog() {
    final TextEditingController valueController = TextEditingController();
    String selectedUnit = '分钟';

    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('自定义时间范围'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Row(
                children: [
                  Expanded(
                    flex: 1,
                    child: TextField(
                      controller: valueController,
                      keyboardType: TextInputType.number,
                      decoration: const InputDecoration(
                        labelText: '数值',
                        border: OutlineInputBorder(),
                      ),
                    ),
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    flex: 2,
                    child: StatefulBuilder(
                      builder: (context, setState) {
                        return DropdownButtonFormField<String>(
                          value: selectedUnit,
                          decoration: const InputDecoration(
                            labelText: '单位',
                            border: OutlineInputBorder(),
                          ),
                          items: const [
                            DropdownMenuItem(value: '分钟', child: Text('分钟')),
                            DropdownMenuItem(value: '小时', child: Text('小时')),
                            DropdownMenuItem(value: '天', child: Text('天')),
                          ],
                          onChanged: (value) {
                            if (value != null) {
                              setState(() {
                                selectedUnit = value;
                              });
                            }
                          },
                        );
                      },
                    ),
                  ),
                ],
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('取消'),
            ),
            ElevatedButton(
              onPressed: () {
                // 将用户输入转换为Duration
                int? value = int.tryParse(valueController.text);
                if (value != null && value > 0) {
                  Duration customDuration;
                  switch (selectedUnit) {
                    case '分钟':
                      customDuration = Duration(minutes: value);
                      break;
                    case '小时':
                      customDuration = Duration(hours: value);
                      break;
                    case '天':
                      customDuration = Duration(days: value);
                      break;
                    default:
                      customDuration = Duration(minutes: value);
                  }

                  setState(() {
                    _selectedTimeWindow = customDuration;
                  });
                  Navigator.of(context).pop();
                } else {
                  // 显示错误提示
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(content: Text('请输入有效的正数值')),
                  );
                }
              },
              child: const Text('确定'),
            ),
          ],
        );
      },
    );
  }
  Widget _buildDateRangeSelector() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 快捷时间范围选择
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: [
            ChoiceChip(
              label: const Text('最近1小时'),
              selected: false,
              onSelected: (selected) {
                if (selected) {
                  setState(() {
                    _endDate = DateTime.now();
                    _startDate = _endDate.subtract(const Duration(hours: 1));
                  });
                }
              },
            ),
            ChoiceChip(
              label: const Text('今天'),
              selected: false,
              onSelected: (selected) {
                if (selected) {
                  final now = DateTime.now();
                  setState(() {
                    _endDate = now;
                    _startDate = DateTime(now.year, now.month, now.day);
                  });
                }
              },
            ),
            ChoiceChip(
              label: const Text('昨天'),
              selected: false,
              onSelected: (selected) {
                if (selected) {
                  final now = DateTime.now();
                  final today = DateTime(now.year, now.month, now.day);
                  setState(() {
                    _startDate = today.subtract(const Duration(days: 1));
                    _endDate = today.subtract(const Duration(milliseconds: 1));
                  });
                }
              },
            ),
            ChoiceChip(
              label: const Text('最近7天'),
              selected: false,
              onSelected: (selected) {
                if (selected) {
                  setState(() {
                    _endDate = DateTime.now();
                    _startDate = _endDate.subtract(const Duration(days: 7));
                  });
                }
              },
            ),
            ChoiceChip(
              label: const Text('最近30天'),
              selected: false,
              onSelected: (selected) {
                if (selected) {
                  setState(() {
                    _endDate = DateTime.now();
                    _startDate = _endDate.subtract(const Duration(days: 30));
                  });
                }
              },
            ),
            ChoiceChip(
              label: const Text('自定义'),
              selected: false,
              onSelected: (selected) {
                if (selected) {
                  // 保持当前选择的时间范围
                }
              },
            ),
          ],
        ),

        const SizedBox(height: 16),

        // 自定义日期选择器
        Row(
          children: [
            Expanded(
              child: _buildDateTimeField(true, '开始时间', _startDate),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: _buildDateTimeField(false, '结束时间', _endDate),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildDateTimeField(bool isStart, String label, DateTime value) {
    return InkWell(
      onTap: () => _selectDate(isStart),
      child: InputDecorator(
        decoration: InputDecoration(
          labelText: label,
          contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(10),
          ),
        ),
        child: Text(_dateFormat.format(value)),
      ),
    );
  }
  Future<void> _selectDate(bool isStart) async {
    final DateTime initialDate = isStart ? _startDate : _endDate;

    // 1. 先选择日期
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: initialDate,
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
      builder: (BuildContext context, Widget? child) {
        return Theme(
          data: Theme.of(context).copyWith(
            dialogTheme: const DialogTheme(
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.all(Radius.circular(16)),
              ),
            ),
          ),
          child: child!,
        );
      },
    );

    // 2. 如果选择了日期，继续选择时间
    if (picked != null) {
      final TimeOfDay? pickedTime = await showTimePicker(
        context: context,
        initialTime: TimeOfDay.fromDateTime(initialDate),
        builder: (context, child) {
          return Theme(
            data: Theme.of(context).copyWith(
              timePickerTheme: TimePickerThemeData(
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(16),
                ),
              ),
            ),
            child: child!,
          );
        },
      );

      // 3. 如果选择了时间，更新状态
      if (pickedTime != null) {
        setState(() {
          if (isStart) {
            _startDate = DateTime(
              picked.year,
              picked.month,
              picked.day,
              pickedTime.hour,
              pickedTime.minute,
            );
          } else {
            _endDate = DateTime(
              picked.year,
              picked.month,
              picked.day,
              pickedTime.hour,
              pickedTime.minute,
            );
          }
        });

        // 4. 验证时间范围逻辑
        if (_startDate.isAfter(_endDate)) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('开始时间不能晚于结束时间')),
          );
          setState(() {
            if (isStart) {
              _startDate = _endDate.subtract(const Duration(hours: 1));
            } else {
              _endDate = _startDate.add(const Duration(hours: 1));
            }
          });
        }
      }
    }
  }
}

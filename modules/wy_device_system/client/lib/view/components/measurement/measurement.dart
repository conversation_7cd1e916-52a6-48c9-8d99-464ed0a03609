import 'dart:async';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';

import '../../../bloc/measurement_bloc.dart';
import '../../../bloc/wy_device_blocs.dart';
import '../../../common/utils.dart';
import '../../../model/target.dart';
import '../../../model/wy_device.dart';
import '../../../repository/telemetry_repository.dart';
import '../charts/chart2.dart';
import '../charts/chart_sparkline.dart';
import '../charts/flexible-line-chart.dart';
import '../charts/measurement_chart.dart';
import '../charts/spark-line-chart.dart';
import '../export_data/export_data_panel.dart';
import 'chart_dashboard.dart';
import 'measurement_control_panel.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:inteagle_monitoring_robot_app/src/bindings/bindings.dart';
import 'package:dio/dio.dart';
import 'package:utils/utils.dart';
import 'package:tdesign_flutter/tdesign_flutter.dart';

class MeasurementDashboard extends StatefulWidget {
  final List<Target> targets;
  final MeasurementChartBloc measurementChartBloc;


  const MeasurementDashboard({
    Key? key,
    required this.targets,
    required this.measurementChartBloc,
  }) : super(key: key);

  @override
  State<MeasurementDashboard> createState() => _MeasurementDashboardState();
}

class _MeasurementDashboardState extends State<MeasurementDashboard> {
  WyDeviceStatus _status = WyDeviceStatus.idle;
  bool _showControlPanel = false;
  late WyDeviceBloc _wyDeviceBloc;
  int _recordLength = 0;
  StreamSubscription? _displacementSubscription;
  final TelemetryRepository _telemetryRepository = TelemetryRepository();

  @override
  void initState() {
    super.initState();
    _wyDeviceBloc = context.read<WyDeviceBloc>();

  }
  void dispose() {
    _displacementSubscription?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final isLargeScreen = screenWidth > 900;

    return BlocBuilder<WyDeviceBloc, WyDeviceState>(
      builder: (context, state) {
        _status = state.wyDeviceStatus ?? WyDeviceStatus.idle;

        if (isLargeScreen) {
          return _buildDesktopLayout();
        } else {
          return _buildMobileLayout();
        }
      },
    );
  }

  Widget _buildDesktopLayout() {
    // 宽屏时使用分栏布局
    return Row(
      children: [
        Expanded(
            child:  ChartDashboard()
        ),


        // 数据同步控制面板
        // Container(
        //   width: 250,
        //   margin: const EdgeInsets.symmetric(vertical: 8),
        //   child: Card(
        //     elevation: 3,
        //     child: Padding(
        //       padding: const EdgeInsets.all(12.0),
        //       child: Column(
        //         crossAxisAlignment: CrossAxisAlignment.start,
        //         mainAxisSize: MainAxisSize.min,
        //         children: [
        //           // Text('数据同步',
        //           //   style: Theme.of(context).textTheme.titleMedium?.copyWith(
        //           //       fontWeight: FontWeight.bold
        //           //   ),
        //           // ),
        //           const SizedBox(height: 16),
        //           // 同步状态显示
        //           // Row(
        //           //   children: [
        //           //     Container(
        //           //       width: 12,
        //           //       height: 12,
        //           //       decoration: BoxDecoration(
        //           //         color: _isSyncing ? Colors.green : Colors.grey,
        //           //         shape: BoxShape.circle,
        //           //       ),
        //           //     ),
        //           //     const SizedBox(width: 8),
        //           //     Text(_isSyncing ? '同步中' : '未同步',
        //           //       style: TextStyle(
        //           //         color: _isSyncing ? Colors.green : Colors.grey,
        //           //       ),
        //           //     ),
        //           //   ],
        //           // ),
        //           const SizedBox(height: 16),
        //           // 同步控制按钮
        //           // Row(
        //           //   mainAxisAlignment: MainAxisAlignment.spaceBetween,
        //           //   children: [
        //           //     // ElevatedButton.icon(
        //           //     //   onPressed: _toggleSyncStatus,
        //           //     //   icon: Icon(_isSyncing ? Icons.pause : Icons.sync),
        //           //     //   label: Text(_isSyncing ? '暂停同步' : '开始同步'),
        //           //     //   style: ElevatedButton.styleFrom(
        //           //     //     backgroundColor: _isSyncing ? Colors.orange : Colors.blue,
        //           //     //   ),
        //           //     // ),
        //           //     IconButton(
        //           //       onPressed: sync,
        //           //       icon: const Icon(Icons.settings),
        //           //       tooltip: '同步设置',
        //           //     ),
        //           //   ],
        //           // ),
        //           const SizedBox(height: 12),
        //           // 最后同步时间
        //           // if (_lastSyncTime != null)
        //           //   Text(
        //           //     '上次同步: ${_formatDateTime(_lastSyncTime!)}',
        //           //     style: const TextStyle(fontSize: 12, color: Colors.grey),
        //           //   ),
        //         ],
        //       ),
        //     ),
        //   ),
        // ),




        // 右侧控制面板
        AnimatedContainer(
          duration: const Duration(milliseconds: 300),
          width: _showControlPanel ? 350 : 50,
          curve: Curves.easeInOut,
          child: Card(
            margin: const EdgeInsets.all(8),
            child: Column(
              children: [
                // 控制面板展开/收起按钮
                InkWell(
                  onTap: () {
                    setState(() {
                      _showControlPanel = !_showControlPanel;
                    });
                  },
                  child: Container(
                    height: 48,
                    padding: const EdgeInsets.symmetric(horizontal: 16),
                    alignment: Alignment.centerLeft,
                    child: Row(
                      children: [
                        Icon(
                          _showControlPanel
                              ? Icons.keyboard_arrow_right
                              : Icons.keyboard_arrow_left,
                          color: Theme.of(context).primaryColor,
                        ),
                        if (_showControlPanel) ...[
                          const SizedBox(width: 8),
                          const Text('测量控制',
                              style: TextStyle(fontWeight: FontWeight.bold)),
                          const Spacer(),
                          _buildStatusWidget(),
                        ],
                      ],
                    ),
                  ),
                ),

                // 控制面板内容
                if (_showControlPanel)
                  Expanded(
                    child: MeasurementControlPanel(
                      status: _status,
                      onMeasurementRequest: _handleMeasurementRequest,
                    ),
                  ),
              ],
            ),
          ),
        ),


      ],
    );
  }

  Future<void> sync() async {

    String deviceId = context.read<WyDeviceBloc>().state.deviceId;
    String ipAddress = context.read<WyDeviceBloc>().state.ipAddress;
    String dbPath = await getDatabasePath(deviceId);

    debugPrint("sync: 同步按钮已按下");

    SyncServiceControl(
      msgType:SyncServiceControlMsgType.start,
      databasePath: dbPath,
      baseApi: "http://${ipAddress}:9999",
      pollInterval: 1000,
    ).sendSignalToRust();

    _showSyncDialog();
  }

  Widget _buildMobileLayout() {
    // 窄屏时使用底部弹出式布局
    return Scaffold(

      body:ChartDashboard(),
      floatingActionButton: Row(
        mainAxisSize: MainAxisSize.min,
        children: [

          // 显示记录长度
          if (_recordLength > 0)
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              margin: const EdgeInsets.only(right: 8),
              decoration: BoxDecoration(
                color: Colors.blue.withOpacity(0.2),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Text('记录: $_recordLength', style: const TextStyle(fontSize: 12)),
            ),
          const SizedBox(width: 8), // 使用一个占位符来保持布局正常

          FloatingActionButton(
            heroTag: "同步",
            onPressed: sync,
            backgroundColor: Colors.blue,
            child: const Icon(Icons.sync_rounded),
          ),

          // 导出数据按钮
          FloatingActionButton(
            heroTag: "exportData",
            onPressed: _showExportBottomSheet,
            backgroundColor: Colors.blue,
            child: const Icon(Icons.download),
          ),
          const SizedBox(width: 16),
          // 测量控制按钮
          FloatingActionButton.extended(
            heroTag: "measurementControl",
            onPressed: _showControlBottomSheet,
            icon: Icon(_getActionIcon()),
            label: Text(_getActionText()),
            backgroundColor: _getActionColor(),
          ),
        ],
      ),
    );
  }

  void _showSyncDialog() {
    debugPrint("_showSyncDialog-1");
    final dashboardContext = context;
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) {
        return Dialog(
          backgroundColor: Colors.white,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          child: Container(
            padding: const EdgeInsets.all(24),
            constraints: const BoxConstraints(
              maxWidth: 400,
              minWidth: 300,
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // 标题栏
                Row(
                  children: [
                    const Text('同步数据',
                        style: TextStyle(
                            fontSize: 18, fontWeight: FontWeight.bold)),
                    const Spacer(),
                  ],
                ),
                const SizedBox(height: 40),
                StreamBuilder(
                  stream: SyncProgress.rustSignalStream, // GENERATED
                  builder: (context, snapshot) {
                    final signalPack = snapshot.data;
                    if (signalPack == null) {
                      return TDProgress(
                          type: TDProgressType.linear,
                          value: 0,
                          strokeWidth: 6,
                          progressLabelPosition: TDProgressLabelPosition.right);
                    };
                    // bug:TDProgressLabel 不同步
                    debugPrint("同步数据进度:${signalPack.message.progressPercentage}");
                    if(signalPack.message.progressPercentage>=100){
                      SyncServiceControl(
                          msgType: SyncServiceControlMsgType.stop,
                          databasePath: '',
                          baseApi: '',
                          pollInterval: 10
                      )
                          .sendSignalToRust();
                      Navigator.of(context).pop();
                    }
                    return TDProgress(
                        type: TDProgressType.linear,
                        progressStatus: TDProgressStatus.primary,
                        value: signalPack.message.progressPercentage/100,
                        strokeWidth: 6,
                        progressLabelPosition: TDProgressLabelPosition.right);
                  },
                ),
                const SizedBox(height: 40),
                TDButton(
                  text:  '取消同步',
                  size: TDButtonSize.large,
                  type: TDButtonType.fill,
                  shape: TDButtonShape.rectangle,
                  theme: TDButtonTheme.primary,
                  onTap: () {
                    SyncServiceControl(
                        msgType: SyncServiceControlMsgType.stop,
                        databasePath: '',
                        baseApi: '',
                        pollInterval: 10
                    )
                        .sendSignalToRust();
                    Navigator.of(context).pop();
                  },
                )
              ],
            ),
          ),
        );
      },
    );
  }

  void _showExportBottomSheet() {
    final deviceId=context.read<WyDeviceBloc>().state.deviceId;
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      builder: (context) {
        return DraggableScrollableSheet(
          initialChildSize: 0.6,
          minChildSize: 0.3,
          maxChildSize: 0.9,
          expand: false,
          builder: (context, scrollController) {
            return Container(
              padding: const EdgeInsets.fromLTRB(16, 0, 16, 0),
              child: Column(
                children: [
                  // 拖动条
                  Container(
                    margin: const EdgeInsets.symmetric(vertical: 12),
                    height: 4,
                    width: 40,
                    decoration: BoxDecoration(
                      color: Colors.grey[300],
                      borderRadius: BorderRadius.circular(2),
                    ),
                  ),

                  // 标题栏
                  Row(
                    children: [
                      const Text('导出数据',
                          style: TextStyle(
                              fontSize: 18, fontWeight: FontWeight.bold)),
                      const Spacer(),
                      IconButton(
                        icon: const Icon(Icons.close),
                        onPressed: () => Navigator.pop(context),
                      ),
                    ],
                  ),
                  // 导出面板
                  Expanded(
                    child: ExportDataPanel(
                      deviceId:deviceId,
                      targets: widget.targets,
                      measurementChartBloc: widget.measurementChartBloc,
                      externalScrollController: scrollController,
                      telemetryRepository: _telemetryRepository,
                    ),
                  ),
                ],
              ),
            );
          },
        );
      },
    );
  }

  void _showControlBottomSheet() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      builder: (context) {
        return DraggableScrollableSheet(
          initialChildSize: 0.6,
          minChildSize: 0.3,
          maxChildSize: 0.9,
          expand: false,
          builder: (context, scrollController) {
            return Container(
              padding: const EdgeInsets.fromLTRB(16, 0, 16, 0),
              child: Column(
                children: [
                  // 拖动条
                  Container(
                    margin: const EdgeInsets.symmetric(vertical: 12),
                    height: 4,
                    width: 40,
                    decoration: BoxDecoration(
                      color: Colors.grey[300],
                      borderRadius: BorderRadius.circular(2),
                    ),
                  ),

                  // 标题栏
                  Row(
                    children: [
                      const Text('测量控制',
                          style: TextStyle(
                              fontSize: 18, fontWeight: FontWeight.bold)),
                      const Spacer(),
                      _buildStatusWidget(),
                      IconButton(
                        icon: const Icon(Icons.close),
                        onPressed: () => Navigator.pop(context),
                      ),
                    ],
                  ),

                  // 控制面板 - 使用Expanded和Column结构确保按钮在底部
                  Expanded(
                    child: MeasurementControlPanel(
                      status: _status,
                      // 传递外部滚动控制器，以便控制面板可以与抽屉协调滚动
                      externalScrollController: scrollController,
                      onMeasurementRequest: (request) {
                        Navigator.pop(context);
                        _handleMeasurementRequest(request);
                      },
                    ),
                  ),
                ],
              ),
            );
          },
        );
      },
    );
  }

  Widget _buildStatusWidget() {
    Color color;
    String text;
    IconData icon;

    switch (_status) {
      case WyDeviceStatus.idle:
        color = Colors.grey;
        text = '空闲';
        icon = Icons.circle_outlined;
        break;
      case WyDeviceStatus.measuring:
        color = Colors.green;
        text = '测量中';
        icon = Icons.fiber_manual_record;
        break;
      case WyDeviceStatus.initializing:
        color = Colors.orange;
        text = '初始化中';
        icon = Icons.pending_outlined;
        break;
      case WyDeviceStatus.testing:
        color = Colors.blue;
        text = '调试中';
        icon = Icons.check_circle_outline;
        break;
      case WyDeviceStatus.accuracyCalibration:
        color = Colors.yellow;
        text = '精度标定中';
        icon = Icons.build_circle_outlined;
        break;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withOpacity(0.2),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 12, color: color),
          const SizedBox(width: 4),
          Text(text, style: TextStyle(color: color, fontSize: 12)),
        ],
      ),
    );
  }

  IconData _getActionIcon() {
    switch (_status) {
      case WyDeviceStatus.idle:
        return Icons.play_arrow;
      case WyDeviceStatus.measuring:
        return Icons.stop;
      case WyDeviceStatus.initializing:
        return Icons.pending;
      case WyDeviceStatus.testing:
        return Icons.check_circle;
      case WyDeviceStatus.accuracyCalibration:
        return Icons.build;
    }
  }

  String _getActionText() {
    switch (_status) {
      case WyDeviceStatus.idle:
        return '开始测量';
      case WyDeviceStatus.measuring:
        return '停止测量';
      case WyDeviceStatus.initializing:
        return '初始化中...';
      case WyDeviceStatus.testing:
        return '调试中...';
      case WyDeviceStatus.accuracyCalibration:
        return '标定中...';
    }
  }

  Color _getActionColor() {
    switch (_status) {
      case WyDeviceStatus.idle:
        return Colors.green;
      case WyDeviceStatus.measuring:
        return Colors.red;
      case WyDeviceStatus.initializing:
        return Colors.orange;
      case WyDeviceStatus.testing:
        return Colors.blue;
      case WyDeviceStatus.accuracyCalibration:
        return Colors.yellow;
    }
  }

  Future<void> _handleMeasurementRequest(Map<String, dynamic> request) async {


    debugPrint('Received measurement request: $request');
    // 处理测量请求
    if (request['method'] == 'startMeasurement') {
      try{
        var response = await HttpUtil.instance.client.post('/rpc', data: request);
        debugPrint('Start measurement response: ${response.data}');
      }catch(e){
        debugPrint("Start measurement  error => ${e}");
        String errorMessage = e is DioException
            ? (e.response?.data != null
            ? (e.response?.data['msg'] ?? e.response?.data.toString())
            : e.message ?? e.toString())
            : e.toString();
        debugPrint("Start measurement  errorMessage => ${errorMessage}");
        throw Exception("$errorMessage");
      }

    } else if (request['method'] == 'stopMeasurement') {

    // 停止所有摄像头的测量
      for (int cameraId = 0; cameraId < 2; cameraId++) {
        Map<String, dynamic> stopRequest = {
          'method': 'wyStopMeasure',
          'params': {
            "cameraId": cameraId
          },
        };
        final response = await HttpUtil.instance.client.post('/rpc', data: stopRequest);

        debugPrint('Stop measurement response: ${response.data}');
      }
    }
  }
}

import 'package:client/bloc/targets_blocs.dart';
import 'package:client/model/target.dart';
import 'package:client/services/export_service.dart';
import 'package:flutter/material.dart';
import 'package:tdesign_flutter/tdesign_flutter.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:intl/intl.dart';

class MeasurementExport extends StatefulWidget {
  final context;
  final buildContext;
  const MeasurementExport(
      {super.key, required this.context, required this.buildContext});

  @override
  State<MeasurementExport> createState() => _MeasurementExportState();
}

class _MeasurementExportState extends State<MeasurementExport> {
  late TargetBloc targetBloc;
  late List<Target> targets;
  // 选择的标靶列表
  List<String> _selectedTargets = [];
  // 选择的数据类型
  bool _includeEnvironmentalData = true;
  bool _includeIMUData = true;
  bool _includeTimeStamp = true;
  // 日期范围
  List<int>? _dateRange;
  // 导出格式
  final String _exportFormat = 'csv'; // 默认为CSV格式

  bool _isExporting = false;
  String _exportStatus = '';

  @override
  void initState() {
    targetBloc = BlocProvider.of<TargetBloc>(widget.context);
    targets = targetBloc.state.targets;
    _selectedTargets = targets.map((t) => t.targetId).toList();
    super.initState();
  }

  bool _validateExportParameters() {
    // 检查是否有选中的目标
    if (_selectedTargets.isEmpty) {
      TDToast.showText('请至少选择一个目标', context: context);
      return false;
    }

    // 检查是否有选中的数据类型
    if (!_includeEnvironmentalData && !_includeIMUData) {
      TDToast.showText('请至少选择一种数据类型', context: context);
      return false;
    }

    return true;
  }

  Future<void> _exportData(context) async {
    if (!_validateExportParameters()) {
      return;
    }
    setState(() {
      _isExporting = true;
      _exportStatus = '准备导出数据...';
    });
    try {
      // 构建导出请求参数
      final exportRequest = {
        'targets': _selectedTargets,
        'include_environmental': _includeEnvironmentalData,
        'include_imu': _includeIMUData,
        'include_timestamp': _includeTimeStamp,
        'format': _exportFormat,
        'start_date':
            _dateRange == null ? '' : _timestampToIso8601(_dateRange![0]),
        'end_date':
            _dateRange == null ? '' : _timestampToIso8601(_dateRange![1]),
      };

      // 调用导出服务
      final exportService = ExportService();
      final result = await exportService.exportData(exportRequest);
      if (result.success) {
        TDToast.showText('导出成功', context: context);
        Navigator.pop(widget.buildContext);
      } else {
        setState(() {
          _exportStatus = '导出失败';
          _isExporting = false;
        });
      }
    } catch (e) {
      setState(() {
        _exportStatus = '导出失败';
        _isExporting = false;
      });
    }
  }

  // 时间范围格式化显示
  String _formatDateRange(List<int> dateRange) {
    final start = DateFormat('yyyy-MM-dd')
        .format(DateTime.fromMillisecondsSinceEpoch(dateRange[0]));
    final end = DateFormat('yyyy-MM-dd')
        .format(DateTime.fromMillisecondsSinceEpoch(dateRange[1]));

    return '$start 至 $end';
  }

  // 时间戳转换为ISO 8601字符串
  String _timestampToIso8601(int value) {
    // 创建DateTime对象
    final dateTime = DateTime.fromMillisecondsSinceEpoch(value);
    // 转换为ISO 8601字符串
    return dateTime.toIso8601String();
  }

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size;
    return Padding(
        padding: const EdgeInsets.symmetric(vertical: 20, horizontal: 16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                TDText(
                  '导出数据',
                  font: TDTheme.of(context).fontHeadlineSmall,
                ),
                const SizedBox(width: 20),
                if (_exportStatus.isNotEmpty)
                  TDTag(
                    _exportStatus,
                    isLight: true,
                    theme: _exportStatus.contains('成功')
                        ? TDTagTheme.success
                        : _exportStatus.contains('失败')
                            ? TDTagTheme.danger
                            : TDTagTheme.primary,
                  ),
              ],
            ),
            const SizedBox(height: 20),
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const TDText('选择目标'),
                const SizedBox(height: 12),
                Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    GestureDetector(
                      onTap: () {
                        setState(() {
                          _selectedTargets =
                              targets.length == _selectedTargets.length
                                  ? []
                                  : targets.map((t) => t.targetId).toList();
                        });
                      },
                      child: Row(
                        children: [
                          getAllIcon(),
                          const SizedBox(width: 8),
                          const TDText('全部')
                        ],
                      ),
                    ),
                    const SizedBox(height: 8),
                    Wrap(
                      spacing: 12,
                      runSpacing: 8,
                      children: targets.map((t) {
                        return TDSelectTag(
                          onSelectChanged: (value) {
                            setState(() {
                              value
                                  ? _selectedTargets.add(t.targetId)
                                  : _selectedTargets.remove(t.targetId);
                            });
                          },
                          t.targetId,
                          size: TDTagSize.large,
                          theme: TDTagTheme.primary,
                          isLight: true,
                          isOutline: true,
                          isSelected: _selectedTargets.contains(t.targetId),
                        );
                      }).toList(),
                    ),
                  ],
                ),
              ],
            ),
            const SizedBox(height: 20),
            Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const TDText('数据类型'),
                Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    TDCheckbox(
                        onCheckBoxChanged: (selected) {
                          setState(() {
                            _includeEnvironmentalData = selected;
                          });
                        },
                        title: '环境数据',
                        subTitle: '温度、湿度、气压等',
                        style: TDCheckboxStyle.square,
                        showDivider: false,
                        checked: _includeEnvironmentalData),
                    TDCheckbox(
                        onCheckBoxChanged: (selected) {
                          setState(() {
                            _includeIMUData = selected;
                          });
                        },
                        title: 'IMU数据',
                        subTitle: '加速度、角速度、姿态等',
                        style: TDCheckboxStyle.square,
                        showDivider: false,
                        checked: _includeIMUData),
                    TDCheckbox(
                        onCheckBoxChanged: (selected) {
                          setState(() {
                            _includeTimeStamp = selected;
                          });
                        },
                        title: '时间戳',
                        style: TDCheckboxStyle.square,
                        showDivider: false,
                        checked: _includeTimeStamp),
                  ],
                )
              ],
            ),
            const SizedBox(height: 20),
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const TDText('时间范围'),
                const SizedBox(height: 12),
                GestureDetector(
                  onTap: () {
                    TDCalendarPopup(
                      context,
                      visible: true,
                      onConfirm: (value) {
                        if (value.length < 2) {
                          TDToast.showText('请选择正确的时间范围', context: context);
                          return;
                        }
                        setState(() {
                          final endT =
                              DateTime.fromMillisecondsSinceEpoch(value[1]);
                          final endTime = DateTime(endT.year, endT.month,
                                  endT.day, 23, 59, 59, 999)
                              .millisecondsSinceEpoch;
                          _dateRange = [value[0], endTime];
                        });
                      },
                      child: TDCalendar(
                        title: '请选择时间范围',
                        type: CalendarType.range,
                        minDate: DateTime.now()
                            .subtract(const Duration(days: 365))
                            .millisecondsSinceEpoch,
                        maxDate: DateTime.now().millisecondsSinceEpoch,
                        value: _dateRange == null
                            ? [
                                DateTime.now()
                                    .subtract(const Duration(days: 6))
                                    .millisecondsSinceEpoch,
                                DateTime.now().millisecondsSinceEpoch,
                              ]
                            : [
                                _dateRange![0],
                                _dateRange![1],
                              ],
                        height: size.height * 0.6,
                      ),
                    );
                  },
                  child: Container(
                    padding:
                        const EdgeInsets.symmetric(vertical: 8, horizontal: 12),
                    decoration: BoxDecoration(
                        border: Border.all(
                            color: TDTheme.of(context).fontGyColor3, width: 1)),
                    child: Row(
                      children: [
                        Icon(
                          TDIcons.calendar_1,
                          color: TDTheme.of(context).fontGyColor3,
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                            child: TDText(
                          _dateRange == null
                              ? '选择时间范围'
                              : _formatDateRange(_dateRange!),
                          font: TDTheme.of(context).fontBodyLarge,
                          textColor: _dateRange == null
                              ? TDTheme.of(context).fontGyColor4
                              : TDTheme.of(context).grayColor13,
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        )),
                        const Icon(TDIcons.chevron_down)
                      ],
                    ),
                  ),
                )
              ],
            ),
            const SizedBox(height: 20),
            Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const TDText('导出格式'),
                TDRadioGroup(
                  selectId: _exportFormat,
                  child: const Column(
                    children: [
                      TDRadio(
                        id: 'csv',
                        title: 'CSV (.csv)',
                        showDivider: false,
                      ),
                      TDRadio(
                        id: 'xlsx',
                        title: 'Excel (.xlsx)',
                        showDivider: false,
                      ),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 20),
            TDButton(
              onTap: () => _exportData(context),
              text: '导出数据',
              size: TDButtonSize.large,
              type: TDButtonType.fill,
              theme: TDButtonTheme.primary,
              isBlock: true,
              disabled: _isExporting,
            ),
          ],
        ));
  }

  // 全选框的样式
  Widget getAllIcon() {
    // 全选
    var checked = targets.length == _selectedTargets.length;
    // 半选
    var halfSelected = !checked && _selectedTargets.isNotEmpty;
    return Icon(
        checked
            ? TDIcons.check_rectangle_filled
            : halfSelected
                ? TDIcons.minus_rectangle_filled
                : TDIcons.rectangle,
        size: 22,
        color: (checked || halfSelected)
            ? TDTheme.of(context).brandNormalColor
            : TDTheme.of(context).grayColor4);
  }
}

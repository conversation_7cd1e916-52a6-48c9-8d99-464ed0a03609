import 'dart:async';
import 'dart:math';
import 'package:client/bloc/measurement_bloc.dart';
import 'package:client/bloc/targets_blocs.dart';
import 'package:client/bloc/wy_device_blocs.dart';
import 'package:client/model/measurement_query.dart';
import 'package:client/model/target.dart';
import 'package:client/repository/telemetry_repository.dart';
import 'package:client/view/device_home/data_view.dart';
import 'package:client/view/device_home/device_home.dart';
import 'package:client/view/device_home/device_status_view_model.dart';
import 'package:client/view/device_home/measurement_export.dart';
import 'package:client/view/device_home/measurement_start_panel.dart';

import 'package:client/view/widgets/empty.dart';
import 'package:client/view/components/targets/target_picker.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:tdesign_flutter/tdesign_flutter.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import 'package:intl/intl.dart';
import 'package:fl_chart/fl_chart.dart';
import 'package:inteagle_monitoring_robot_app/src/bindings/bindings.dart';
import '../../bloc/wy_device_blocs.dart';
import '../../common/utils.dart';

class MeasurementTab extends StatefulWidget {
  final void Function(String) toPath;
  const MeasurementTab({super.key, required this.toPath});

  @override
  State<MeasurementTab> createState() => _MeasurementTabState();
}

class _MeasurementTabState extends State<MeasurementTab> {
  late TargetBloc _targetBloc;
  late List<Target> _targets;
  final Random _random = Random();
  // 是否表格展示
  bool isShowTable = false;
  // 选择的数据类型（位移、环境）
  MeasurementType _selectedChartType = MeasurementType.displacement;
  bool _isRealTime = true;
  // 快速选择的时间
  Duration? _selectedTime = const Duration(minutes: 10);
  // 选择的标靶
  String _selectedTargetId = '';

  // 选择的历史数据时间
  int? _startTime;
  int? _endTime;
  // 图表数据
  List<DataPoint> _data = [];

  // 设置警告音
  // final AudioPlayer _audioPlayer = AudioPlayer();
  // _audioPlayer.play(AssetSource('images/warning.mp4'));
  @override
  void initState() {
    super.initState();
    _targetBloc = context.read<TargetBloc>();
    _targets = _targetBloc.state.targets;
    if (_targets.isNotEmpty) {
      context.read<TargetBloc>().add(TargetSelected(_targets[0].targetId));
      _selectedTargetId = _targets[0].targetId;
    }
    _loadData();
    // 订阅 MeasurementChartBloc 状态变化
    // _setupMeasurementBlocListener();
  }

  // void _setupMeasurementBlocListener() {

  //   final measurementChartBloc = context.read<MeasurementChartBloc>();
  //   _chartBlocSubscription =
  //       measurementChartBloc.telemetryStreamController.stream.listen((data) {
  //     //提取出当前选择的标靶数据
  //     debugPrint('Received data from WebSocket: $data');
  //     if (data.containsKey("Displacements")) {
  //       final displacements = data["Displacements"] as List;
  //       final targetData = displacements
  //           .where((item) => item['targetId'] == _selectedTargetId)
  //           .map((item) {
  //         // 将数据格式化为图表需要的格式
  //         return {
  //           'timestamp': DateTime.fromMillisecondsSinceEpoch(item['ts']),
  //           'sigmaX': item['sigmaX'],
  //           'sigmaY': item['sigmaY'],
  //         };
  //       }).toList();

  //       if (targetData.isNotEmpty) {
  //         print(targetData);
  //       }
  //     }
  //   });
  // }
  Future<void> _loadData() async {
    print({
      '是否实时数据: ${_isRealTime ? '是' : '否'}',
      '选择的数据类型：$_selectedChartType',
      '选择的标靶id：$_selectedTargetId',
      '选择的开始时间：$_startTime',
      '选择的结束时间：$_endTime'
    });
    // 模拟API调用
    await Future.delayed(const Duration(seconds: 1));
    // 假设得到的数据如下：
    final List<DataPoint> newData = [
      DataPoint(timestamp: 1750429384027.0, sigmaX: 1.0, sigmaY: 3.1),
      DataPoint(timestamp: 1750429385027.0, sigmaX: -1.0, sigmaY: 4.2),
      DataPoint(timestamp: 1750429386027.0, sigmaX: 2.39, sigmaY: 2.0),
      DataPoint(timestamp: 1750429387027.0, sigmaX: -2.11, sigmaY: 2.3),
      DataPoint(timestamp: 1750429388027.0, sigmaX: 5.0, sigmaY: -1.55),
    ];
    setState(() {
      _data = newData;
    });
    // _data.add(item);
    // if (_data.length > 20) {
    //   _data.removeAt(0);
    // }
    // final now = DateTime.now().millisecondsSinceEpoch;
    // final DataPoint item = DataPoint(
    //     timestamp: _endTime != null ? _endTime!.toDouble() : now.toDouble(),
    //     sigmaX: _random.nextDouble() * 20 - 10,
    //     sigmaY: _random.nextDouble() * 20 - 10);

    // if (_isRealTime) {
    //   // 实时数据
    //   // DateTime now = DateTime.now();
    //   // _startTime = now.subtract(_selectedTime as Duration).millisecondsSinceEpoch;
    //   // _endTime = now.millisecondsSinceEpoch;
    // } else {
    //   print({
    //     '选择的数据类型：$_selectedChartType',
    //     '选择的标靶id：$_selectedTargetId',
    //     '选择的开始时间：$_startTime',
    //     '选择的结束时间：$_endTime'
    //   });
    //   final types = findDataType(_selectedChartType).fields;
    //   String deviceId = context.read<WyDeviceBloc>().state.deviceId;
    //   final telemetryRepository = TelemetryRepository();
    //   final result = await telemetryRepository.queryDisplacement(
    //     deviceId,
    //     targetId: _selectedTargetId,
    //     startTs: _startTime,
    //     endTs: _endTime,
    //   );
    //   print(result);
    // }
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<TargetBloc, TargetState>(
      builder: (context, state) {
        if (state.targets.isEmpty) {
          return Empty(
              icon: TDIcons.task_error,
              emptyText: '未绑定标靶',
              child: TDButton(
                text: '去绑定',
                size: TDButtonSize.small,
                theme: TDButtonTheme.primary,
                onTap: () => widget.toPath('home'),
              ));
        } else {
          return SingleChildScrollView(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // 数据切换
                _buildDataChange(context),
                // 图表或者表格数据展示
                _buildDataView(context),
                // 操作按钮
                _buildOperations(context),
                // 选择标靶
                _buildTargetPicker(context, state),
                _buildTimeFilter()
              ],
            ),
          );
        }
      },
    );
  }

  // 数据切换
  Widget _buildDataChange(context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        GestureDetector(
          onTap: () {
            TDActionSheet(
              context,
              visible: true,
              items: dataTypeList
                  .map((item) => TDActionSheetItem(label: item.label))
                  .toList(),
              onSelected: (item, index) {
                if (_selectedChartType != dataTypeList[index].value) {
                  setState(() {
                    _selectedChartType = dataTypeList[index].value;
                    // 如果选择的是环境数据，没有实时数据，只有历史数据
                    // if (_selectedChartType != MeasurementType.displacement) {
                    //   _isRealTime = false;
                    // }
                    _loadData();
                  });
                }
              },
            );
          },
          child: Row(
            children: [
              Container(
                width: 10, // 圆点宽度
                height: 10, // 圆点高度
                decoration: BoxDecoration(
                  color: TDTheme.of(context).brandNormalColor, // 圆点颜色
                  borderRadius: BorderRadius.circular(5), // 圆角半径，确保为圆形
                ),
              ),
              const SizedBox(
                width: 12,
              ),
              TDText(findDataType(_selectedChartType).label,
                  font: TDTheme.of(context).fontTitleExtraLarge),
              const SizedBox(
                width: 12,
              ),
              const Icon(TDIcons.chevron_down)
            ],
          ),
        ),
        TDButton(
          text: isShowTable ? '表格' : '图表',
          icon: isShowTable ? TDIcons.table : TDIcons.chart_line,
          size: TDButtonSize.small,
          type: TDButtonType.text,
          onTap: () {
            setState(() {
              isShowTable = !isShowTable;
            });
          },
        ),
      ],
    );
  }

  // 数据展示（图表或表格）
  Widget _buildDataView(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 10),
      height: 360,
      child: _data.isEmpty
          ? const Center(child: CircularProgressIndicator())
          : DataView(
              dataPoints: _data,
              isShowTable: isShowTable,
            ),
    );
  }

  // 操作按钮
  Widget _buildOperations(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Row(
          children: [
            TDButton(
              onTap: () => widget.toPath('home'),
              text: '初始化',
              size: TDButtonSize.small,
              type: TDButtonType.fill,
              theme: TDButtonTheme.primary,
            ),
            const SizedBox(
              width: 12,
            ),
            TDButton(
              onTap: () => _startMeasure(context),
              text: '启动测量',
              size: TDButtonSize.small,
              type: TDButtonType.fill,
              theme: TDButtonTheme.light,
            )
          ],
        ),
        Row(
          children: [
            IconButton(
                onPressed: () => _syncData(context),
                color: TDTheme.of(context).brandNormalColor,
                tooltip: '同步数据',
                icon: const Icon(TDIcons.refresh, size: 18)),
            // MeasurementExport(context: context),
            IconButton(
                onPressed: () => _exportData(context),
                color: TDTheme.of(context).brandNormalColor,
                tooltip: '导出数据',
                icon: const Icon(TDIcons.file_download, size: 18))
          ],
        )
      ],
    );
  }

  // 选择标靶
  Widget _buildTargetPicker(context, state) {
    return GestureDetector(
        onTap: () {
          Navigator.of(context).push(TDSlidePopupRoute(
              modalBarrierColor: TDTheme.of(context).fontGyColor2,
              slideTransitionFrom: SlideTransitionFrom.center,
              builder: (context) {
                return TargetPicker(
                  selected: _selectedTargetId,
                  context: context,
                  targetList: _targets,
                  onSelected: (selected) {
                    setState(() {
                      _selectedTargetId = selected;
                      _targetBloc.add(TargetSelected(selected));
                    });
                    _loadData();
                    Navigator.maybePop(context);
                  },
                );
              }));
        },
        child: Padding(
          padding: const EdgeInsets.symmetric(vertical: 8),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              TDText('选择标靶', font: TDTheme.of(context).fontBodyLarge),
              Expanded(
                child: Row(
                  children: [
                    Expanded(
                      child: TDText(
                        state.selectedTargetId ?? '选择标靶',
                        textAlign: TextAlign.end,
                        font: TDTheme.of(context).fontBodyLarge,
                        textColor:
                            TDTheme.of(context).fontGyColor3.withOpacity(0.4),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                    const SizedBox(width: 8),
                    Icon(
                      TDIcons.chevron_right,
                      color: TDTheme.of(context).fontGyColor3.withOpacity(0.4),
                    )
                  ],
                ),
              )
            ],
          ),
        ));
  }

  // 筛选时间
  Widget _buildTimeFilter() {
    return Column(
      children: [
        const TDDivider(),
        _buildTimeRow('实时数据', realTimeOptions),
        const TDDivider(),
        _buildTimeRow('历史数据', historyTimeOptions),
        if (_selectedTime == null)
          Column(
            children: [
              const TDDivider(),
              _buildTimePicker('开始时间', _startTime),
              const TDDivider(),
              _buildTimePicker('结束时间', _endTime),
            ],
          )
      ],
    );
  }

  Widget _buildTimeRow(String label, List<TimeOption> options) {
    return Padding(
        padding: const EdgeInsets.symmetric(vertical: 8),
        child: Row(children: [
          TDText(label, font: TDTheme.of(context).fontBodyLarge),
          Expanded(
            child: Wrap(
              alignment: WrapAlignment.end,
              spacing: 8,
              runSpacing: 8,
              children: options.map((option) {
                return TDSelectTag(
                  option.label,
                  key: ValueKey(option.value),
                  size: TDTagSize.large,
                  theme: TDTagTheme.primary,
                  isLight: true,
                  isOutline: true,
                  isSelected: _selectedTime == option.value,
                  onSelectChanged: (value) {
                    setState(() {
                      if (value) {
                        _selectedTime = option.value;
                        if (label == '实时数据') {
                          _isRealTime = true;
                        } else {
                          _isRealTime = false;
                          if (_selectedTime != null) {
                            DateTime now = DateTime.now();
                            _startTime = now
                                .subtract(_selectedTime as Duration)
                                .millisecondsSinceEpoch;
                            _endTime = now.millisecondsSinceEpoch;
                          }
                        }
                      }
                    });
                    if (_selectedTime != null) {
                      _loadData();
                    }
                  },
                );
              }).toList(),
            ),
          )
        ]));
  }

  // 选择自定义时间
  Widget _buildTimePicker(String label, int? time) {
    //  现在时间
    DateTime now = DateTime.now();
    List<int> dateStart = [];
    List<int> dateEnd = [
      now.year,
      now.month,
      now.day,
      now.hour,
      now.minute,
      now.second
    ];
    List<int> initialDate = dateEnd;
    String? timeStr;
    if (time != null) {
      timeStr = _formatTime(time);
      //  选择的时间
      DateTime date = DateTime.fromMillisecondsSinceEpoch(time);
      initialDate = [
        date.year,
        date.month,
        date.day,
        date.hour,
        date.minute,
        date.second
      ];
    }

    if (label == '结束时间' && _startTime != null) {
      DateTime date = DateTime.fromMillisecondsSinceEpoch(_startTime as int);
      dateStart = [
        date.year,
        date.month,
        date.day,
        date.hour,
        date.minute,
        date.second
      ];
    }
    return TDCell(
      onClick: (cell) {
        if (label == '结束时间' && _startTime == null) {
          TDToast.showText('请先选择开始时间', context: context);
          return;
        }
        TDPicker.showDatePicker(
          context,
          title: '选择$label',
          onConfirm: (selected) {
            final timestamp = DateTime(
                    selected['year']!,
                    selected['month']!,
                    selected['day']!,
                    selected['hour']!,
                    selected['minute']!,
                    selected['second']!)
                .millisecondsSinceEpoch;

            setState(() {
              if (label == '开始时间') {
                _startTime = timestamp;
                // 开始时间大于结束时间,把结束时间清空，重选
                if (_endTime != null && timestamp > (_endTime as int)) {
                  TDToast.showText('开始时间不能晚于结束时间', context: context);
                  _endTime = null;
                  return;
                }
              } else {
                _endTime = timestamp;
              }
            });
            if (_startTime != null && _endTime != null) {
              _loadData();
            }
            Navigator.of(context).pop();
          },
          useHour: true,
          useMinute: true,
          useSecond: true,
          dateStart: dateStart,
          dateEnd: dateEnd,
          initialDate: initialDate,
        );
      },
      arrow: false,
      title: label,
      style: TDCellStyle(
        padding: const EdgeInsets.symmetric(vertical: 6),
      ),
      noteWidget: Container(
        constraints: const BoxConstraints(
          maxWidth: 300,
        ),
        padding: const EdgeInsets.symmetric(vertical: 6, horizontal: 8),
        decoration: BoxDecoration(
            border:
                Border.all(color: TDTheme.of(context).grayColor2, width: 1)),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              TDIcons.calendar_1,
              color: TDTheme.of(context).grayColor4,
              size: 18,
            ),
            const SizedBox(width: 8),
            Expanded(
              child: TDText(
                timeStr ?? label,
                font: TDTheme.of(context).fontBodyLarge,
                textColor: timeStr != null
                    ? TDTheme.of(context).grayColor11
                    : TDTheme.of(context).grayColor6,
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ),
            const SizedBox(width: 8),
            Icon(
              TDIcons.chevron_down,
              color: TDTheme.of(context).grayColor6,
            )
          ],
        ),
      ),
    );
  }

  String _formatTime(int time) {
    return DateFormat('yyyy-MM-dd HH:mm:ss')
        .format(DateTime.fromMillisecondsSinceEpoch(time));
  }

  // 开始测量
  void _startMeasure(context) {
    showGeneralDialog(
      context: context,
      pageBuilder: (BuildContext buildContext, Animation<double> animation,
          Animation<double> secondaryAnimation) {
        return const MeasurementStartPanel();
      },
    );
  }

  // 同步数据
  void _syncData(context) {
    final wyDeviceBloc = BlocProvider.of<WyDeviceBloc>(context);

    String deviceId = wyDeviceBloc.state.deviceId;
    String ipAddress = wyDeviceBloc.state.ipAddress;
    getDatabasePath(deviceId).then((dbPath) {
      SyncServiceControl(
        msgType: SyncServiceControlMsgType.start,
        databasePath: dbPath,
        baseApi: "http://$ipAddress:9999",
        pollInterval: 1000,
      ).sendSignalToRust();
    });

    debugPrint("sync: 同步按钮已按下");

    //  展示同步数据进度弹窗
    showGeneralDialog(
      context: context,
      pageBuilder: (BuildContext buildContext, Animation<double> animation,
          Animation<double> secondaryAnimation) {
        return TDConfirmDialog(
          title: '同步数据',
          buttonText: '取消同步',
          action: () {
            const SyncServiceControl(
                    msgType: SyncServiceControlMsgType.stop,
                    databasePath: '',
                    baseApi: '',
                    pollInterval: 10)
                .sendSignalToRust();
            print('取消同步数据');
            Navigator.pop(buildContext);
          },
          contentWidget: Padding(
              padding: const EdgeInsets.only(top: 20),
              child: StreamBuilder(
                  stream: SyncProgress.rustSignalStream, // GENERATED
                  builder: (context, snapshot) {
                    final signalPack = snapshot.data;
                    if (signalPack == null) {
                      return TDProgress(
                        type: TDProgressType.linear,
                        value: 0,
                        strokeWidth: 10,
                        progressLabelPosition: TDProgressLabelPosition.right,
                        customProgressLabel: const Text("0%"),
                      );
                    }
                    debugPrint("同步进度:${signalPack.message.progressPercentage}");

                    final progressPercentage =
                        min(signalPack.message.progressPercentage, 100);
                    return TDProgress(
                      type: TDProgressType.linear,
                      value: progressPercentage / 100,
                      strokeWidth: 10,
                      progressLabelPosition: TDProgressLabelPosition.right,
                      customProgressLabel: Text("$progressPercentage%"),
                    );
                  })),
        );
      },
    );
  }

  // 导出数据
  void _exportData(context) {
    Navigator.of(context).push(TDSlidePopupRoute(
        modalBarrierColor: TDTheme.of(context).fontGyColor2,
        slideTransitionFrom: SlideTransitionFrom.bottom,
        builder: (buildContext) {
          return TDPopupCenterPanel(
            closeClick: () {
              Navigator.maybePop(buildContext);
            },
            child:
                MeasurementExport(context: context, buildContext: buildContext),
          );
        }));
  }
}

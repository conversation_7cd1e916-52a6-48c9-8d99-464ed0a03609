import 'package:flutter/material.dart';
import 'package:tdesign_flutter/tdesign_flutter.dart';

// 启动测量
void startMeasure(context) {
  showGeneralDialog(
    context: context,
    pageBuilder: (BuildContext buildContext, Animation<double> animation,
        Animation<double> secondaryAnimation) {
      return const MeasurementStartPanel();
    },
  );
}

// 停止测量
void stopMeasuring(context) {
  showGeneralDialog(
    context: context,
    pageBuilder: (BuildContext buildContext, Animation<double> animation,
        Animation<double> secondaryAnimation) {
      return TDAlertDialog(
        title: '停止测量',
        content: '确定要停止测量吗？',
        rightBtnAction: () {
          // TODO:停止测量
          Navigator.pop(buildContext);
        },
      );
    },
  );
}

class MeasurementStartPanel extends StatefulWidget {
  const MeasurementStartPanel({super.key});

  @override
  State<MeasurementStartPanel> createState() => _MeasurementStartPanelState();
}

class _MeasurementStartPanelState extends State<MeasurementStartPanel> {
  @override
  Widget build(BuildContext context) {
    return TDConfirmDialog(
      title: '测量设置',
      buttonText: '开始测量',
      action: () {
        // TODO: 开始测量
        print('开始测量');
        Navigator.pop(context);
      },
      contentMaxHeight: 240,
      showCloseButton: true,
      contentWidget: Column(
        children: [
          Padding(
            padding: const EdgeInsets.symmetric(vertical: 12),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const TDText('测量模式'),
                TDRadioGroup(
                  selectId: '0',
                  direction: Axis.horizontal,
                  directionalTdRadios: const [
                    TDRadio(
                      id: '0',
                      title: '同步测量',
                      showDivider: false,
                    ),
                    TDRadio(
                      id: '1',
                      title: '轮询测量',
                      showDivider: false,
                    ),
                  ],
                )
              ],
            ),
          ),
          const TDDivider(),
          Padding(
            padding: const EdgeInsets.symmetric(vertical: 12),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const TDText('调度模式'),
                TDRadioGroup(
                  selectId: '0',
                  direction: Axis.horizontal,
                  directionalTdRadios: const [
                    TDRadio(
                      id: '0',
                      title: '连续测量',
                      showDivider: false,
                    ),
                    TDRadio(
                      id: '1',
                      title: '定时测量',
                      showDivider: false,
                    ),
                  ],
                )
              ],
            ),
          )
        ],
      ),
    );
  }
}

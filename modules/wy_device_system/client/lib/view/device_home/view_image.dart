import 'package:client/bloc/targets_blocs.dart';
import 'package:client/model/wy_device.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:provider/provider.dart';
import 'package:tdesign_flutter/tdesign_flutter.dart';

import '../../bloc/wy_device_blocs.dart';
import '../components/roi_selector/multi_camera_roi_selector.dart';

class ViewImage extends StatefulWidget {
  final context;

  const ViewImage({
    super.key,
    required this.context,
  });

  @override
  State<ViewImage> createState() => _ViewImageState();
}

class _ViewImageState extends State<ViewImage> {
  String date = "";
  bool _showROIs = true;
  int _pwm = 2000;
  DateTime _dateTime = DateTime.now();

  @override
  void initState() {
    super.initState();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    print(widget.context);
    final _wyDeviceBloc = context.read<WyDeviceBloc>();

    return Scaffold(
        backgroundColor: Colors.white,
        appBar: AppBar(
          title: const Text('查看图像'),
        ),
        body: Column(
          children: [
            AnimatedContainer(
              duration: const Duration(milliseconds: 300),
              curve: Curves.easeInOut,
              height: 320, // 固定高度
              child: AnimatedOpacity(
                opacity: 1.0,
                duration: const Duration(milliseconds: 200),
                child: _buildSwitchableSection(context),
              ),
            ),
            const Center(child: TDDivider()),
            Center(
                child: Row(
              children: [
                const Text("显示ROI"),
                TDSwitch(
                  isOn: _showROIs,
                  onChanged: (value) {
                    setState(() {
                      _showROIs = !_showROIs;
                    });
                    return false;
                  },
                ),
              ],
            )),
            const Center(child: TDDivider()),
            Center(
                child: Row(
              children: [
                const Text("PW"),
                Expanded(
                  child: TDSlider(
                    sliderThemeData: TDSliderThemeData(
                      context: context,
                      showThumbValue: true,
                      scaleFormatter: (value) {
                        return value.toInt().toString();
                      },
                      min: 0,
                      max: 10000,
                    ),
                    value: (_wyDeviceBloc.state.deviceAttribute?.pwm ?? 2000)
                        .toDouble(),
                    rightLabel: '10000',
                    onChanged: (value) {
                      _pwm = value.toInt();
                    },
                  ),
                ),
                TextButton(
                  child: const Text("发送"),
                  onPressed: () {
                    _wyDeviceBloc.repository.saveDeviceAttribute("pwm", _pwm);
                  },
                ),
              ],
            )),
            const Center(child: TDDivider()),
            const Center(
                child: TDButton(
              text: '请求图像',
              size: TDButtonSize.large,
              type: TDButtonType.fill,
              shape: TDButtonShape.rectangle,
              theme: TDButtonTheme.primary,
            ))
          ],
        ));
  }

  Widget _buildSwitchableSection(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final _targetBloc = context.read<TargetBloc>();
    final _wyDeviceBloc = context.read<WyDeviceBloc>();

    // final viewModel = Provider.of<ROISelectorViewModel>(context);
    // final historyImages = viewModel.getCameraImageHistory(0);
    // debugPrint("历史图片数量: ${historyImages.length}");

    return Container(
      height: 320, // 固定合适的高度
      width: screenWidth, // 使用全屏宽度
      child: LayoutBuilder(
        builder: (context, constraints) {
          return Container(
            width: constraints.maxWidth,
            height: constraints.maxHeight,
            child: MultiCameraROISelector(
              compactMode: true,
              targetBloc: _targetBloc,
              wyDeviceBloc: _wyDeviceBloc,
              showTargetInfoCard: false,
              canvasWidth: constraints.maxWidth,
              canvasHeight: constraints.maxHeight,
              imagePath: 'assets/images/sample_image.jpg',
              showControlPanel: false,
              showHistoryPanel: true,
              showROIs: _showROIs,
              onROIsChanged: (roiData) {
                // Handle ROI changes
              },
            ),
          );
        },
      ),
    );
  }
}

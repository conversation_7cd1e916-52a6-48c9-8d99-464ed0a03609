import 'package:client/view/device_home/target_manage.dart';
import 'package:flutter/material.dart';
import 'package:tdesign_flutter/tdesign_flutter.dart';

import '../../bloc/targets_blocs.dart';
import '../../model/target.dart';

class TargetEditor extends StatefulWidget {
  final bool createMode;
  final Target target;
  final TargetBloc targetBloc;

  const TargetEditor(
      {super.key,
      required this.createMode,
      required this.target,
      required this.targetBloc});

  @override
  State<TargetEditor> createState() => _TargetEditorState();
}

class _TargetEditorState extends State<TargetEditor> {
  late TextEditingController nameController;
  late String targetModel;

  late TextEditingController distanceController;
  late TextEditingController heightController;

  late bool isBasePoint;
  late bool skipMeasurement;
  late bool isSelfAdaption;

  @override
  void initState() {
    super.initState();
    // 初始化数据
    nameController = TextEditingController(text: widget.target.name);
    targetModel = widget.target.targetModel;
    distanceController = TextEditingController(
      text: widget.target.actualMeasureDistance.toString() ?? '',
    );
    heightController = TextEditingController(
      text: widget.target.heightDiff?.toString() ?? '0',
    );
    isBasePoint = widget.target.basePoint;
    skipMeasurement = widget.target.skipMeasurement;
    isSelfAdaption = widget.target.selfAdaption ?? false;
  }

  @override
  void dispose() {
    nameController.dispose();
    distanceController.dispose();
    heightController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.only(
        bottom: MediaQuery.of(context).viewInsets.bottom,
      ),
      child: TDPopupCenterPanel(
        closeClick: () {
          Navigator.maybePop(context);
        },
        child: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const SizedBox(height: 24),
              Row(
                children: [
                  const SizedBox(width: 12),
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: Colors.blue.shade50,
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: const Icon(Icons.edit_rounded,
                        color: Colors.blue, size: 20),
                  ),
                  const SizedBox(width: 12),
                  Text(
                    widget.createMode ? '创建标靶' : '设置标靶',
                    style: const TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ],
              ),
              SizedBox(height: 16),
              TDInput(
                leftLabel: '标靶名称',
                required: true,
                controller: nameController,
              ),
              SizedBox(height: 16),
              _buildTargetModelPicker(),
              SizedBox(height: 16),
              TDInput(
                leftLabel: '测量距离(m)',
                required: true,
                controller: distanceController,
                inputType: const TextInputType.numberWithOptions(
                  decimal: true,
                ),
              ),
              SizedBox(height: 16),
              TDInput(
                leftLabel: '高度差(m)',
                required: true,
                controller: heightController,
                inputType: const TextInputType.numberWithOptions(
                  decimal: true,
                ),
              ),
              SizedBox(height: 16),
              _buildTargetBasePointSwitch(),
              SizedBox(height: 16),
              _buildTargetMeasurementSwitch(),
              SizedBox(height: 16),
              _buildTargetSelfAdaptionSwitch(),
              SizedBox(height: 24),
              Row(
                children: [
                  Expanded(
                    child: TDButton(
                      text: '取消',
                      type: TDButtonType.outline,
                      onTap: () {
                        Navigator.maybePop(context);
                      },
                    ),
                  ),
                  SizedBox(width: 16),
                  Expanded(
                    child: TDButton(
                      text: widget.createMode ? '创建' : '保存',
                      type: TDButtonType.fill,
                      onTap: () {
                        _handleSave();
                      },
                    ),
                  ),
                ],
              ),
              SizedBox(height: 16), // 底部额外间距
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildTargetModelPicker() {
    return GestureDetector(
      onTap: () {
        _pickTargetModel(context);
      },
      child: Row(
        children: [
          SizedBox(width: 16),
          TDText(
            '标靶型号',
          ),
          TDText(
            ' *',
            textColor: TDTheme.of(context).errorColor6, // 红色星号
          ),
          SizedBox(width: 24),
          Text(
            targetModel.isEmpty ? '请选择标靶型号' : targetModel,
          ),
          Icon(
            Icons.arrow_forward_ios,
          ),
        ],
      ),
    );
  }

  Widget _buildTargetBasePointSwitch() {
    return GestureDetector(
      onTap: () {
        _pickTargetModel(context);
      },
      child: Row(
        children: [
          SizedBox(width: 16),
          SizedBox(
            child: TDText(
              '基准点',
            ),
            width: 96,
          ),
          TDSwitch(
            isOn: isBasePoint,
            onChanged: (newValue) {
              isBasePoint = newValue;
              return false;
            },
          )
        ],
      ),
    );
  }

  Widget _buildTargetMeasurementSwitch() {
    return GestureDetector(
      onTap: () {
        _pickTargetModel(context);
      },
      child: Row(
        children: [
          SizedBox(width: 16),
          SizedBox(
            child: TDText(
              '测量',
            ),
            width: 96,
          ),
          TDSwitch(
            isOn: !skipMeasurement,
            onChanged: (newValue) {
              skipMeasurement = !newValue;
              return false;
            },
          )
        ],
      ),
    );
  }

  Widget _buildTargetSelfAdaptionSwitch() {
    return GestureDetector(
      onTap: () {
        _pickTargetModel(context);
      },
      child: Row(
        children: [
          SizedBox(width: 16),
          SizedBox(
            child: TDText(
              '自适应标靶',
            ),
            width: 96,
          ),
          TDSwitch(
            isOn: isSelfAdaption,
            onChanged: (newValue) {
              isSelfAdaption = newValue;
              return false;
            },
          )
        ],
      ),
    );
  }

  void _pickTargetModel(BuildContext context) {
    final targetModels = ['T200', 'T100', 'T50', 'T20'];
    // 找到当前选中项的索引
    int initialIndex =
        targetModel.isEmpty ? 0 : targetModels.indexOf(targetModel);
    if (initialIndex == -1) initialIndex = 0;

    TDPicker.showMultiPicker(
      context,
      title: '选择标靶型号',
      onConfirm: (selected) {
        if (selected.isNotEmpty) {
          setState(() {
            targetModel = targetModels[selected[0]];
          });
        }
        Navigator.maybePop(context);
      },
      data: [targetModels],
      initialIndexes: [initialIndex],
    );
  }

  void _handleSave() {
    // 验证输入
    if (nameController.text.trim().isEmpty) {
      // 显示错误提示
      TDMessage.showMessage(
        context: context,
        content: "标靶名称不能为空",
        visible: true,
        icon: true,
        theme: MessageTheme.info,
        duration: 3000,
      );
      return;
    }

    final heightDiff = double.tryParse(heightController.text.trim());
    if (heightDiff == null) {
      // 显示错误提示
      TDMessage.showMessage(
        context: context,
        content: "高度差无效",
        visible: true,
        icon: true,
        theme: MessageTheme.info,
        duration: 3000,
      );
      return;
    }

    final distance = double.tryParse(distanceController.text.trim());
    if (distance == null) {
      // 显示错误提示
      TDMessage.showMessage(
        context: context,
        content: "测量距离无效",
        visible: true,
        icon: true,
        theme: MessageTheme.info,
        duration: 3000,
      );
      return;
    }

    final target = widget.target.copyWith(
      name: nameController.text,
      targetModel: targetModel,
      actualMeasureDistance: distance,
      heightDiff: heightDiff,
      basePoint: isBasePoint,
      skipMeasurement: skipMeasurement,
      selfAdaption: isSelfAdaption,
    );

    insertTarget(widget.targetBloc, target);

    Navigator.maybePop(context);
  }
}

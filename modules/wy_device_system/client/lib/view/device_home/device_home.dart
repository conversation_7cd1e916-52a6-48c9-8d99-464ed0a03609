import 'package:client/model/wy_device.dart';
import 'package:flutter/material.dart';
import 'package:tdesign_flutter/tdesign_flutter.dart';

class DeviceHome extends StatefulWidget {
  final context;
  const DeviceHome({
    super.key,
    required this.context,
  });

  @override
  State<DeviceHome> createState() => _DeviceHomeState();
}

class _DeviceHomeState extends State<DeviceHome> {
  late WyDeviceStatus deviceStatus;

  @override
  void initState() {
    super.initState();
    deviceStatus = WyDeviceStatus.idle;
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    print(widget.context);
    return Scaffold(
        backgroundColor: Colors.white,
        appBar: AppBar(
          title: const Text('视觉位移计客户端'),
        ),
        body: const Column(
          children: [],
        ));
  }
}

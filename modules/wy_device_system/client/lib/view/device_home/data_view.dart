import 'dart:math';

import 'package:client/view/device_home/device_status_view_model.dart';
import 'package:flutter/material.dart';
import 'package:fl_chart/fl_chart.dart';
import 'package:intl/intl.dart';
import 'package:tdesign_flutter/tdesign_flutter.dart';

class DataView extends StatefulWidget {
  final List<DataPoint> dataPoints;
  final isShowTable;
  const DataView(
      {super.key, required this.dataPoints, required this.isShowTable});

  @override
  State<DataView> createState() => _DataViewState();
}

class _DataViewState extends State<DataView> {
  late double minX;
  late double maxX;
  late double minY;
  late double maxY;
  late List<double> yAxisValues;
  late List<double> xAxisValues;
  @override
  void initState() {
    super.initState();
    _calculateBounds();
  }

  void _calculateBounds() {
    if (widget.dataPoints.isEmpty) {
      DateTime now = DateTime.now();
      DateTime now1 = now.subtract(const Duration(minutes: 1));
      minX = 0;
      maxX = 1;
      minY = 0;
      maxY = 1;
      yAxisValues = [0.0, 0.25, 0.5, 0.75, 1.0];
      xAxisValues = [
        now1.millisecondsSinceEpoch as double,
        now.millisecondsSinceEpoch as double
      ];
      return;
    }

    // 计算X轴范围（使用所有时间戳）
    final timestamps = widget.dataPoints.map((p) => p.timestamp).toList();
    minX = timestamps.reduce(min);
    maxX = timestamps.reduce(max);
    // 计算Y轴范围（同时考虑两组数据）
    final sigmaXValues = widget.dataPoints.map((p) => p.sigmaX).toList();
    final sigmaYValues = widget.dataPoints.map((p) => p.sigmaY).toList();

    final minSigmaX = sigmaXValues.reduce(min);
    final maxSigmaX = sigmaXValues.reduce(max);
    final minSigmaY = sigmaYValues.reduce(min);
    final maxSigmaY = sigmaYValues.reduce(max);

    final dataMinY = min(minSigmaX, minSigmaY);
    final dataMaxY = max(maxSigmaX, maxSigmaY);

    yAxisValues = _calculateYAxisValues(dataMinY, dataMaxY);
    // 设置Y轴范围
    minY = yAxisValues.first;
    maxY = yAxisValues.last;
    // 计算X轴标签位置（显示4个点）
    xAxisValues = _generateAxisValues(minX, maxX, 4);
  }

  // 时间戳转换为日期字符串
  String _formatTimestamp(double timestamp) {
    final now = DateTime.now();
    final date = DateTime.fromMillisecondsSinceEpoch(timestamp.toInt());
    final diff = now.difference(date).abs();

    if (diff.inDays > 365) {
      return DateFormat('yyyy-MM-dd').format(date);
    } else {
      return DateFormat('MM-dd HH:mm').format(date);
    }
  }

  // 生成指定数量的等间距值
  List<double> _generateAxisValues(double min, double max, int count) {
    if (count < 2) return [min, max];

    final step = (max - min) / (count - 1);
    return List.generate(count, (i) => min + step * i);
  }

  List<double> _calculateYAxisValues(double dataMin, double dataMax) {
    // 计算数据范围
    double range = dataMax - dataMin;

    // 确定步长基准（5的倍数）
    double stepBase = 5.0;

    // 如果范围很大，增加步长基准
    if (range > 50) stepBase = 10.0;
    if (range > 100) stepBase = 20.0;
    if (range > 200) stepBase = 50.0;

    // 计算最小边界（向下取整到stepBase的倍数）
    double minBound = (dataMin / stepBase).floor() * stepBase;
    // 计算最大边界（向上取整到stepBase的倍数）
    double maxBound = (dataMax / stepBase).ceil() * stepBase;

    // 扩展边界以确保覆盖数据范围
    if (minBound > dataMin) minBound -= stepBase;
    if (maxBound < dataMax) maxBound += stepBase;

    // 调整边界确保有足够的范围
    if (maxBound - minBound < range * 1.2) {
      minBound -= stepBase;
      maxBound += stepBase;
    }

    // 计算实际步长（确保5个点）
    double step = (maxBound - minBound) / 4;

    // 调整步长为5的倍数
    step = (step / stepBase).ceil() * stepBase;

    // 计算5个刻度点
    List<double> values = [];
    for (int i = 0; i < 5; i++) {
      double value = minBound + i * step;
      // 确保所有点都是5的倍数
      value = (value / stepBase).round() * stepBase;
      values.add(value);
    }

    // 确保最大值覆盖数据范围
    if (values.last < dataMax) {
      values[4] = values[4] + stepBase;
    }

    // 确保最小值覆盖数据范围
    if (values.first > dataMin) {
      values[0] = values[0] - stepBase;
    }

    // 返回排序后的值（从小到大）
    values.sort();
    return values;
  }

  @override
  Widget build(BuildContext context) {
    if (widget.isShowTable) {
      return TDTable(
        height: 300,
        columns: [
          TDTableCol(title: '时间', colKey: 'timestamp'),
          TDTableCol(title: 'sigmaX', colKey: 'sigmaX'),
          TDTableCol(title: 'sigmaY', colKey: 'sigmaY'),
        ],
        data: widget.dataPoints,
      );
    }
    final sigmaXSpots = widget.dataPoints
        .map((point) => FlSpot(point.timestamp, point.sigmaX))
        .toList();
    final sigmaYSpots = widget.dataPoints
        .map((point) => FlSpot(point.timestamp, point.sigmaY))
        .toList();
    return LineChart(
      LineChartData(
        gridData: FlGridData(
          drawVerticalLine: false,
          drawHorizontalLine: true,
          getDrawingHorizontalLine: (value) {
            return FlLine(
              color: Colors.grey.withOpacity(0.2),
              strokeWidth: 1,
            );
          },
        ),
        borderData: FlBorderData(
          show: true,
          border: const Border(
            left: BorderSide(color: Colors.black, width: 1),
            bottom: BorderSide(color: Colors.black, width: 1),
          ),
        ),
        titlesData: FlTitlesData(
          show: true,
          leftTitles: AxisTitles(
            sideTitles: SideTitles(
              showTitles: true,
              reservedSize: 30,
              getTitlesWidget: (value, meta) {
                if (yAxisValues.any((v) => (v - value).abs() < 0.0001)) {
                  return Padding(
                    padding: const EdgeInsets.only(right: 8.0),
                    child: Text(
                      value.toStringAsFixed(0),
                      style: const TextStyle(fontSize: 12),
                      textAlign: TextAlign.center,
                    ),
                  );
                }
                return const SizedBox.shrink();
              },
            ),
          ),
          bottomTitles: AxisTitles(
            sideTitles: SideTitles(
              showTitles: true,
              reservedSize: 30,
              getTitlesWidget: (value, meta) {
                for (double x in xAxisValues) {
                  if ((x - value).abs() < (maxX - minX) * 0.01) {
                    return Transform.translate(
                      offset: const Offset(-6, 0),
                      child: Padding(
                        padding: const EdgeInsets.only(top: 16.0),
                        child: Text(
                          _formatTimestamp(value),
                          style: const TextStyle(fontSize: 12),
                          textAlign: TextAlign.center,
                        ),
                      ),
                    );
                  }
                }
                return const SizedBox.shrink();
              },
            ),
          ),
          topTitles:
              const AxisTitles(sideTitles: SideTitles(showTitles: false)),
          rightTitles:
              const AxisTitles(sideTitles: SideTitles(showTitles: false)),
        ),
        lineBarsData: [
          // SigmaX 线条
          LineChartBarData(
            spots: sigmaXSpots,
            isCurved: true,
            color: Colors.blue,
            barWidth: 2,
            dotData: const FlDotData(show: false),
          ),
          // SigmaY 线条
          LineChartBarData(
            spots: sigmaYSpots,
            isCurved: true,
            color: Colors.orange,
            barWidth: 2,
            dotData: const FlDotData(show: false),
          ),
        ],
        minX: minX,
        maxX: maxX,
        minY: minY,
        maxY: maxY,
        lineTouchData: const LineTouchData(enabled: false),
      ),
    );
  }
}

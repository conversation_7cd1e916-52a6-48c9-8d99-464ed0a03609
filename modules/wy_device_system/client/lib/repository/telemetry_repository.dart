import 'package:flutter/cupertino.dart';
import 'package:sqflite/sqflite.dart';
import 'dart:async';
import 'package:path/path.dart';

import '../common/utils.dart';

class TelemetryRepository {
  // 存储不同设备ID对应的数据库实例
  static final Map<String, Database> _deviceDatabases = {};

  // 获取特定设备的数据库实例
  Future<Database> _getDeviceDatabase(String deviceId) async {
    if (_deviceDatabases.containsKey(deviceId)) {
      return _deviceDatabases[deviceId]!;
    }

    final dbPath = await getDatabasePath(deviceId);
    final path = join(dbPath, 'telemetry.db');

    debugPrint("数据库路径: $path");

    final db = await openDatabase(
      path,
      version: 1,
      onCreate: (Database db, int version) async {

        db.execute('''
                create table displacement
                    (
                        target_id varchar not null,
                        ts        bigint  not null,
                        sigma_x   real    not null,
                        sigma_y   real    not null,
                        x         real    not null,
                        y         real    not null,
                        r         real    not null,
                        filtered  boolean not null,
                        inserted  boolean not null,
                        constraint pk_displacement
                            primary key (target_id, ts)
                    );
        ''');
      },
    );

    _deviceDatabases[deviceId] = db;
    return db;
  }

  Future<List<Map<String, dynamic>>> queryDisplacement(
      String deviceId, {String? targetId, int? startTs, int? endTs}) async {
    final db = await _getDeviceDatabase(deviceId);

    String query = "SELECT target_id,ts,sigma_x,sigma_y FROM displacement WHERE 1=1";
    List<dynamic> arguments = [];

    if (targetId != null) {
      query += " AND target_id = ?";
      arguments.add(targetId);
    }

    if (startTs != null) {
      query += " AND ts >= ?";
      arguments.add(startTs);
    }

    if (endTs != null) {
      query += " AND ts <= ?";
      arguments.add(endTs);
    }

    query += " ORDER BY ts DESC limit 50000";

    List<Map<String, dynamic>> result = await db.rawQuery(query, arguments);

    List<Map<String, dynamic>> modifiableResult = [];
    for (var row in result) {
      Map<String, dynamic> newRow = Map<String, dynamic>.from(row);
      newRow['timestamp'] = DateTime.fromMillisecondsSinceEpoch(row['ts']);
      modifiableResult.add(newRow);
    }
    result = modifiableResult;

    debugPrint("查询结果: ${result.length}");

    return result;
  }



  // 关闭特定设备的数据库连接
  Future<void> closeDeviceDatabase(String deviceId) async {
    if (_deviceDatabases.containsKey(deviceId)) {
      await _deviceDatabases[deviceId]!.close();
      _deviceDatabases.remove(deviceId);
    }
  }

  // 关闭所有数据库连接
  Future<void> closeAllDatabases() async {
    for (final db in _deviceDatabases.values) {
      await db.close();
    }
    _deviceDatabases.clear();
  }
}
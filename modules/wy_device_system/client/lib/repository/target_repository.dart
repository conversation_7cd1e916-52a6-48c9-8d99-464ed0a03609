import 'dart:convert';
import 'dart:io';

import 'package:client/bloc/wy_device_blocs.dart';
import 'package:dio/dio.dart';
import 'package:flutter/cupertino.dart';
import 'package:path/path.dart';
import 'package:path_provider/path_provider.dart';
import 'package:utils/utils.dart';

import '../common/utils.dart';
import '../model/roi.dart';
import '../model/target.dart';
import '../view/components/roi_selector/roi_scale_converter.dart';
import 'package:sqflite/sqflite.dart';

abstract class TargetRepository {
  Future<List<Target>> loadTargets();
  Future<void> saveTarget(Target target);
  Future<void> deleteTarget(String id);
  Future<void> updateTarget(Target target);
  Future<void> setRois(Target target);
  Future<Map<String, dynamic>> initializeTarget(Map<String, dynamic> rpc);
  Future<Map<String, dynamic>> wyTestTarget(Map<String, dynamic> rpc);
}

class TargetRepositoryImpl implements TargetRepository {
  final WyDeviceBloc wyDeviceBloc;

  TargetRepositoryImpl({
    required this.wyDeviceBloc,
  });

  @override
  Future<void> deleteTarget(String id) async {
    var result = await HttpUtil.instance.client.delete('/targets/$id');

    // 从本地数据库移除target
    try {
      final deviceId = wyDeviceBloc.state.deviceId;
      final db = await _getDeviceDatabase(deviceId);
      await db.delete(
        'target',
        where: 'target_id = ?',
        whereArgs: [id],
      );
      debugPrint("已从本地数据库删除target: $id");
    } catch (e) {
      debugPrint("从数据库删除target失败: $e");
      // 即使数据库操作失败，也不抛出异常，因为HTTP删除已经成功
    }
  }

  // 获取特定设备的数据库实例
  Future<Database> _getDeviceDatabase(String deviceId) async {
    final dbPath = await getDatabasePath(deviceId);
    final path = join(dbPath, 'target.db');

    debugPrint("数据库路径: $path");

    final db = await openDatabase(
      path,
      version: 1,
      onCreate: (Database db, int version) async {
        // 创建标靶表
        await db.execute('''
          CREATE TABLE IF NOT EXISTS target
          (
              target_id               varchar                  not null
                  primary key,
              camera_id               INTEGER                  not null,
              name                    varchar                  not null,
              roi                     varchar                  not null,
              target_model            varchar                  not null,
              actual_measure_distance float                    not null,
              pwm                     integer default 0        not null,
              ref_point               json_text,
              base_point              boolean default FALSE    not null,
              skip_measurement        boolean default FALSE    not null,
              camera_exposure         varchar default 'Middle' not null,
              brightness_thr          integer default 225      not null,
              init_ts                 bigint  default 0        not null,
              created_time            bigint                   not null,
              last_update_ts          bigint                   not null,
              self_adaption           boolean default false,
              normal_angle            float,
              height_diff             float
          );
        ''');

      },
    );
    return db;
  }


  @override
  Future<List<Target>> loadTargets() async {
    final deviceId = wyDeviceBloc.state.deviceId;

    debugPrint("当前加载的标靶集所属设备Id: $deviceId");
    try {
      var result = await HttpUtil.instance.client.get('/targets');

      debugPrint("loadTargets response => ${jsonEncode(result.data)}");

      List<Target> targetList = [];
      if (result.data != null) {
        var targets = result.data['data']['targets'];
        if (targets != null && targets is List) {
          for (var target_json in targets) {
            var target = Target.fromJson(target_json);
            targetList.add(target);
          }
        }
      }
      _saveTargetsCache(targetList);
      return Future.value(targetList);
    } catch (e) {
      final cache = await _getTargetsCache();
      if (cache != null) {
        return cache;
      }

      String errorMessage = e is DioException
          ? (e.response?.data != null
              ? (e.response?.data['msg'] ?? e.response?.data.toString())
              : e.message ?? e.toString())
          : e.toString();
      throw Exception(errorMessage);
    }
  }

  Future<void> _saveTargetsCache(List<Target> targetList) async {
    try {
      final deviceId = wyDeviceBloc.state.deviceId;
      final db = await _getDeviceDatabase(deviceId);
      await db.transaction((txn) async {
        await txn.delete("target");
        for (final target in targetList) {
          // roi序列化算法是从其他文件抄来的 modules/wy_device_system/client/lib/model/target.dart:convertToJson
          var originalRect = ScaleConverter.scaleRectToOriginal(target.rect,
              Size(640, 480), ScaleConverter.defaultDeviceResolution);
          await txn.insert(
            "target",
            {
              // 奇怪的硬编码数据是从 Target.convertToJson 抄来的
              'target_id': target.targetId,
              'camera_id': target.cameraId,
              'name': target.name,
              'roi': jsonEncode({
                "x": (originalRect.left).toInt(),
                "y": (originalRect.top).toInt(),
                "width": (originalRect.width).toInt(),
                "height": (originalRect.height).toInt(),
                "factorX": 1,
                "factorY": 1,
              }),
              'target_model': target.targetModel,
              'actual_measure_distance': target.actualMeasureDistance,
              'pwm': 5000,
              'ref_point': jsonEncode(target.refPoint),
              'base_point': target.basePoint,
              'skip_measurement': target.skipMeasurement,
              'camera_exposure': target.wyCameraExposure,
              'brightness_thr': 225,
              'init_ts': target.initTs,
              'created_time': 0,
              'last_update_ts': DateTime.now().millisecondsSinceEpoch,
              'self_adaption': target.selfAdaption,
            },
            conflictAlgorithm: ConflictAlgorithm.replace,
          );
        }
      });
    } catch (e) {
      debugPrint(e.toString());
    }
  }

  Future<List<Target>?> _getTargetsCache() async {
    try {
      final deviceId = wyDeviceBloc.state.deviceId;
      final db = await _getDeviceDatabase(deviceId);
      final targetList = (await db.query("target"))
          .map((row) {
        // roi转react的算法是从其他文件抄来的 modules/wy_device_system/client/lib/model/target.dart:fromJson
        Map<String, dynamic>? roiData =
            jsonDecode(row['roi'] as String) as Map<String, dynamic>?;
        var ratioX = 3840 / 640;
        var ratioY = 2160 / 480;
        if (roiData == null) {
          // 设置为640x480屏幕中间的150x150区域
          // 中心点：(320, 240)，左上角：(320-75=245, 240-75=165)
          roiData = {
            'x': 245, // 中心点x(320) - 宽度(150)/2 = 245
            'y': 165, // 中心点y(240) - 高度(150)/2 = 165
            'width': 150,
            'height': 150,
            'factorX': 1,
            'factorY': 1,
          };
        } else {
          // 将高分辨率坐标转换为低分辨率显示坐标
          roiData['x'] = (roiData['x'] / ratioX).toInt();
          roiData['y'] = (roiData['y'] / ratioY).toInt();
          roiData['width'] = (roiData['width'] / ratioX).toInt();
          roiData['height'] = (roiData['height'] / ratioY).toInt();
        }

        return Target(
          targetId: row['target_id'] as String,
          name: row['name'] as String,
          cameraId: row['camera_id'] as int,
          targetModel: row['target_model'] as String,
          actualMeasureDistance: row['actual_measure_distance'] as double,
          heightDiff: row['height_diff'] as double?,
          // sqlite 没有原生 bool 类型，用 int 存 bool
          skipMeasurement: row['skip_measurement'] != 0,
          basePoint: row['base_point'] != 0,
          selfAdaption: row['self_adaption'] != 0,
          wyCameraExposure: row['camera_exposure'] as String,
          initTs: row['init_ts'] as int,
          roi: ROI.fromJson(roiData),
          rect: Rect.fromLTWH(
            roiData['x'].toDouble(),
            roiData['y'].toDouble(),
            roiData['width'].toDouble(),
            roiData['height'].toDouble(),
          ),
        );
      }).toList();
      return targetList;
    } catch (e) {
      debugPrint("无法找到缓存数据:$e");
    }

    return null;
  }

  @override
  Future<void> saveTarget(Target target) async {
    try {
      var response = await HttpUtil.instance.client
          .post('/targets', data: target.convertToJson());
      // todo: 将target插入到本地数据库
      return response.data;
    } catch (e) {
      String errorMessage = e is DioException
          ? (e.response?.data != null
              ? (e.response?.data['msg'] ?? e.response?.data.toString())
              : e.message ?? e.toString())
          : e.toString();
      throw Exception(errorMessage);
    }
  }

  @override
  Future<Map<String, dynamic>> updateTarget(Target target) async {
    try {
      var response = await HttpUtil.instance.client
          .post('/targets', data: target.convertToJson());
      // todo:更新本地数据库中的target
      return response.data;
    } catch (e) {
      String errorMessage = e is DioException
          ? (e.response?.data != null
              ? (e.response?.data['msg'] ?? e.response?.data.toString())
              : e.message ?? e.toString())
          : e.toString();
      throw Exception("$errorMessage");
    }
  }

  Future<Map<String, dynamic>> initializeTarget(
      Map<String, dynamic> rpc) async {
    try {
      var response = await HttpUtil.instance.client.post('/rpc', data: rpc);
      return response.data;
    } catch (e) {
      String errorMessage = e is DioException
          ? (e.response?.data != null
              ? (e.response?.data['msg'] ?? e.response?.data.toString())
              : e.message ?? e.toString())
          : e.toString();
      throw Exception("$errorMessage");
    }
  }

  @override
  Future<Map<String, dynamic>> wyTestTarget(Map<String, dynamic> rpc) async {
    try {
      var response = await HttpUtil.instance.client.post('/rpc', data: rpc);
      return response.data;
    } catch (e) {
      String errorMessage = e is DioException
          ? (e.response?.data != null
              ? (e.response?.data['msg'] ?? e.response?.data.toString())
              : e.message ?? e.toString())
          : e.toString();
      throw Exception("$errorMessage");
    }
  }

  @override
  Future<void> setRois(Target target) async {
    try {
      ROI oriRoi = ScaleConverter.scaleRectToOriginalROI(
          target.rect, Size(640, 480), ScaleConverter.defaultDeviceResolution);

      var roi = {
        "cameraId": "1",
        // "cameraId": target.cameraId,
        "targetId": target.targetId,
        "x": oriRoi.x,
        "y": oriRoi.y,
        "width": oriRoi.width,
        "height": oriRoi.height,
        "factorX": oriRoi.factorX,
        "factorY": oriRoi.factorY,
      };

      Map<String, dynamic> params = {
        "cameraId": target.cameraId,
        "rois": [roi],
      };
      Map<String, dynamic> rpc = {
        // 这里需要根据实际需求设置 rpc 数据
        'method': "wySetROIs",
        'params': params,
      };
      debugPrint("setRois rpc => ${rpc}");

      var response = await HttpUtil.instance.client.post('/rpc', data: rpc);
      debugPrint("setRois response => ${response.data}");
      return response.data;
    } catch (e) {
      debugPrint("setRois error => ${e}");
      String errorMessage = e is DioException
          ? (e.response?.data != null
              ? (e.response?.data['msg'] ?? e.response?.data.toString())
              : e.message ?? e.toString())
          : e.toString();
      debugPrint("setRois errorMessage => ${errorMessage}");
      throw Exception("$errorMessage");
    }
  }
}

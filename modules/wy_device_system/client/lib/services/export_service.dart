import 'package:file_picker/file_picker.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:inteagle_monitoring_robot_app/src/bindings/bindings.dart';
import 'package:path/path.dart';

import '../../common/utils.dart';
import '../repository/measurement_repository.dart';

/// 导出结果类
class ExportResult {
  final bool success;
  final String message;
  final String? filePath;

  ExportResult({
    required this.success,
    required this.message,
    this.filePath,
  });
}

/// 导出服务类
/// 负责与Rust后端通信，处理数据导出请求
class ExportService {
  static const platform = MethodChannel('com.example.app/export');

  /// 导出数据
  ///
  /// [request] 包含导出参数的Map
  Future<ExportResult> exportData(Map<String, dynamic> request) async {
    try {
      final deviceId = request['device_id'];
      DateTime currentDate = DateTime.now();

      final fileName =
          "$deviceId-${currentDate.year}-${currentDate.month}-${currentDate.day}";
      String fileFullName = "$fileName.csv";
      ExportFormat format = ExportFormat.csv;
      if (request['format'] == "xlsx") {
        fileFullName = "$fileName.xlsx";
        format = ExportFormat.xlsx;
      }

      final selectedDirectory = await FilePicker.platform.getDirectoryPath(
        initialDirectory: "/storage/emulated/0/Documents",
      );
      if (selectedDirectory == null) {
        debugPrint("未能导出数据: 用户取消导出");
        return ExportResult(
          success: false,
          message: "用户取消导出",
        );
      }
      final filePath = join(selectedDirectory, fileFullName);

      debugPrint("正在导出设备 ${deviceId} 的数据");

      // 通过信号将请求发送给Rust后端
      final dbPath = join(await getDatabasePath(deviceId), 'telemetry.db');

      final exportRequest = ExportRequest(
        format: format,
        databasePath: dbPath,
        outputFilePath: filePath,
      );
      ExportCommandStart(value: exportRequest).sendSignalToRust();

      MeasurementRepository measurementRepository = MeasurementRepository();
      measurementRepository.fetchMeasurements(deviceId);

      // 解析结果
      return ExportResult(
        success: true,
        message: "success",
        filePath: filePath,
      );
    } on PlatformException catch (e) {
      return ExportResult(
        success: false,
        message: '导出失败: ${e.message}',
      );
    } catch (e) {
      return ExportResult(
        success: false,
        message: '导出失败: $e',
      );
    }
  }
}

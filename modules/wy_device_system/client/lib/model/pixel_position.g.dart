// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'pixel_position.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

PixelPosition3Mark _$PixelPosition3MarkFromJson(Map<String, dynamic> json) =>
    $checkedCreate(
      'PixelPosition3Mark',
      json,
      ($checkedConvert) {
        final val = PixelPosition3Mark(
          x1: $checkedConvert('x1', (v) => (v as num).toDouble()),
          y1: $checkedConvert('y1', (v) => (v as num).toDouble()),
          r1: $checkedConvert('r1', (v) => (v as num).toDouble()),
          x2: $checkedConvert('x2', (v) => (v as num).toDouble()),
          y2: $checkedConvert('y2', (v) => (v as num).toDouble()),
          r2: $checkedConvert('r2', (v) => (v as num).toDouble()),
          x3: $checkedConvert('x3', (v) => (v as num).toDouble()),
          y3: $checkedConvert('y3', (v) => (v as num).toDouble()),
          r3: $checkedConvert('r3', (v) => (v as num).toDouble()),
        );
        return val;
      },
    );

Map<String, dynamic> _$PixelPosition3MarkToJson(PixelPosition3Mark instance) =>
    <String, dynamic>{
      'x1': instance.x1,
      'y1': instance.y1,
      'r1': instance.r1,
      'x2': instance.x2,
      'y2': instance.y2,
      'r2': instance.r2,
      'x3': instance.x3,
      'y3': instance.y3,
      'r3': instance.r3,
    };

// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'factory_calibration.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

FactoryCalibration _$FactoryCalibrationFromJson(Map<String, dynamic> json) =>
    $checkedCreate(
      'FactoryCalibration',
      json,
      ($checkedConvert) {
        final val = FactoryCalibration(
          cameraId: $checkedConvert('cameraId', (v) => (v as num).toInt()),
          isCalibrated: $checkedConvert('isCalibrated', (v) => v as bool),
          ts: $checkedConvert('ts', (v) => (v as num).toInt()),
          targetModel: $checkedConvert('targetModel', (v) => v as String),
          actualMeasureDistance: $checkedConvert(
              'actualMeasureDistance', (v) => (v as num).toDouble()),
          roi: $checkedConvert(
              'roi', (v) => ROI.fromJson(v as Map<String, dynamic>)),
          pixelPosition: $checkedConvert(
              'pixelPosition',
              (v) => v == null
                  ? null
                  : PixelPosition3Mark.fromJson(v as Map<String, dynamic>)),
          focalLength:
              $checkedConvert('focalLength', (v) => (v as num?)?.toInt() ?? 0),
        );
        return val;
      },
    );

Map<String, dynamic> _$FactoryCalibrationToJson(FactoryCalibration instance) =>
    <String, dynamic>{
      'cameraId': instance.cameraId,
      'isCalibrated': instance.isCalibrated,
      'ts': instance.ts,
      'targetModel': instance.targetModel,
      'actualMeasureDistance': instance.actualMeasureDistance,
      'roi': instance.roi.toJson(),
      if (instance.pixelPosition?.toJson() case final value?)
        'pixelPosition': value,
      'focalLength': instance.focalLength,
    };

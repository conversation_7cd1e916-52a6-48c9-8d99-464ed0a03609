import 'dart:ui';

import 'package:client/model/pixel_position.dart';
import 'package:flutter/cupertino.dart';
import '../view/components/roi_selector/roi_scale_converter.dart';
import 'roi.dart';

class Target {

  final String targetId;
  final int cameraId;
  final String name;
  final Rect rect;
  final bool basePoint;
  final bool skipMeasurement;
  final double? heightDiff;
  final double? actualMeasureDistance;
  final PixelPosition? refPoint;
  final String targetModel;
  final String wyCameraExposure;
  final bool? selfAdaption;
  final int initTs;
  final ROI roi ;


  Target({
    required this.targetId,
    required this.name,
    required this.rect,
    this.cameraId = 0,
    this.basePoint = false,
    this.skipMeasurement = false,
    this.heightDiff,
    this.actualMeasureDistance,
    this.refPoint,
    this.targetModel = '',
    this.wyCameraExposure = 'Middle',
    this.selfAdaption=false,
    this.initTs = 0,
    this.roi = const ROI(x: 0, y: 0, width: 0, height: 0, factorX: 0, factorY: 0)
  });

  Target copyWith({
    String? name,
    Rect? rect,
    bool? basePoint,
    bool? skipMeasurement,
    double? heightDiff,
    double? actualMeasureDistance,
    PixelPosition? refPoint,
    targetModel,
    selfAdaption,
    cameraId,
    int? initTs,
  }) {
    return Target(
      targetId: targetId,
      name: name ?? this.name,
      rect: rect ?? this.rect,
      basePoint: basePoint ?? this.basePoint,
      skipMeasurement: skipMeasurement ?? this.skipMeasurement,
      heightDiff: heightDiff ?? this.heightDiff,
      actualMeasureDistance: actualMeasureDistance ?? this.actualMeasureDistance,
      refPoint: refPoint ?? this.refPoint,
      targetModel: targetModel ?? this.targetModel,
      selfAdaption: selfAdaption ?? this.selfAdaption,
      cameraId: cameraId ?? this.cameraId,
      initTs: initTs ?? this.initTs,
    );
  }

// 在 Target 类中添加
  Map<String, dynamic> convertToJson() {
    var originalRect = ScaleConverter.scaleRectToOriginal(rect,Size(640, 480),ScaleConverter.defaultDeviceResolution);

    return {
      'targetId': targetId,
      'name': name,
      'cameraId': cameraId,
      'targetModel': targetModel,
      'actualMeasureDistance': actualMeasureDistance,
      'heightDiff': heightDiff,
      'skipMeasurement': skipMeasurement,
      'basePoint': basePoint,
      'selfAdaption': selfAdaption,
      'wyCameraExposure': wyCameraExposure,
      'wyBrightnessThr': 225,
      "pwm": 5000,
      "roi": {
        "x": (originalRect.left).toInt(),
        "y": (originalRect.top).toInt(),
        "width": (originalRect.width).toInt(),
        "height": (originalRect.height).toInt(),
        "factorX": 1,
        "factorY": 1,
      },
    };
  }

  bool get isInitialized => refPoint != null;

  @override
  String toString() {
    return 'Target{'
        'id: $targetId, '
        'name: $name, '
        'rect: $rect, '
        'isBasePoint: $basePoint, '
        'skipMeasurement: $skipMeasurement, '
        'heightDiff: $heightDiff, '
        'actualMeasureDistance: $actualMeasureDistance, '
        'refPoint: $refPoint,'
        'model: $targetModel,'
        'cameraId: $cameraId,'
        'roi: $roi,'
        'selfAdaption: $selfAdaption,'
        '}';
  }

    static Target fromJson(Map<String, dynamic> target) {
      Map<String, dynamic>? roiData = target['roi'] as Map<String, dynamic>?;
      var ratioX = 3840/640;
      var ratioY = 2160/480;


      if (roiData == null) {
        // 设置为640x480屏幕中间的150x150区域
        // 中心点：(320, 240)，左上角：(320-75=245, 240-75=165)
        roiData = {
          'x': 245, // 中心点x(320) - 宽度(150)/2 = 245
          'y': 165, // 中心点y(240) - 高度(150)/2 = 165
          'width': 150,
          'height': 150,
          'factorX': 1,
          'factorY': 1,
        };
      } else {
        // 将高分辨率坐标转换为低分辨率显示坐标
        roiData['x'] = (roiData['x'] / ratioX).toInt();
        roiData['y'] = (roiData['y'] / ratioY).toInt();
        roiData['width'] = (roiData['width'] / ratioX).toInt();
        roiData['height'] = (roiData['height'] / ratioY).toInt();
      }


      String id = target['targetId']?.toString() ?? target['id']?.toString() ?? '';
      String model = target['targetModel']?.toString() ?? target['model']?.toString() ?? '';

      return Target(
        targetId: id,
        name: target['name']?.toString() ?? '',
        rect: Rect.fromLTWH(
          roiData['x'].toDouble(),
          roiData['y'].toDouble(),
          roiData['width'].toDouble(),
          roiData['height'].toDouble(),
        ),
        basePoint: target['basePoint'] == true,
        skipMeasurement: target['skipMeasurement'] == true,
        heightDiff: target['heightDiff'] != null && target['heightDiff'] is num ? (target['heightDiff'] as num).toDouble() : null,
        actualMeasureDistance: target['actualMeasureDistance'] != null && target['actualMeasureDistance'] is num ? (target['actualMeasureDistance'] as num).toDouble() : null,
        refPoint: target['refPoint'] != null || target['refPoint'] != null
            ? PixelPosition.fromJson((target['refPoint'] ?? target['refPoint']) as Map<String, dynamic>)
            : null,
        targetModel: model,
        cameraId: target['cameraId'] is num ? (target['cameraId'] as num).toInt() : 0,
        selfAdaption: target['selfAdaption'] == true,
        wyCameraExposure: target['wyCameraExposure']?.toString() ?? 'Middle',
        roi: ROI.fromJson(roiData),
      );
    }
}
import 'dart:core';

import 'package:client/model/pixel_position.dart';
class Target {
  final String targetId;
  final int cameraId;
  final String name;
  final PixelPosition? refPoint;
  double actualMeasureDistance;
  double heightDiff;
  bool skipMeasurement;
  bool isBasePoint;

  Target({
    required this.targetId,
    required this.name,
    this.cameraId = 0,
    this.refPoint,
    this.actualMeasureDistance = 50.0,
    this.heightDiff = 0.0,
    this.skipMeasurement = false,
    this.isBasePoint = false,
  });

  Map<String, dynamic> toJson() => {
    'targetId': targetId,
    'cameraId': cameraId,
    'name': name,
    'refPoint': refPoint,
    'actualMeasureDistance': actualMeasureDistance,
    'heightDiff': heightDiff,
    'skipMeasurement': skipMeasurement,
    'isBasePoint': isBasePoint,

  };
}
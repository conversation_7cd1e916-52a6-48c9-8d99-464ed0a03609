import 'data_point.dart';

class DataSource {
  List<DataPoint> data = [];

  // 生成初始数据（模拟）
  void generateData() {
    final now = DateTime.now();
    data = List.generate(10, (index) {
      final time = now.add(Duration(minutes: index));
      final value = (index * 10 + (index % 3) * 5).toDouble(); // 示例数据
      return DataPoint(time, value);
    });
  }

  // 更新数据以模拟实时变化
  void updateData() {
    final now = DateTime.now();
    final newValue = (data.last.value + 1).toDouble(); // 简单递增
    data.add(DataPoint(now, newValue));
    if (data.length > 50) data.removeAt(0); // 限制数据点数量，避免内存占用过大
  }
}
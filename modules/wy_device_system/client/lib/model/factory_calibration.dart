import 'package:client/model/pixel_position.dart';
import 'roi.dart';
import 'package:json_annotation/json_annotation.dart';
part 'factory_calibration.g.dart';

@JsonSerializable(
    explicitToJson: true,
    includeIfNull: false,
    checked: true, // 这个参数很重要，启用详细的类型检查
    disallowUnrecognizedKeys: false
)
class FactoryCalibration{
  final int cameraId;
  final bool isCalibrated;
  final int ts;
  final String targetModel;
  final double actualMeasureDistance;
  final ROI roi;
  final PixelPosition3Mark? pixelPosition;
  final int focalLength;

  const FactoryCalibration({
    required this.cameraId,
    required this.isCalibrated,
    required this.ts,
    required this.targetModel,
    required this.actualMeasureDistance,
    required this.roi,
    this.pixelPosition,
    this.focalLength = 0,
  });


  factory FactoryCalibration.fromJson(Map<String, dynamic> json) =>
      _$FactoryCalibrationFromJson(json);

  Map<String, dynamic> toJson() => _$FactoryCalibrationToJson(this);

  Map<String, dynamic> toMap() {
    return {
      'cameraId': cameraId,
      'isCalibrated': isCalibrated,
      'ts': ts,
      'targetModel': targetModel,
      'actualMeasureDistance': actualMeasureDistance,
      'pixelPosition': pixelPosition?.toJson(),
      'roi': roi.toJson(),
      'focalLength': focalLength,
    };
  }


  copyWith({
    int? cameraId,
    bool? isCalibrated,
    int? ts,
    String? targetModel,
    double? actualMeasureDistance,
    ROI? roi,
    PixelPosition3Mark? pixelPosition,
    int? focalLength,
  }) {
    return FactoryCalibration(
      cameraId: cameraId ?? this.cameraId,
      isCalibrated: isCalibrated ?? this.isCalibrated,
      ts: ts ?? this.ts,
      targetModel: targetModel ?? this.targetModel,
      actualMeasureDistance: actualMeasureDistance ?? this.actualMeasureDistance,
      roi: roi ?? this.roi,
      pixelPosition: pixelPosition ?? this.pixelPosition,
      focalLength: focalLength ?? this.focalLength,
    );
  }

}


import 'package:json_annotation/json_annotation.dart';
part 'pixel_position.g.dart';


class PixelPosition {
  final double x;
  final double y;
  final double r;

  PixelPosition( {
    required this.x,
    required this.y,
    required this.r
  });

  factory PixelPosition.fromJson(Map<String, dynamic> json) {
    return PixelPosition(
      x: json['x'],
      y: json['y'],
      r: json['r']
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'x': x,
      'y': y,
      'r': r
    };
  }
}





@JsonSerializable(
    explicitToJson: true,
    includeIfNull: false,
    checked: true, // 这个参数很重要，启用详细的类型检查
    disallowUnrecognizedKeys: false
)
class PixelPosition3Mark {
  final double x1;
  final double y1;
  final double r1;

  final double x2;
  final double y2;
  final double r2;

  final double x3;
  final double y3;
  final double r3;

  PixelPosition3Mark( {
    required this.x1,
    required this.y1,
    required this.r1,
    required this.x2,
    required this.y2,
    required this.r2,
    required this.x3,
    required this.y3,
    required this.r3,
  });


factory PixelPosition3Mark.fromJson(Map<String, dynamic> json) {
  return _$PixelPosition3MarkFromJson(json);
}

Map<String, dynamic> toJson() {
  return _$PixelPosition3MarkToJson(this);
}
}
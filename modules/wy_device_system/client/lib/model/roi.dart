import 'dart:ui';

class ROI{

  final int x;
  final int y;
  final int width;
  final int height;
  final int factorX;
  final int factorY;
  final String name;

   const ROI({
    required this.x,
    required this.y,
    required this.width,
    required this.height,
    required this.factorX,
    required this.factorY,
    this.name = '',
  });




  factory ROI.fromJson(Map<String, dynamic> json) {
    return ROI(
      x: json['x'],
      y: json['y'],
      width: json['width'],
      height: json['height'],
      factorX: json['factorX'],
      factorY: json['factorY'],
    );
  }

  factory ROI.fromRect(Rect rect) {
    return ROI(
      x: rect.left.toInt(),
      y: rect.top.toInt(),
      width: rect.width.toInt(),
      height: rect.height.toInt(),
      factorX: 1,
      factorY: 1,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'x': x,
      'y': y,
      'width': width,
      'height': height,
      'factorX': factorX,
      'factorY': factorY,
    };
  }



  @override
  String toString() {
    return 'ROI(x: $x, y: $y, width: $width, height: $height, factorX: $factorX, factorY: $factorY)';
  }
}
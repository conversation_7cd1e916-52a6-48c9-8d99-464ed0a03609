// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'wy_device.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

WyDeviceAttribute _$WyDeviceAttributeFromJson(Map<String, dynamic> json) =>
    $checkedCreate(
      'WyDeviceAttribute',
      json,
      ($checkedConvert) {
        final val = WyDeviceAttribute(
          model: $checkedConvert('model', (v) => v as String? ?? ''),
          wyID: $checkedConvert('wyID', (v) => v as String? ?? ''),
          ccid: $checkedConvert('ccid', (v) => v as String? ?? ''),
          indicatorLight:
              $checkedConvert('indicatorLight', (v) => v as bool? ?? true),
          currentFwTitle:
              $checkedConvert('current_fw_title', (v) => v as String? ?? ''),
          currentFwVersion:
              $checkedConvert('current_fw_version', (v) => v as String? ?? ''),
          linkedCaptureRule: $checkedConvert('linkedCaptureRule',
              (v) => LinkedCaptureRule.fromJson(v as Map<String, dynamic>)),
          linkedCaptureType: $checkedConvert(
              'linkedCaptureType', (v) => v as String? ?? 'Disable'),
          linkedCaptureUrl:
              $checkedConvert('linkedCaptureUrl', (v) => v as String? ?? ''),
          maximumAdaptivePwm: $checkedConvert(
              'maximumAdaptivePwm', (v) => (v as num?)?.toInt() ?? 9999),
          minimumAdaptivePwm: $checkedConvert(
              'minimumAdaptivePwm', (v) => (v as num?)?.toInt() ?? 0),
          pwSelfAdaptive: false,
              // $checkedConvert('pwSelfAdaptive', (v) => v as bool? ?? false),
          reportEnvStatusInterval: $checkedConvert(
              'reportEnvStatusInterval', (v) => (v as num?)?.toInt() ?? 900),
          sigmaXDirection:
              $checkedConvert('sigmaXDirection', (v) => v as bool? ?? false),
          sigmaYDirection:
              $checkedConvert('sigmaYDirection', (v) => v as bool? ?? false),
          stableSample:
              $checkedConvert('stableSample', (v) => v as bool? ?? false),
          uploadTailorData:
              $checkedConvert('uploadTailorData', (v) => v as bool? ?? false),
          wyBrightnessThr: $checkedConvert(
              'wyBrightnessThr', (v) => (v as num?)?.toInt() ?? 225),
          wyCameraExposure: $checkedConvert(
              'wyCameraExposure', (v) => v as String? ?? 'Middle'),
          wyDeviceStatus: $checkedConvert(
              'wyDeviceStatus',
              (v) => v == null
                  ? WyDeviceStatus.idle
                  : WyDeviceAttribute._parseDeviceStatus(v)),
          wyEnableHeater:
              $checkedConvert('wyEnableHeater', (v) => v as bool? ?? false),
          wyMaxSampleFreq: $checkedConvert(
              'wyMaxSampleFreq', (v) => (v as num?)?.toDouble() ?? 1.0),
          wyMCUFirmwareV:
              $checkedConvert('wyMCUFirmwareV', (v) => v as String? ?? '0.0.0'),
          captureNum:
              $checkedConvert('captureNum', (v) => (v as num?)?.toInt() ?? 3),
          wyHeaterTemperatureRange: $checkedConvert(
              'wyHeaterTemperatureRange',
              (v) =>
                  (v as List<dynamic>?)
                      ?.map((e) => (e as num).toInt())
                      .toList() ??
                  [-25, 45]),
          sim: $checkedConvert('sim', (v) => v as String? ?? ''),
          filter: $checkedConvert('filter',
              (v) => Filter.fromJson(v as Map<String, dynamic>)),
          wyReportSelfAdaptiveInterval: $checkedConvert(
              'wyReportSelfAdaptiveInterval',
              (v) => (v as num?)?.toInt() ?? 60),
          pwm: $checkedConvert('pwm', (v) => (v as num?)?.toInt() ?? 2000),
          cameraFactoryCalibrations: $checkedConvert(
              'cameraFactoryCalibrations',
              (v) =>
                  (v as List<dynamic>?)
                      ?.map((e) => FactoryCalibration.fromJson(
                          e as Map<String, dynamic>))
                      .toList() ??
                  []),
        );
        return val;
      },
      fieldKeyMap: const {
        'currentFwTitle': 'current_fw_title',
        'currentFwVersion': 'current_fw_version'
      },
    );

Map<String, dynamic> _$WyDeviceAttributeToJson(WyDeviceAttribute instance) =>
    <String, dynamic>{
      'model': instance.model,
      'wyID': instance.wyID,
      'ccid': instance.ccid,
      'indicatorLight': instance.indicatorLight,
      'current_fw_title': instance.currentFwTitle,
      'current_fw_version': instance.currentFwVersion,
      'linkedCaptureRule': instance.linkedCaptureRule.toJson(),
      'linkedCaptureType': instance.linkedCaptureType,
      'linkedCaptureUrl': instance.linkedCaptureUrl,
      'captureNum': instance.captureNum,
      'maximumAdaptivePwm': instance.maximumAdaptivePwm,
      'minimumAdaptivePwm': instance.minimumAdaptivePwm,
      'pwSelfAdaptive': instance.pwSelfAdaptive,
      'reportEnvStatusInterval': instance.reportEnvStatusInterval,
      'sigmaXDirection': instance.sigmaXDirection,
      'sigmaYDirection': instance.sigmaYDirection,
      'stableSample': instance.stableSample,
      'uploadTailorData': instance.uploadTailorData,
      'wyBrightnessThr': instance.wyBrightnessThr,
      'wyCameraExposure': instance.wyCameraExposure,
      'wyDeviceStatus':
          WyDeviceAttribute._deviceStatusToJson(instance.wyDeviceStatus),
      'wyEnableHeater': instance.wyEnableHeater,
      'wyMaxSampleFreq': instance.wyMaxSampleFreq,
      'wyMCUFirmwareV': instance.wyMCUFirmwareV,
      'wyHeaterTemperatureRange': instance.wyHeaterTemperatureRange,
      'sim': instance.sim,
      'filter': instance.filter.toJson(),
      'wyReportSelfAdaptiveInterval': instance.wyReportSelfAdaptiveInterval,
      'pwm': instance.pwm,
      if (instance.cameraFactoryCalibrations?.map((e) => e.toJson()).toList()
          case final value?)
        'cameraFactoryCalibrations': value,
    };

Filter _$filterFromJson(Map<String, dynamic> json) => $checkedCreate(
      'filter',
      json,
      ($checkedConvert) {
        final val = Filter(
          type: $checkedConvert('type', (v) => v as String? ?? 'none'),
          args: $checkedConvert(
              'args', (v) => Args.fromJson(v as Map<String, dynamic>)),
        );
        return val;
      },
    );

Map<String, dynamic> _$filterToJson(Filter instance) =>
    <String, dynamic>{
      'type': instance.type,
      'args': instance.args.toJson(),
    };

Args _$ArgsFromJson(Map<String, dynamic> json) => $checkedCreate(
      'Args',
      json,
      ($checkedConvert) {
        final val = Args(
          minor: $checkedConvert('minor', (v) => (v as num?)?.toInt() ?? 6),
          major: $checkedConvert('major', (v) => (v as num?)?.toInt() ?? 10),
        );
        return val;
      },
    );

Map<String, dynamic> _$ArgsToJson(Args instance) => <String, dynamic>{
      'minor': instance.minor,
      'major': instance.major,
    };

LinkedCaptureRule _$LinkedCaptureRuleFromJson(Map<String, dynamic> json) =>
    $checkedCreate(
      'LinkedCaptureRule',
      json,
      ($checkedConvert) {
        final val = LinkedCaptureRule(
          sigmaXGT0: $checkedConvert('sigmaXGT0', (v) => (v as num).toInt()),
          sigmaXLT0: $checkedConvert('sigmaXLT0', (v) => (v as num).toInt()),
          sigmaYGT0: $checkedConvert('sigmaYGT0', (v) => (v as num).toInt()),
          sigmaYLT0: $checkedConvert('sigmaYLT0', (v) => (v as num).toInt()),
        );
        return val;
      },
    );

Map<String, dynamic> _$LinkedCaptureRuleToJson(LinkedCaptureRule instance) =>
    <String, dynamic>{
      'sigmaXGT0': instance.sigmaXGT0,
      'sigmaXLT0': instance.sigmaXLT0,
      'sigmaYGT0': instance.sigmaYGT0,
      'sigmaYLT0': instance.sigmaYLT0,
    };

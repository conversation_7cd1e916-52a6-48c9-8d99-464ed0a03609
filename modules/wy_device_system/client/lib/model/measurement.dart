import '../view/components/charts/model.dart';

class Measurement {
  final DateTime timestamp;
  final double value;
  final String type;
  final String? targetId;

  Measurement({
    required this.timestamp,
    required this.value,
    required this.type,
    this.targetId,
  });

  Map<String, dynamic> toMap() {
    return {
      'value': value,
      'timestamp': timestamp,
    };
  }

  factory Measurement.fromJson(Map<String, dynamic> json) {
    return Measurement(
      timestamp: DateTime.parse(json['timestamp']),
      value: json['value'].toDouble(),
      type: json['type'],
      targetId: json['targetId'],
    );
  }

  MeasurementData toUiModel() {
    return MeasurementData(timestamp, value);
  }
}
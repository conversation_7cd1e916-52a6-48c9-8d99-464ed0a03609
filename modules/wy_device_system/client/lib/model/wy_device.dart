import 'package:equatable/equatable.dart';
import 'package:json_annotation/json_annotation.dart';

import 'factory_calibration.dart';

part 'wy_device.g.dart';

@JsonSerializable(
    explicitToJson: true,
    includeIfNull: false,
    checked: true, // 这个参数很重要，启用详细的类型检查
    disallowUnrecognizedKeys: false
)
class WyDeviceAttribute {

  @JsonKey(defaultValue:'')
  final String model; // 设备型号

  @JsonKey(defaultValue:'')
  final String wyID; // 设备ID

  @JsonKey(defaultValue: '')
  final String ccid; // SIM卡号

  @JsonKey(defaultValue: true)
  final bool indicatorLight; // 指示灯状态


  @Json<PERSON>ey(name: 'current_fw_title',
      defaultValue: '')
  final String currentFwTitle;// 当前固件标题

  @JsonKey(name: 'current_fw_version',
      defaultValue: '')
  final String currentFwVersion;

  final LinkedCaptureRule linkedCaptureRule; // 关联的捕获规则

  @Json<PERSON>ey(defaultValue: 'Disable')
  final String linkedCaptureType; // 关联的捕获类型

  @JsonKey(defaultValue: '')
  final String linkedCaptureUrl; // 关联的捕获URL
  @JsonKey(defaultValue: 3)
  final int captureNum; // 捕获次数

  @JsonKey(defaultValue: 9999)
  final int maximumAdaptivePwm; // 最大自适应PWM
  @JsonKey(defaultValue: 0)
  final int minimumAdaptivePwm; // 最小自适应PWM
  @JsonKey(defaultValue: false)
  final bool pwSelfAdaptive; // PWM是否自适应（反序列化时忽略）// PWM是否自适应
  @JsonKey(defaultValue: 900)
  final int reportEnvStatusInterval; // 环境状态报告间隔
  @JsonKey(defaultValue: false)
  final bool sigmaXDirection; // X方向Sigma
  @JsonKey(defaultValue: false)
  final bool sigmaYDirection; // Y方向Sigma
  @JsonKey(defaultValue: false)
  final bool stableSample; // 是否稳定采样
  @JsonKey(defaultValue: false)
  final bool uploadTailorData; // 是否上传裁剪数据
  @JsonKey(defaultValue: 225)
  final int wyBrightnessThr; // 亮度阈值
  @JsonKey(defaultValue: 'Middle')
  final String wyCameraExposure; // 相机曝光设置

  @JsonKey(
      fromJson: _parseDeviceStatus,
      toJson: _deviceStatusToJson
  )
  final WyDeviceStatus wyDeviceStatus; // 设备状态
  @JsonKey(defaultValue: false)
  final bool wyEnableHeater; // 是否启用加热器
  @JsonKey(defaultValue: 1.0)
  final double wyMaxSampleFreq; // 最大采样频率

  @JsonKey(defaultValue: '0.0.0')
  final String wyMCUFirmwareV; // MCU固件版本
  @JsonKey(defaultValue: const [-25,45])
  final List<int> wyHeaterTemperatureRange; // 加热器温度范围

  @JsonKey(defaultValue: '')
  final String sim; // SIM卡信息
  final Filter filter; // 过滤器类型
  @JsonKey(defaultValue: 60)
  final int wyReportSelfAdaptiveInterval; // 自适应上报间隔

  @JsonKey(defaultValue: 2000)
  final int pwm; // 补光灯PWM

  // 精度标定
  @JsonKey(defaultValue:  const [])
  final List<FactoryCalibration>? cameraFactoryCalibrations;

  WyDeviceAttribute({
    required this.model,
    required this.wyID,
    this.ccid = '',
    required this.indicatorLight,
    required this.currentFwTitle,
    required this.currentFwVersion,
    required this.linkedCaptureRule,
    required this.linkedCaptureType,
    required this.linkedCaptureUrl,
    required this.maximumAdaptivePwm,
    required this.minimumAdaptivePwm,
    required this.pwSelfAdaptive,
    required this.reportEnvStatusInterval,
    required this.sigmaXDirection,
    required this.sigmaYDirection,
    required this.stableSample,
    required this.uploadTailorData,
    required this.wyBrightnessThr,
    required this.wyCameraExposure,
    this.wyDeviceStatus = WyDeviceStatus.idle,
    required this.wyEnableHeater,
    required this.wyMaxSampleFreq,
    required this.wyMCUFirmwareV,
    required this.captureNum,
    required this.wyHeaterTemperatureRange,
    required this.sim,
    required this.filter,
    required this.wyReportSelfAdaptiveInterval,
    this.pwm = 2500,
    this.cameraFactoryCalibrations = const [],
  });

  // 从JSON创建对象
  factory WyDeviceAttribute.fromJson(Map<String, dynamic> json) =>
      _$WyDeviceAttributeFromJson(json);

  // 转换为JSON
  Map<String, dynamic> toJson() => _$WyDeviceAttributeToJson(this);

  // 设备状态解析辅助方法
  static WyDeviceStatus _parseDeviceStatus(dynamic statusValue) {
    if (statusValue == null) return WyDeviceStatus.idle;

    if (statusValue is int) {
      switch (statusValue) {
        case 0: return WyDeviceStatus.idle;
        case 1: return WyDeviceStatus.initializing;
        case 2: return WyDeviceStatus.testing;
        case 3: return WyDeviceStatus.measuring;
        default: return WyDeviceStatus.idle;
      }
    } else if (statusValue is String) {
      try {
        return WyDeviceStatus.fromString(statusValue);
      } catch (_) {
        return WyDeviceStatus.idle;
      }
    }

    return WyDeviceStatus.idle;
  }

  static String _deviceStatusToJson(WyDeviceStatus status) => status.toJson();

  // 复制方法
  WyDeviceAttribute copyWith({
    String? model,
    String? wyID,
    String? ccid,
    bool? indicatorLight,
    String? currentFwTitle,
    String? currentFwVersion,
    LinkedCaptureRule? linkedCaptureRule,
    String? linkedCaptureType,
    String? linkedCaptureUrl,
    int? maximumAdaptivePwm,
    int? minimumAdaptivePwm,
    bool? pwSelfAdaptive,
    int? reportEnvStatusInterval,
    bool? sigmaXDirection,
    bool? sigmaYDirection,
    bool? stableSample,
    bool? uploadTailorData,
    int? wyBrightnessThr,
    String? wyCameraExposure,
    WyDeviceStatus? wyDeviceStatus,
    bool? wyEnableHeater,
    double? wyMaxSampleFreq,
    String? wyMCUFirmwareV,
    int? captureNum,
    List<int>? wyHeaterTemperatureRange,
    String? sim,
    Filter? filter,
    int? wyReportSelfAdaptiveInterval,
    int? focalLength,
    int? pwm,
    List<FactoryCalibration>? cameraFactoryCalibrations,
  }) {
    return WyDeviceAttribute(
      model: model ?? this.model,
      wyID: wyID ?? this.wyID,
      ccid: ccid ?? this.ccid,
      indicatorLight: indicatorLight ?? this.indicatorLight,
      currentFwTitle: currentFwTitle ?? this.currentFwTitle,
      currentFwVersion: currentFwVersion ?? this.currentFwVersion,
      linkedCaptureRule: linkedCaptureRule ?? this.linkedCaptureRule,
      linkedCaptureType: linkedCaptureType ?? this.linkedCaptureType,
      linkedCaptureUrl: linkedCaptureUrl ?? this.linkedCaptureUrl,
      maximumAdaptivePwm: maximumAdaptivePwm ?? this.maximumAdaptivePwm,
      minimumAdaptivePwm: minimumAdaptivePwm ?? this.minimumAdaptivePwm,
      pwSelfAdaptive: pwSelfAdaptive ?? this.pwSelfAdaptive,
      reportEnvStatusInterval: reportEnvStatusInterval ?? this.reportEnvStatusInterval,
      sigmaXDirection: sigmaXDirection ?? this.sigmaXDirection,
      sigmaYDirection: sigmaYDirection ?? this.sigmaYDirection,
      stableSample: stableSample ?? this.stableSample,
      uploadTailorData: uploadTailorData ?? this.uploadTailorData,
      wyBrightnessThr: wyBrightnessThr ?? this.wyBrightnessThr,
      wyCameraExposure: wyCameraExposure ?? this.wyCameraExposure,
      wyDeviceStatus: wyDeviceStatus ?? this.wyDeviceStatus,
      wyEnableHeater: wyEnableHeater ?? this.wyEnableHeater,
      wyMaxSampleFreq: wyMaxSampleFreq ?? this.wyMaxSampleFreq,
      wyMCUFirmwareV: wyMCUFirmwareV ?? this.wyMCUFirmwareV,
      captureNum: captureNum ?? this.captureNum,
      wyHeaterTemperatureRange: wyHeaterTemperatureRange ?? this.wyHeaterTemperatureRange,
      sim: sim ?? this.sim,
      filter: filter ?? this.filter,
      wyReportSelfAdaptiveInterval: wyReportSelfAdaptiveInterval ?? this.wyReportSelfAdaptiveInterval,
      pwm: pwm ?? this.pwm,
      cameraFactoryCalibrations: cameraFactoryCalibrations ?? this.cameraFactoryCalibrations,
    );
  }

  static WyDeviceStatus parseDeviceStatus(dynamic statusValue) {
    if (statusValue == null) return WyDeviceStatus.idle;

    if (statusValue is int) {
      switch (statusValue) {
        case 0: return WyDeviceStatus.initializing;
        case 1: return WyDeviceStatus.measuring;
        case 2: return WyDeviceStatus.idle;
        case 3: return WyDeviceStatus.testing;
        case 4: return WyDeviceStatus.accuracyCalibration;
        default: return WyDeviceStatus.idle;
      }
    } else if (statusValue is String) {
      switch (statusValue) {
        case '0': return WyDeviceStatus.initializing;
        case '1': return WyDeviceStatus.measuring;
        case '2': return WyDeviceStatus.idle;
        case '3': return WyDeviceStatus.testing;
        case '4': return WyDeviceStatus.accuracyCalibration;
        default:
          try {
            // 尝试枚举名称转换
            return WyDeviceStatus.values.firstWhere(
                  (e) => e.toString().split('.').last.toLowerCase() == statusValue.toLowerCase(),
              orElse: () => WyDeviceStatus.idle,
            );
          } catch (_) {
            return WyDeviceStatus.idle;
          }
      }
    }

    return WyDeviceStatus.idle;
  }
}


@JsonSerializable(
    explicitToJson: true,
    includeIfNull: false,
    checked: true,
    disallowUnrecognizedKeys: false
)
class Filter {
  @JsonKey(defaultValue: 'none')
  final String type;
  final Args args;

  const Filter({required this.type, required this.args});

  factory Filter.fromJson(Map<String, dynamic> json) =>
      _$filterFromJson(json);

  Map<String, dynamic> toJson() => _$filterToJson(this);
}

@JsonSerializable(
    explicitToJson: true,
    includeIfNull: false,
    checked: true,
    disallowUnrecognizedKeys: false
)
class Args {
  @JsonKey(defaultValue: 6)
  final int minor;
  @JsonKey(defaultValue: 10)
  final int major;

  const Args({
    required this.minor,
    required this.major,
  });

  factory Args.fromJson(Map<String, dynamic> json) => _$ArgsFromJson(json);
  Map<String, dynamic> toJson() => _$ArgsToJson(this);
}

@JsonSerializable(
    explicitToJson: true,
    includeIfNull: false,
    checked: true,
    disallowUnrecognizedKeys: false
)
class LinkedCaptureRule {
  final int sigmaXGT0;
  final int sigmaXLT0;
  final int sigmaYGT0;
  final int sigmaYLT0;

  const LinkedCaptureRule({
    required this.sigmaXGT0,
    required this.sigmaXLT0,
    required this.sigmaYGT0,
    required this.sigmaYLT0,
  });

  factory LinkedCaptureRule.fromJson(Map<String, dynamic> json) =>
      _$LinkedCaptureRuleFromJson(json);

  Map<String, dynamic> toJson() => _$LinkedCaptureRuleToJson(this);
}

enum WyDeviceStatus {
  idle,
  initializing,
  measuring,
  testing,
  accuracyCalibration;

  String toJson() => name;

  static WyDeviceStatus fromString(String status) {
    switch (status) {
      case '0':
        return WyDeviceStatus.initializing;
      case '1':
        return WyDeviceStatus.measuring;
      case '2':
        return WyDeviceStatus.idle;
      case '3':
        return WyDeviceStatus.testing;
      case '4':
        return WyDeviceStatus.accuracyCalibration;
      default:
        return WyDeviceStatus.idle;
    }
  }


  static WyDeviceStatus parseDeviceStatus(dynamic statusValue) {
    // 直接在枚举中实现解析逻辑，而不是调用 WyDeviceAttribute 中的方法
    if (statusValue == null) return WyDeviceStatus.idle;

    if (statusValue is int) {
      switch (statusValue) {
        case 0: return WyDeviceStatus.initializing;
        case 1: return WyDeviceStatus.measuring;
        case 2: return WyDeviceStatus.idle;
        case 3: return WyDeviceStatus.testing;
        case 4: return WyDeviceStatus.accuracyCalibration;
        default: return WyDeviceStatus.idle;
      }
    } else if (statusValue is String) {
      try {
        return WyDeviceStatus.fromString(statusValue);
      } catch (_) {
        return WyDeviceStatus.idle;
      }
    }

    return WyDeviceStatus.idle;
  }
}
